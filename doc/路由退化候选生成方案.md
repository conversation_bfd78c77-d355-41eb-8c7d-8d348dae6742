# 路由退化候选生成方案（多维、多值、可配置）

## 1. 背景与问题
- 现状：`degradeChain(routeCode)` 采用“去尾降级”：car_owner|su7|xxx → car_owner|su7 → car_owner → all。
- 问题：当用户在某维度上存在“多值”时（例如 CarModelEnum={su7Ultra, su7}），理想顺序应先尝试“同层可替代值”（su7Ultra → su7），再降维，而不是直接退到 car_owner。
- 目标：生成一条“由近及远”的候选列表，优先命中更贴近用户的组合；并保持对现网零侵入（未配置时沿用旧逻辑）。

## 2. 方案概述
- 抽象“候选生成器 RouteCandidateBuilder”：
  - 输入：pageId、RouteContext（UserStrategyEnum、CarModelEnum 多值支持）、该页可用的 routeCodes（availableRouteCodes）。
  - 配置：YAML 中声明“同层替代优先序/别名”和“降维顺序”。
  - 输出：候选 routeCode 列表（去重保序），顺序为“同层替代 → 降维 → all”。
- 接入点：
  - 装配层：PageResponseBuilder 按候选顺序选择最近可用的 group（car_owner|su7 不存在时优先 car_owner）。
  - 读路径：SitePageAssemblyService 逐候选生成组合键，并为每个候选用 CachePolicyResolver 决策策略后读缓存。
  - 预载（可选增强）：若已知可用 routeCodes，可直接用“逻辑 routeCode → 物理 group”映射构建，减少读时退化。

## 3. YAML 配置（轻量，非必要）
- 未配置时：仅执行“去尾降级”（保持现有行为）。
- 配置示例：
```
site:
  route:
    degrade:
      # 维度降级顺序（右端优先降维）
      dimension_order: [UserStrategyEnum, CarModelEnum]
      # 同层优先序与别名
      car_model:
        # 越靠前越优先（用于构造同层尝试的顺序）
        order: [su7Ultra, su7Pro, su7, su]
        # 等价/备用集合（先试原值，再试别名）
        alias:
          su7Ultra: [su7]
          su7Pro:   [su7]
```
- 解释：
  - 同层替代：在不降维的前提下，对“用户持有的该维度多值 + 该值的 alias + order 中其他值”，按先后顺序生成候选（仅保留页面 availableRouteCodes 中存在的）。
  - 降维：每次去掉 `dimension_order` 中最右侧维度，再重复“同层替代”，直到仅剩最左维；最后补 all。

## 4. 候选生成算法
- 输入：
  - pageId
  - RouteContext（userStrategy：单值；carModel：多值可选）
  - availableRouteCodes（该页支持的全部 routeCode；从 pageConfigModel.groupConfig.strategy 汇总）
  - DegradeConfig（如上 YAML）
- 输出：候选 routeCode 列表（List<String>）
- 步骤（伪代码）：
```
dims = cfg.dimension_order
level = full dims
candidates = []
while level 非空：
  # 构造“同层”所有组合（含多值 + alias + order）
  for comb in sameLevelPermutations(ctx, level, cfg):
    rc = joinByPipe(comb)
    if availableRouteCodes.contains(rc) and rc not in candidates:
      candidates.add(rc)
  level = level 去掉最右维
# 最后 all
if 'all' not in candidates: candidates.add('all')
return candidates
```
- 复杂度：受控于“同层枚举度”（一般仅车系/车型，别名与 order 规模小），且对 availableRouteCodes 做过滤避免无效候选。

## 5. 与现有系统的整合
- 装配层（PageResponseBuilder）：
  - 现状：精确匹配或“去尾降级”匹配。
  - 改造：使用 RouteCandidateBuilder 生成候选；按顺序找第一个存在于 groupConfig.strategy 的分组，取其模块顺序。
- 读路径（SitePageAssemblyService）：
  - 现状：对退化链 candidates 逐个生成组合键，用 CachePolicyResolver（pageId + cand）解析策略后读缓存。
  - 改造：将退化链 candidates 的生成替换为 RouteCandidateBuilder 的候选序列（若 availableRouteCodes 无法在读路径获取，则退回“仅做去尾降级 + alias”）。
- 预载（可选）：
  - 若使用注册表或聚合时可收集全页可用 routeCode，则可按“逻辑 routeCode（键）→ 物理 group（装配）”构建，提升直命中率；否则保持现状。
- 策略决策：
  - 不变：对每个候选 `cand` 使用 `CachePolicyResolver.resolve(pageId, cand)`，与预载一致。

## 6. 回退策略与默认行为
- 无 YAML 配置时：仅执行“去尾降级”，与现网保持一致。
- 获取不到 availableRouteCodes（如读路径无法拿到 pageConfigModel）时：同层仅按 alias + order 构造，仍有限放大；再降维。
- 任何阶段异常：记录日志并退回到“去尾降级 + all”。

## 7. 监控与调试
- 监控建议：
  - `route_candidate_build_total{pageId}`：候选生成次数/异常数
  - `route_read_fallback_total{pageId,from,to}`：读路径退化命中
  - `route_group_fallback_total{pageId,from,to}`：装配退化命中
- Debug 建议：
  - 暴露 `/debug/route/candidates?pageId=..&user=..&car_model=..` 返回候选数组（仅用于验证，后续下线）。

## 8. 实施清单（最小改动）
1) 新增 `DegradeConfigProperties`（绑定 `site.route.degrade.*`）。
2) 新增 `RouteCandidateBuilder`：从 RouteContext + availableRouteCodes + 配置生成候选列表；内部封装 alias/order/降维逻辑。
3) PageResponseBuilder：改为用 `RouteCandidateBuilder` 生成候选，再选最近分组。
4) SitePageAssemblyService：改为用 `RouteCandidateBuilder` 生成候选，逐个候选计算策略并读取缓存。
5) （可选）Debug 接口与埋点。

## 9. 示例
- 用户：UserStrategyEnum=car_owner，CarModelEnum={su7Ultra, su7}
- 页面：availableRouteCodes={car_owner|su7, car_owner, all}
- YAML：car_model.order=[su7Ultra, su7]，alias.su7Ultra=[su7]
- 候选：
  - 同层：car_owner|su7Ultra（不可用，跳）、car_owner|su7（可用）
  - 降维：car_owner（可用）
  - all（兜底）
- 最终：`[car_owner|su7, car_owner, all]`

## 10. 风险与边界
- 配置漂移：同层 order/alias 与实际页面支持不一致时，候选会自动被 availableRouteCodes 过滤；仍然安全。
- 多维扩展：新增维度仅需在 YAML 增加对应段，Builder 中读取后加入 sameLevelPermutations 即可。

---

附：默认去尾降级（兼容模式）
- 当没有 YAML 配置、也不提供 availableRouteCodes 时，Builder 退化为：
  - `routeCode` → 逐次 `去掉末尾一段` → `all`。
- 已实现的 `RouteCodeUtil.degradeChain` 可作为该兼容实现的基础。

