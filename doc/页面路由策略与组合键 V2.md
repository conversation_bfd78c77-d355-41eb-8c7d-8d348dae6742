# 页面路由策略与组合键 V2（人群 + 车型，多维路由，零迁移优先）

## 1. 目标与范围
- 将现有单一“人群策略 strategy”升级为“路由码 routeCode”，支持多维条件（人群 + 车型 等）。
- 优先采用“零迁移（Phase 1）”方案：不新增表、不改现有表结构，上线不影响任何未配置页面。
- 路由码用于：
  - 决定页面模块分组（组装顺序/可见模块）
  - 参与组合键 combinationKey 生成（缓存命中维度）
  - 解析缓存策略（按页面路由元数据的 policy 决策）
- 向后兼容：未配置路由的页面，routeCode 自动回退为原有人群策略值（如 all/car_owner）。

阶段划分：
- Phase 1（推荐，立即落地，零迁移）：仅代码路由解析 + 复用现有配置；不引入外部存储。
- Phase 2A（推荐，零迁移，配置化）：路由定义改由 Nacos 托管（pages-only），自动推送到内存注册表（不落库）。

## 2. 数据模型与配置

本节先给出 Phase 1（零迁移），随后给出 Phase 2A（Nacos 配置，pages-only）。

### 2.1 Phase 1（零迁移，立即可用）
- 复用现表：`site_page_module_strategy`，字段 `strategy` 的语义升级为 routeCode（不改结构）。
- 复用页面配置：`PageConfigModel.groupConfig[*].strategy` 即一组可用的 routeCode 列表（预载与读路径以此为准）。
  - 命名规范建议（RouteCode 格式见下文）：
    - 人群 + 车型：`car_owner|su7` / `car_owner|ultra` 等；
    - 仅人群：`car_owner`；兜底：`all`。

路由码生效规则：
- Phase 1：以页面配置为准。仅当某个 routeCode 已配置在页面的 `groupConfig` 中时参与构建/命中；否则按第 3 节的候选顺序回退到更粗粒度（如 `audience` → `all`）。
- Phase 2A（Nacos）：以 Nacos 页面路由清单为准。预载与读路径按 Nacos 中 `pages[pageId]` 的 routeCode 列表工作；若某 routeCode 在页面的 groupConfig 中没有专属模块顺序，则在装配阶段回退使用 `all` 的模块顺序（或按候选链 `audience` → `all` 回退）。

### 2.2 Phase 2A（Nacos 配置，零迁移，推荐，pages-only）
- 不使用数据库，将路由定义完全托管到 Nacos，应用通过监听配置变更实现内存注册表的自动热更新。
- 配置项（示例）：
```
site:
  route:
    registry:
      enabled: true
      source: nacos             # nacos | db | none
      refresh-interval-seconds: 0   # 0 表示仅靠 Nacos 监听；>0 可开启定时兜底刷新
      nacos:
        group: micar-site
        dataId: route_registry.json    # 建议单一 DataId 原子替换
        listenerDebounceMs: 300        # 合并频繁变更
        timeoutMs: 1500
      fallback:
        phase1OnError: true            # 解析失败/超时自动回退 Phase 1
```

- Nacos 配置内容（单 DataId，原子替换，建议 < 128KB；仅承载“路由元数据”，pages-only）：
  - 仅包含与路由判定相关的元信息：`routeCode`、`conditions`、`priority`、`isDefault`、`enabled`、`policy`、`version`。
  - 不包含组件列表、模板、模块配置、数据提供者等业务内容（这些仍由数据库与页面配置系统承载）。
```
{
  "pages": {
    "explore": [
      {
        "routeCode": "car_owner|su7",
        "priority": 10,
        "isDefault": false,
        "enabled": true,
        "policy": "REDIS_ONLY",
        "conditions": {"matchAll": true, "rules": [
          {"dim": "UserStrategyEnum",  "op": "in", "values": ["car_owner"]},
          {"dim": "CarModelEnum",      "op": "eq", "value": "su7"}
        ]}
      },
      {
        "routeCode": "all",
        "priority": 999,
        "isDefault": true,
        "enabled": true,
        "policy": "MYSQL_BROADCAST_MEMORY",
        "conditions": {"matchAll": true, "rules": []}
      }
    ]
  },
  "version": "2025-01-01T00:00:00Z"
}
```

- 加载与热更策略：
  - 应用启动：一次性拉取 DataId 并构建快照；失败根据 `fallback.phase1OnError` 回退 Phase 1。
  - 配置变更：Nacos Listener 推送 → 去抖（listenerDebounceMs）→ 解析校验 → 条件预编译 → 原子替换内存注册表；失败保留旧快照并告警。
  - 读路径：建议回归 DB 策略（site_page_module_strategy.cache_policy），可不启用注册表。
  - 预载：可调用注册表的 `listRouteCodesForPage(pageId)` 作为可构建集合；若运行在 Phase 1，可继续仅以页面 groupConfig 为准（pages-only 推荐使用注册表）。

- 配置治理：
  - 单 DataId 原子替换，避免 “codes 与 pages 跨 DataId 不一致”的窗口期；如需拆分 DataId，需加入 `version/epoch` 对齐或合并刷新逻辑。
  - 提供 Debug 接口：dump 当前快照、按上下文试算匹配；提供指标：加载耗时/失败、快照大小、命中率、降级次数。

<!-- DB 相关实现（Phase 2B）已移除，统一采用 Nacos pages-only 方案。 -->

## 3. 路由解析算法（RouteResolver）

### 3.1 Phase 1（零迁移，默认）
- 输入：`pageId`、`userId`、`queryParams`。
- 获取 UserStrategyEnum：调用 `UserStrategyService.determineUserStrategyWithPageCheck(pageId, queryParams, userId)`（现有实现）。
- 获取 CarModelEnum：统一从 `UserStrategyService` 获取；当前阶段“可先 mock”，由 `RouteResolver` 内部从 `queryParams.get("car_model")` 兜底读取；后续由 `UserStrategyService` 提供统一方法（对外无改动）。
- 生成候选 routeCode（按优先级）：
  1) `UserStrategyEnum|CarModelEnum`（如 `car_owner|su7`），仅当 `CarModelEnum` 非空时生成；
  2) `UserStrategyEnum`（如 `car_owner`）；
  3) `all`（兜底）。
- 命中策略：在页面配置的 `groupConfig[*].strategy` 集合中，按上述顺序选择第一个存在的 routeCode；若均不存在，返回 `all`。

说明：
- Phase 1：预载构建以 `groupConfig[*].strategy` 为真值集合，避免构建无配置的 routeCode。
- Phase 2A（Nacos）：可直接以 `pages[pageId]` 中的 routeCode 列表作为构建集合；若某 routeCode 在页面 groupConfig 中未配置专属模块顺序，则装配阶段自动回退到 `all`（或 `UserStrategyEnum` → `all`）。
- 未配置车型专属 routeCode 的页面，最终 routeCode 等于原 `UserStrategyEnum`，行为与线上完全一致（零侵入）。

### 3.2 路由过程（pages-only，详细说明）
- 构造 RouteContext：
  - `UserStrategyEnum`：调用 `UserStrategyService.determineUserStrategyWithPageCheck(pageId, queryParams, userId)`。
  - `CarModelEnum`：优先从 `UserStrategyService` 获取；暂未提供时，可在 Resolver 内从 `queryParams.get("car_model")` 兜底解析（代码隔离，后续可替换为服务输出）。
  - 其他维度：按需扩展（保持“维度→提取器”映射）。
  - 读取页面规则：从 `RouteDefinitionRegistry` 获取 `pages[pageId]` 的 `routeCode→policy` 列表。
  - 匹配：不使用条件与优先级；按“维度退化链”与列表做等值匹配，命中即返回 `{routeCode, policy}`；推荐每页包含 `all` 兜底项。
- 产出与后续：
- 组合键：`ExperimentUtils.generateCombinationKey(pageId, routeCode, expList)`。
- 读策略：依据 `policy` 走内存/Redis。
- 装配：若页面未配置该 routeCode 的专属模块顺序，装配阶段回退使用 `all` 的模块顺序（或“UserStrategyEnum → all”）。
  - 伪代码：
    - `rules = registry.getPageRules(pageId)`
    - `ctx = buildContext(pageId, userId, queryParams)`
    - `cands = degradeChain(ctx)`
    - `for cand in cands: if rules.contains(cand): return decision(cand, rules[cand].policy)`
    - `return null`（不建议；规范要求每页至少包含 `all`）

### 3.3 路由退化与映射（解决“car_owner|su7 不存在时退到 car_owner”）
- 目标：当命中 `routeCode=car_owner|su7`，但页面只配置了 `car_owner` 分组时，优先退化到 `car_owner`，而不是直接退到 `all`。
- 核心概念：
  - 逻辑 routeCode：由路由规则匹配得到的最细粒度路由码（如 `car_owner|su7`）。
  - 物理 group：页面 `groupConfig[*].strategy` 中实际存在的分组键（如 `car_owner`、`all`）。
  - 退化链 degradeChain(routeCode)：按维度从细到粗生成候选列表，例如 `car_owner|su7 → [car_owner|su7, car_owner, all]`（维度顺序固定为 `UserStrategyEnum`、`CarModelEnum`）。

- 落点 A（装配阶段，推荐，默认开启）：
  - 在 `PageResponseBuilder#filterAndApplyGroupComponents` 中，引入“退化匹配”。
  - 对传入的 `routeCode` 计算 `degradeChain(routeCode)`，在 `groupConfig` 中按顺序寻找第一个存在的分组：
    - 命中 `car_owner|su7` → 用其组件顺序
    - 否则命中 `car_owner` → 用其组件顺序
    - 否则命中 `all` → 用其组件顺序
  - 组合键不变（仍用逻辑 routeCode 构建）；仅装配采用“最近可用的物理 group”。

- 落点 B（读缓存阶段，链式兜底，可选）
  - 当按逻辑 routeCode 未命中缓存时（例如未构建该组合）：
    - 生成候选 `routeCode` 列表：`[car_owner|su7, car_owner, all]`；
    - 依次尝试读取 `V2_{pageId}|{cand}|(exp...)`；命中任意一个即返回；
    - 记录监控：`route_read_fallback_total{from,to,pageId}`；默认开启可观测性后再按需打开链式读配置。
  - 可通过开关启用：`site.route.read-fallback-enabled=true|false`，默认建议开启。

- 落点 C（预载/构建阶段，提升命中率，可选，注册表开启时生效）
  - 当 `site.route.registry.enabled=true`：
    - 以注册表 `pages[pageId]` 的“逻辑 routeCode 列表”为 `targetGroupKeys`；
    - 对每个逻辑 `routeCode`，计算 `degradeChain(routeCode)`，在页面 `groupConfig` 中选择“最近可用的物理 group”；
    - 构建策略：
      - 组合键使用逻辑 `routeCode`（例如 `car_owner|su7`）；
      - 组件顺序来自物理 group（例如 `car_owner`）；
    - 这样读路径就无需链式兜底即可直命中（更高缓存命中率）。
  - 未开启注册表（Phase 1）：维持现状（仅构建 `groupConfig` 中声明的分组），读路径可依赖“链式兜底”。

- 策略与监控：
  - 缓存策略 `policy` 默认仍以“逻辑 routeCode 的页面规则”为准，不随物理 group 退化；可加开关 `site.route.policy.follow-physical=false|true` 以支持策略随物理 group 退化（默认 false）。
  - 监控建议：
    - `route_group_fallback_total{from,to,pageId}`：装配退化计数；
    - `route_read_fallback_total{from,to,pageId}`：读路径链式兜底命中计数；
    - 预载“逻辑→物理映射”覆盖率统计。

- 退化链算法（从最细到最粗）
  - 输入：`routeCode`（如 `car_owner|su7|xxx`）
  - 过程：按 `|` 切分，从右往左每次去掉一个末尾维度，最后补 `all`；去重保序
  - 示例：
    - `car_owner|su7|xxx` → `[car_owner|su7|xxx, car_owner|su7, car_owner, all]`
    - `car_owner|xxx|su7` → `[car_owner|xxx|su7, car_owner|xxx, car_owner, all]`
    - `car_owner` → `[car_owner, all]`

## 4. 组合键、缓存键与缓存策略
- 组合键仍由 `ExperimentUtils.generateCombinationKey(pageId, routeCode, expList)` 生成。
- 历史快照键 `HistoricalSnapshotCacheKeyGenerator` 无需调整（输入是 combinationKey + 版本）。
- 现有 Redis/MySQL 缓存结构不变，字段中的 strategy 语义升级为 routeCode。

- 缓存策略（按页面维度，yml 配置，推荐）：
  - 配置格式：
    ```yml
    site:
      cache:
        page-policies:
          default: MYSQL_BROADCAST_MEMORY
          pages:
            explore: REDIS_ONLY
            topic:   MYSQL_BROADCAST_MEMORY
    ```
  - 决策规则：读路径在按退化链逐步放大读取时，对所有候选 routeCode 使用同一页面级策略；未配置页面则使用 default。
  - 策略枚举：`MYSQL_BROADCAST_MEMORY` / `REDIS_ONLY`。

策略枚举：
- `MYSQL_BROADCAST_MEMORY`（现状）
  - 读：MemoryOnly；写：MySQLOnly；分发：Nacos 广播到内存
- `REDIS_ONLY`
  - 读：RedisOnly；写：RedisOnly；不参与广播

实现提示：当前代码内置 `MYSQL_BROADCAST_MEMORY` 与 `REDIS_ONLY` 两种策略即可覆盖现阶段需求。

RouteCode 规范（v2）：
- 由多维枚举值按固定顺序用 `|` 拼接构成，当前推荐顺序：`UserStrategyEnum`、`CarModelEnum`、（后续可拓展更多维度）。
- 每一维的取值必须来自代码中定义的枚举（或其稳定 code 字段）；空缺维度不参与拼接。
- 保留字符：`|` 不得出现在单个维度的枚举值中。
- 例：
  - 仅人群：`car_owner`
  - 人群+车型：`car_owner|su7`
  - 全量兜底：`all`

解析兼容性（重要）：
- 现有部分代码通过 `split("\\|")` 解析 `combinationKey=pageId|strategy(|exp:...)`；当 routeCode 内部也包含 `|` 时将被误拆分。
- 建议统一改为“仅定位首个分隔符”和“`|exp:` 前缀”解析：
  - `int p1 = key.indexOf('|'); int p2 = key.indexOf("|" + EXPERIMENT_PREFIX);`
  - `pageId = key.substring(0, p1); routeCode = (p2 > 0 ? key.substring(p1+1, p2) : key.substring(p1+1));`
  - 这样 routeCode 可安全包含 `|`。
- 同时更新使用 `split("\\|")` 的位置（示例：`CacheSyncOrchestrator.extractStrategy`、`CacheSyncService.extractStrategiesFromCacheKeys`）为上述解析方式，或抽象 `CombinationKeyParser` 统一处理。

## 5. 代码改造点（最小、可渐进）
- 路由解析与策略决策
  - 新增 `RouteDecisionResolver`（封装 `RouteResolver` + `PolicyResolver`），面向 pages-only：
    - `RouteDecision resolve(String pageId, Map<String,Object> queryParams, String userId)` → `{ routeCode, cachePolicy, source }`
    - 内部：
      1) `routeCode = RouteResolver.determineRouteCodeWithPageCheck(...)`；
      2) `cachePolicy = PolicyResolver.resolve(pageId, routeCode)`（优先级：page override > code-level > default）。
  - `PolicyResolver` 从注册表读取 page 规则的 policy；注册表不可用时使用“上一次有效快照”；若无快照则降级 Phase 1（仅用于生存性兜底）。
  - `RouteResolver` 仍按 3.1/3.2 匹配 routeCode。
  - 读路径
    - `HistoricalSnapshotPageAssemblyService`/`SitePageAssemblyService`：使用 `RouteDecisionResolver` 一次性拿到 `{routeCode, cachePolicy}`；
    - 用 routeCode 生成 combinationKey，再据 cachePolicy 走内存/Redis。
- 预载构建（写路径）
  - `PageCacheBuilder#buildPageCombinationCache(...)`：
    - Phase 1：`targetGroupKeys` 使用页面配置的分组键集合（`PageConfigModel.groupConfig[*].strategy`）。
    - Phase 2A（pages-only）：`targetGroupKeys` 使用注册表中 `pages[pageId]` 的所有启用规则的 `routeCode`；若某 routeCode 未配置专属模块顺序，装配阶段回退到 `all`（或 `UserStrategyEnum` → `all`）。
    - 始终复用页面级组件去重标记 `pageProcessedComponents`。

### 路由定义内存缓存与查询（Phase 2A）
- 目的：请求时零查库，统一在内存中维护 pages-only 路由元数据并提供查询方法。
- 组件：`RouteDefinitionRegistry`（单例，原子替换）
  - 内存结构：
    - `Map<String, List<PageRule>>`：key=`pageId`；值为启用规则，按 `priority` 升序排列（每个 PageRule 含 routeCode/conditions/policy/isDefault/enabled）。
  - 生命周期：
    - 应用启动时从 Nacos 拉取；随后监听变更推送；可配置定时兜底刷新（`refresh-interval-seconds`）。
    - 支持手动刷新/按页刷新（debug 接口可选）。
  - 原子替换：
    - 刷新时构建新快照并校验，通过后一次性替换；失败保留旧快照。
  - 容错与回退：
    - 首次加载失败或刷新失败 → 继续使用旧快照；若无旧快照 → 自动回退 Phase 1。
    - 配置开关：`site.route.registry.enabled=false` 时强制使用 Phase 1。
  - 指标：
    - 加载时长、加载失败数、page 数量、规则数量、命中率/未命中、降级次数。
  - 查询 API：
    - `List<PageRule> getPageRules(String pageId)`：返回排序后的启用规则。
    - `RouteDecision match(String pageId, RouteContext ctx)`：按规则顺序匹配，返回 `{routeCode, policy, matchedRuleId/version}`。
    - `Set<String> listRouteCodesForPage(String pageId)`：供预载判断可构建的 routeCode 集合。

实现细节建议：
  - 条件预编译：将 `conditions` JSON 解析为一组 Condition 对象（支持 `eq`/`in`），加载期编译为 `Predicate<RouteContext>`，请求期零 JSON 解析开销。
  - RouteContext 注入：`UserStrategyEnum` 与 `CarModelEnum` 从 `UserStrategyService` 获取（当前 `CarModelEnum` 缺失时由 Resolver 内部兜底读取 `queryParams.get("car_model")`）。
  - 数据健壮性：规则非法（未知维度/操作符/取值）、禁用或条件缺失时跳过该规则，保留告警日志。
  - 读路径零查库：`RouteDecisionResolver` 仅访问 `RouteDefinitionRegistry`；当 `registry.enabled=false` 或加载失败时自动退回 Phase 1。
- 页面组装
  - `PageResponseBuilder#filterAndApplyGroupComponents(PageConfigModel, String, PageRespV2)`：入参 `strategy` 语义升级为 `routeCode`，匹配 `groupConfig.strategy`。

> 渐进式启用：以上改动全部向下兼容；当没有配置路由码时，routeCode=audience，整体行为与现状一致。

- ## 6. 数据迁移与初始化
- Phase 1（零迁移）：不需要任何外部存储改造；只需在页面配置中新增 `groupConfig[*].strategy` 项（如 `car_owner_su7`），并按需在 `site_page_module_strategy` 中维护对应顺序即可。

- Phase 2A（Nacos，零迁移）：
  - 创建 DataId（如 `route_registry.json`）与 Group（如 `micar-site`），发布最小配置：
    - `pages`：为所有页面配置兜底 `all`（priority=999, isDefault=true）。
  - 页面必须提供兜底规则；否则不允许上线（启动校验失败或报错告警）。
  - 上线后逐页新增车型路由规则并发布；观察命中与降级指标；问题时可禁用相关规则（enabled=false）或仅保留 `all`。

<!-- DB 迁移步骤已删除，采用 Nacos pages-only，无需建表。 -->

## 7. 例子（explore 页）
- Nacos pages（explore）：
  - (car_owner|su7, priority=10)
  - (car_owner, priority=20)
  - (all, priority=999, is_default=1)
- site_page_module_strategy（explore）：
  - strategy=car_owner|su7 → config=[...]（车型专属组件顺序）
  - strategy=car_owner → config=[...]（车主通用）
  - strategy=all → config=[...]（兜底）
- 请求解析：当 UserStrategyEnum=car_owner 且 CarModelEnum=su7 → 命中 `car_owner|su7` → 组合键 `explore|car_owner|su7|exp:...`

## 8. 兼容性与回滚
- Phase 1：
  - 未配置任何新 routeCode 的页面：routeCode = audience（all/car_owner/...），行为与现状一致；零侵入。
  - 仅配置 `all`：等价现状。
  - 回滚：删除页面配置中新增的 routeCode（如 `car_owner_su7`）即可。
- Phase 2A：
  - 回滚：在 Nacos pages 中禁用相关规则（enabled=false）或仅保留 `all` 兜底。

## 9. 上线步骤建议
- Phase 1（零迁移）：
  1) 上线代码（RouteResolver、回退逻辑）；
  2) 按页面在 groupConfig 中新增 routeCode 并在 `site_page_module_strategy` 配模块顺序；
  3) 观察命中与降级指标，按页面扩围；
  4) 回滚直接移除新 routeCode 配置。

- Phase 2A（Nacos）：
  1) 打开 `site.route.registry.enabled=true`，设置 `source=nacos` 并发布最小配置（仅 all）；
  2) 验证加载成功（debug 接口/指标），观察推送与原子替换日志；
  3) 为目标页面追加车型路由码并发布；
  4) 如遇异常，将配置回滚为仅 all 或关闭 `registry.enabled`。

<!-- DB 上线步骤已删除，采用 Nacos pages-only。 -->

## 10. 开发工作清单（最小集合）
- 新增：`RouteResolver`、`RouteContext`、`RouteDefinitionRegistry`（内存快照，原子替换）。
- Provider 抽象：`RouteDefinitionProvider` 接口，提供 `load()`/`start()`/`stop()`；实现 `NacosRouteDefinitionProvider`（pages-only）。
- 改动：历史快照查询使用 routeCode；预载 `targetGroupKeys`（Phase 1 用 groupConfig；Phase 2A 用注册表）；缓存策略完全依据页面规则的 `policy` 决策（不再读取 yml）。
- 配置：`site.route.registry.enabled|source|refresh-interval-seconds|nacos.*|fallback.*`。
- 文档：本文件 + 在 V1 缓存策略文档中补充路由码兼容说明。
