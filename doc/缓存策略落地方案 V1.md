# 缓存策略落地方案 v1（无版本维度）

> 范围收敛：本次仅新增 1 个缓存策略 `REDIS_ONLY` 与“路由模块 v1”（按页面/人群解析策略）。`defaultPolicy=MYSQL_BROADCAST_MEMORY` 保持不变；不引入版本维度（VersionKey/RouteIndex）。

## 1. 背景与目标
- 现状：预载构建在启动或定时被调用，PageCachePreloadService → PageCacheBuilder 产出 pageId→(combKey→PageRespV2) → CacheSyncService 写 MySQL（V1/V2），随后通过 CacheBootstrapService 发布 Nacos；其他节点收到后从 MySQL 拉取并原子替换到 MemoryCacheManager。读路径 SitePageAssemblyService 从内存命中。
- 目标：抽象“缓存策略 CachePolicy”，按页面（可细化到 strategy）在 yml 配置，驱动写入、分发与读取行为；默认不改变现有页面的行为（向后兼容）。本阶段不引入 VersionKey/RouteIndex（版本维度），组合键仍沿用现状。

## 2. 策略模型（最小实现）
- CachePolicy（枚举）
  - REDIS_ONLY：ReadPlan.redisOnly()，WritePlan.redisOnly()，Distribute.NONE，ReceiverTarget.NONE
  - MYSQL_BROADCAST_MEMORY（现状）：ReadPlan.memoryOnly()，WritePlan.mysqlOnly()，Distribute.BROADCAST，ReceiverTarget.MEMORY
- ReadPlan/WritePlan：枚举或轻量类表示 memoryOnly、redisOnly、mysqlOnly（可拓展多级读写）
- Distribute：NONE、BROADCAST
- ReceiverTarget：NONE、MEMORY、REDIS、MYSQL（本阶段仅使用 MEMORY/NONE）

## 3. 配置方式（yml）
- 配置前缀：`site.cache.policies`
- 字段：
  - `defaultPolicy`：默认策略，建议设为 `MYSQL_BROADCAST_MEMORY`（完全兼容现状）
  - `rules[]`：按页面/策略配置策略，按“精确匹配 > pageId+all > defaultPolicy”生效

示例配置（application.yml 片段）：
```
site:
  cache:
    policies:
      defaultPolicy: MYSQL_BROADCAST_MEMORY
      rules:
        - pageId: home
          strategy: all
          policy: MYSQL_BROADCAST_MEMORY
        - pageId: explore
          strategy: vip
          policy: REDIS_ONLY
```

## 4. 数据与存储结构
- MySQL（保持现状）：`site_page_cache_storage` 存 V1/V2 两份 JSON，含 batchId、configVersion；用于广播场景的落地。
- Redis（新增）：页面快照 Hash 结构：
  - key：`{site:page:cache}:{pageId}`
  - field：组合键（带 `V1_`/`V2_` 前缀），如 `V2_home|vip|exp:...`
  - value：`Result<PageRespV2>` 或 `Result<PageResp>` 的 JSON（与 MySQL 一致，建议写入 V1/V2 双份）
  - 过期：可不设（由预载覆盖），或设置较长 TTL（如 1 天）

## 5. 流程改造（对照现状）

5.1 预载写路径（按策略分流）
```mermaid
sequenceDiagram
  autonumber
  participant T as 定时/手动触发
  participant P as PageCachePreloadService
  participant B as PageCacheBuilder
  participant O as CacheSyncOrchestrator
  participant M as MySQLCacheStorageService
  participant R as RedisCacheStorageService
  participant N as CacheBootstrapService(Nacos)

  T->>P: preloadAllPageCombinations()
  P->>B: 构建 pageCacheMap
  B-->>P: 返回 pageCacheMap
  P->>O: 分策略同步(pageCacheMap)
  alt 默认/现状（MYSQL_BROADCAST_MEMORY）
    O->>M: 保存页面快照(V1/V2)
    O->>N: 发布批次通知(pageBatchMap)
  else 新增 REDIS_ONLY
    O->>R: 保存页面快照到 Redis(Hash)
    Note right of R: 不进行 Nacos 广播
  end
```

5.2 收端（广播策略）
```mermaid
sequenceDiagram
  autonumber
  participant N as Nacos
  participant BS as CacheBootstrapService
  participant CS as CacheSyncService
  participant DB as MySQL
  participant MC as MemoryCacheManager
  participant PS as PageStrategyStateCache

  N-->>BS: 批次通知(configInfo)
  BS->>CS: batchSyncCacheFromStorage(pageBatchMap)
  CS->>DB: load page snapshots(by pageId,batchId)
  CS->>PS: updatePageStrategies(pageId, strategies)
  CS->>MC: swapPageCache(pageId, responseCache)
```

5.3 读路径（按策略读取）
```mermaid
sequenceDiagram
  autonumber
  participant C as Client
  participant A as SitePageAssemblyService
  participant PR as CachePolicyResolver(路由模块)
  participant RF as CacheReadFacade
  participant MC as MemoryCacheManager
  participant RD as Redis

  C->>A: GET /page (pageId, userId, ...)
  A->>A: 生成 combinationKey(+V1/V2)
  A->>PR: resolve(pageId, strategy)
  PR-->>A: policy
  alt policy=MYSQL_BROADCAST_MEMORY
    A->>RF: get(pageId, combinationKey, memoryOnly)
    RF->>MC: get
    MC-->>A: cached JSON
  else policy=REDIS_ONLY
    A->>RF: get(pageId, combinationKey, redisOnly)
    RF->>RD: HGET {site:page:cache}:{pageId} combinationKey
    RD-->>A: cached JSON
  end
  A-->>C: JSON(Result<...>)
```

## 5.4 路由模块 v1（交付重点）
- 职责：将 (pageId, strategy) 解析为 CachePolicy；v1 不包含版本维度（appVersion → versionKey）。
- 配置来源：`application.yml`（`site.cache.policies`）
- 匹配规则：精确规则(pageId+strategy) > pageId+`all` > `defaultPolicy`
- 设计：
  - `CachePolicyProperties`（@ConfigurationProperties 绑定 `defaultPolicy` 与 `rules[]`）
  - `PagePolicyRule{ pageId, strategy, policy }`
  - `CachePolicyResolver.resolve(pageId, strategy)`：返回 `CachePolicy`
  - 可选 Debug 接口：`/debug/system/cache/policy?pageId=&strategy=`

## 6. 组件与改动清单
- 新增
  - 策略模型：`CachePolicy`、`ReadPlan`、`WritePlan`、`Distribute`、`ReceiverTarget`（`site-cache/.../cache/policy/*`）
  - 配置装配（路由模块 v1）：`CachePolicyProperties`、`PagePolicyRule`、`CachePolicyResolver`（`site-server/.../config/*`，`@ConfigurationProperties(prefix = "site.cache.policies")`）
  - Redis 存储：`RedisCacheStorageService implements CacheStorageService.savePageCache`（`site-cache/.../service/*`）
  - 读门面：`CacheReadFacade`（统一封装 memory/redis 读取）
  - 同步编排：`CacheSyncOrchestrator`（聚合分策略写与广播）
  - `SiteRedisKey`：新增 `{site:page:cache}:%s`（页面快照 Hash）
- 改造
  - `PageCachePreloadService.publishCacheChanges`：改为调用 `CacheSyncOrchestrator` 分策略处理
  - `SitePageAssemblyService.pageDataV2OptimizedWithStrategy`：接入策略解析与 `CacheReadFacade`
- 保持不变
  - `CacheBootstrapService`：监听/发布不变；仅处理广播策略页面
  - `MemoryCacheManager`：数据结构不变

## 7. 示例 Redis Key（建议）
```
// 页面快照（Hash）
key   = {site:page:cache}:{pageId}
field = V2_{pageId}|{strategy}|exp:{...}
value = JSON(Result<PageRespV2>)

field = V1_{pageId}|{strategy}|exp:{...}
value = JSON(Result<PageResp>)
```

## 8. 灰度与回滚
- 默认不改变现状：`defaultPolicy = MYSQL_BROADCAST_MEMORY`
- 试点：挑选 1~2 个页面配置 `REDIS_ONLY` 验证；问题时改回 `MYSQL_BROADCAST_MEMORY` 即可回滚
- 观测：
  - 预载日志：分策略写入统计（页数、组合数、失败数）
  - 读路径命中来源：内存/Redis 命中与未命中计数
  - Nacos 发布：仅对广播策略页面，发布失败重试与告警

## 9. 风险与兜底
- RedisOnly 场景 Redis 异常：本阶段返回未命中；后续可增加兜底（MySQL 回源 + 回填 Redis/内存）
- 配置缺失/写入异常：回退使用 `defaultPolicy`；不影响默认策略页面
- 一致性：广播策略仍走“批次+原子替换”；RedisOnly 不参与 Nacos 广播

## 10. 实施步骤（开发排期）
1) 新增策略模型与 yml 配置装配（含默认值与优先级匹配）
2) 新增 `RedisCacheStorageService` 与 `SiteRedisKey`（页面快照 Hash，写 V1/V2 双份）
3) 新增 `CacheSyncOrchestrator`；`PageCachePreloadService` 切换到编排入口
4) 新增 `CacheReadFacade`；`SitePageAssemblyService` 接入策略解析与读门面（默认不改行为）
5) 配置示例与 Debug 接口（可选：`/debug/system/cache/policy?pageId&strategy`）
6) 联调与灰度：挑选 1~2 个页面使用 `REDIS_ONLY` 验证；完善日志指标

## 11. 后续扩展（版本维度/路由索引）
- 引入 VersionKey/RouteIndex，将读路径从“直取键”升级为“(pageId,strategy,versionKey) → CacheEntry(match)”
- 策略仍沿用当前抽象，仅在 RouteIndex 层引入 versionKey 与优先级匹配；写路径在通知中携带受影响的 strategies/versionKeys（参考总技术方案）

---

附注（路由码兼容说明）
- 自“页面路由策略与组合键 V2”起，读写路径中的 strategy 入参在语义上等价于 routeCode（路由码）。
- 未开启路由升级或未配置路由码的页面，routeCode 回落为原有人群策略值（如 all/car_owner 等），组合键与缓存键格式不变。
- 详情参见：doc/页面路由策略与组合键 V2.md。
