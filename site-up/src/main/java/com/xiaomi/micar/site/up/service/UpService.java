package com.xiaomi.micar.site.up.service;

import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.micar.site.up.model.UserRouteContext;

/**
 * 人群处理service
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
public interface UpService {


    /**
     * 获取分组策略
     *
     * @param id 用户id
     * @return UserLabel
     */
    UserStrategyEnum strategy(Long id);

    default void load(UserStrategyEnum label) {
        // nothind
    }

    default void loadAll() {
        // nothind
    }

    /**
     * 返回用户的多维路由上下文（默认实现仅输出单一人群策略）。
     */
    default UserRouteContext routeContext(Long id) {
        UserStrategyEnum s = strategy(id);
        java.util.Map<String, java.util.List<String>> dims = new java.util.HashMap<>();
        if (s != null) {
            dims.put("UserStrategyEnum", java.util.Collections.singletonList(s.getCode()));
        }
        return UserRouteContext.of(dims);
    }
}
