package com.xiaomi.micar.site.up.service;

import com.github.rholder.retry.AttemptTimeLimiters;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.mi.car.iccc.starter.metrics.core.annotation.IcccDuration;
import com.mi.car.iccc.starter.user.permit.client.UserPermitQueryClient;
import com.mi.car.iccc.starter.user.permit.model.UserPermitTagRequest;
import com.mi.car.iccc.starter.user.permit.model.UserPermitTagResponse;
import com.mi.car.iccc.user.permit.common.UserPermitRoleTagEnum;
import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.micar.site.up.model.UserRouteContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 人群处理service（人群包）
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Slf4j
public class UserPermitUpService extends AbstractUpService implements UpService {

    // 90天
    // private static final Long NEW_OWNER_MILLIS = 90 * 24 * 60 * 60 * 1000L;

    private final UserPermitQueryClient userPermitQueryClient;
    protected final Retryer<UserPermitTagResponse> retryer;

    public UserPermitUpService(UserPermitQueryClient userPermitQueryClient) {
        this.userPermitQueryClient = userPermitQueryClient;
        retryer = RetryerBuilder.<UserPermitTagResponse>newBuilder()
                .retryIfRuntimeException()
                .withAttemptTimeLimiter(AttemptTimeLimiters.fixedTimeLimit(100, TimeUnit.MILLISECONDS))
                .withWaitStrategy(WaitStrategies.fixedWait(10, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();
    }

    @Override
    @IcccDuration
    public UserStrategyEnum strategyInner(Long id) {

        if (id == null || id.equals(0L)) {
            return UserStrategyEnum.ALL;
        }

        UserPermitTagRequest request = new UserPermitTagRequest();
        request.setMid(id);
        List<UserPermitRoleTagEnum> tags = new ArrayList<>();
        // 设置查询身份为车主/授权人 或已锁单
        tags.add(UserPermitRoleTagEnum.OWNER);
        tags.add(UserPermitRoleTagEnum.SHARER);
        tags.add(UserPermitRoleTagEnum.LOCKED);
        request.setRoleTags(tags);

        UserPermitTagResponse response = null;
        try {
            response = retryer.call(() -> {
                log.info("人群包userTagQuery接口请求，request={}", request);
                UserPermitTagResponse innerResp = userPermitQueryClient.userTagQuery(request);
                log.info("人群包userTagQuery接口返回，response={}", innerResp);
                return innerResp;
            });
        } catch (Exception e) {
            log.error("userPermitQueryClient userTagQuery error", e);
        }
        if (response == null) {
            throw new RuntimeException("userPermitQueryClient userTagQuery response is null");
        }
        if (CollectionUtils.isEmpty(response.getVehicleProperties())) {
            return UserStrategyEnum.ALL;
        }

        // 是否车主
        if (response.getVehicleProperties().stream().anyMatch(vehicle -> Objects.equals(vehicle.getRoleTag(), UserPermitRoleTagEnum.OWNER.getValue()))) {
            return UserStrategyEnum.CAR_OWNER;
        }

        // 是否授权人
        if (response.getVehicleProperties().stream().anyMatch(vehicle -> Objects.equals(vehicle.getRoleTag(), UserPermitRoleTagEnum.SHARER.getValue()))) {
            return UserStrategyEnum.CAR_SHARE;
        }

        // 是否准车主
        if (response.getVehicleProperties().stream().anyMatch(vehicle -> Objects.equals(vehicle.getRoleTag(), UserPermitRoleTagEnum.LOCKED.getValue()))) {
            return UserStrategyEnum.CAR_LOCKED;
        }

        // 默认兜底
        return UserStrategyEnum.ALL;

    }

    @Override
    public UserRouteContext routeContext(Long id) {
        UserRouteContext ctx = new UserRouteContext();
        List<String> strategies = new ArrayList<>();

        if (id == null || id.equals(0L)) {
            strategies.add(UserStrategyEnum.ALL.getCode());
            ctx.put("UserStrategyEnum", strategies);
            return ctx;
        }

        UserPermitTagRequest request = new UserPermitTagRequest();
        request.setMid(id);
        List<UserPermitRoleTagEnum> tags = new ArrayList<>();
        tags.add(UserPermitRoleTagEnum.OWNER);
        tags.add(UserPermitRoleTagEnum.SHARER);
        tags.add(UserPermitRoleTagEnum.LOCKED);
        request.setRoleTags(tags);

        UserPermitTagResponse response = null;
        try {
            response = retryer.call(() -> {
                log.info("人群包userTagQuery接口请求，request={}", request);
                UserPermitTagResponse innerResp = userPermitQueryClient.userTagQuery(request);
                log.info("人群包userTagQuery接口返回，response={}", innerResp);
                return innerResp;
            });
        } catch (Exception e) {
            log.error("userPermitQueryClient userTagQuery error", e);
        }
        if (response == null || CollectionUtils.isEmpty(response.getVehicleProperties())) {
            strategies.add(UserStrategyEnum.ALL.getCode());
            ctx.put("UserStrategyEnum", strategies);
            return ctx;
        }

        // 聚合多个人群角色
        if (response.getVehicleProperties().stream().anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.OWNER.getValue()))) {
            strategies.add(UserStrategyEnum.CAR_OWNER.getCode());
        }
        if (response.getVehicleProperties().stream().anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.SHARER.getValue()))) {
            strategies.add(UserStrategyEnum.CAR_SHARE.getCode());
        }
        if (response.getVehicleProperties().stream().anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.LOCKED.getValue()))) {
            strategies.add(UserStrategyEnum.CAR_LOCKED.getCode());
        }
        if (strategies.isEmpty()) {
            strategies.add(UserStrategyEnum.ALL.getCode());
        }
        ctx.put("UserStrategyEnum", strategies);

        // 车型维度：如有需要可在此处从 vehicleProperties 中提取车型并填充为 CarModelEnum 列表
        // 注意：具体字段名依赖于外部模型，若确认字段名（例如 getCarModel() / getSeries()），可补充解析逻辑。

        return ctx;
    }
//
//    private boolean isNewOwner(Long mid, List<VehicleProperties> vehicles) {
//        // 按照绑定车辆时间倒排序
//        vehicles.sort(Comparator.comparingLong(VehicleProperties::getBindTime).reversed());
//
//        for (VehicleProperties vehicle : vehicles) {
//            // 获取车主最近绑车时间
//            if (isNewOwner(mid, vehicle.getBindTime())) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    private boolean isNewOwner(Long mid, Long bindTime) {
//        Long now = IcccCommonDateUtil.getCurrentTimeMillis();
//        return bindTime != null && (now - bindTime) <= NEW_OWNER_MILLIS;
//    }

}
