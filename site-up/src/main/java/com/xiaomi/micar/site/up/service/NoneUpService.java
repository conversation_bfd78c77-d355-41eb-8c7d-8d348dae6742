package com.xiaomi.micar.site.up.service;

import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.micar.site.up.model.UserRouteContext;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 无人群处理service
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Slf4j
public class NoneUpService extends AbstractUpService implements UpService {

    @Override
    public UserStrategyEnum strategyInner(Long id) {
        return UserStrategyEnum.ALL;
    }

    @Override
    public UserRouteContext routeContext(Long id) {
        Map<String, List<String>> dims = new HashMap<>();
        dims.put("UserStrategyEnum", Collections.singletonList(UserStrategyEnum.ALL.getCode()));
        return UserRouteContext.of(dims);
    }

}
