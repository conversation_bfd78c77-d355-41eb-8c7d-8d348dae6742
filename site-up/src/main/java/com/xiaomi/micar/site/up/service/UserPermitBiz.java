package com.xiaomi.micar.site.up.service;

import com.github.rholder.retry.AttemptTimeLimiters;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.mi.car.iccc.starter.user.permit.client.UserPermitQueryClient;
import com.mi.car.iccc.starter.user.permit.model.UserPermitTagRequest;
import com.mi.car.iccc.starter.user.permit.model.UserPermitTagResponse;
import com.mi.car.iccc.user.permit.common.UserPermitRoleTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 人群处理
 *
 * <AUTHOR>
 * @since 2025/05/19
 */
@Slf4j
@Component
public class UserPermitBiz {

    private final UserPermitQueryClient userPermitQueryClient;
    protected final Retryer<UserPermitTagResponse> retryer;

    public UserPermitBiz(UserPermitQueryClient userPermitQueryClient) {
        this.userPermitQueryClient = userPermitQueryClient;
        retryer = RetryerBuilder.<UserPermitTagResponse>newBuilder()
                .retryIfRuntimeException()
                .withAttemptTimeLimiter(AttemptTimeLimiters.fixedTimeLimit(100, TimeUnit.MILLISECONDS))
                .withWaitStrategy(WaitStrategies.fixedWait(10, TimeUnit.MILLISECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();
    }

    public UserPermitRoleTagEnum getUserRoleTag(Long id) {
        if (id == null || id <= 0) {
            return null;
        }

        UserPermitTagRequest request = new UserPermitTagRequest();
        request.setMid(id);
        List<UserPermitRoleTagEnum> tags = new ArrayList<>();
        // 设置查询身份为车主/授权人 或已锁单
        tags.add(UserPermitRoleTagEnum.OWNER);
        tags.add(UserPermitRoleTagEnum.SHARER);
        tags.add(UserPermitRoleTagEnum.LOCKED);
        request.setRoleTags(tags);

        UserPermitTagResponse response = null;
        try {
            response = retryer.call(() -> {
                log.info("人群包userTagQuery接口请求，request={}", request);
                UserPermitTagResponse innerResp = userPermitQueryClient.userTagQuery(request);
                log.info("人群包userTagQuery接口返回，response={}", innerResp);
                return innerResp;
            });
        } catch (Exception e) {
            log.error("userPermitQueryClient userTagQuery exception", e);
            // 异常降级为默认配置
            return null;
        }

        if (CollectionUtils.isEmpty(response.getVehicleProperties())) {
            return null;
        }

        if (response.getVehicleProperties().stream()
                .anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.OWNER.getValue()))) {
            return UserPermitRoleTagEnum.OWNER;
        }
        if (response.getVehicleProperties().stream()
                .anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.SHARER.getValue()))) {
            return UserPermitRoleTagEnum.SHARER;
        }

        if (response.getVehicleProperties().stream()
                .anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.LOCKED.getValue()))) {
            return UserPermitRoleTagEnum.LOCKED;
        }
        if (response.getVehicleProperties().stream()
                .anyMatch(v -> Objects.equals(v.getRoleTag(), UserPermitRoleTagEnum.DELIVERED.getValue()))) {
            return UserPermitRoleTagEnum.DELIVERED;
        }
        return null;
    }
}
