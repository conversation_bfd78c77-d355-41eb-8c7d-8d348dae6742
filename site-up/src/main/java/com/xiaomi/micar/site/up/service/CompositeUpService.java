package com.xiaomi.micar.site.up.service;

import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.micar.site.up.model.UserRouteContext;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 复合人群匹配
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Slf4j
public class CompositeUpService extends AbstractUpService implements UpService {

    private final LocalUpService localUpService;

    private final UserPermitUpService userPermitUpService;

    public CompositeUpService(LocalUpService localUpService, UserPermitUpService userPermitUpService) {
        this.localUpService = localUpService;
        this.userPermitUpService = userPermitUpService;
    }

    @Override
    public UserStrategyEnum strategyInner(Long id) {
        if (id == null || id.equals(0L)) {
            return UserStrategyEnum.ALL;
        }
        try {
            return userPermitUpService.strategyInner(id);
        } catch (Exception e) {
            log.error("userPermitUpService match error", e);
        }

        log.error("人群匹配降级为本地匹配. id={}", id);
        try {
            return localUpService.strategyInner(id);
        } catch (Exception e) {
            log.error("localUpService strategyInner error", e);
        }
        log.error("人群匹配再次降级为默认策略. id={}", id);
        return UserStrategyEnum.ALL;
    }

    @Override
    public UserRouteContext routeContext(Long id) {
        try {
            return userPermitUpService.routeContext(id);
        } catch (Exception e) {
            log.error("userPermitUpService routeContext error", e);
        }
        try {
            return localUpService.routeContext(id);
        } catch (Exception e) {
            log.error("localUpService routeContext error", e);
        }
        // 回退到与 UpService 默认实现一致的逻辑，避免对 default 调用的编译兼容问题
        UserStrategyEnum s = strategy(id);
        Map<String, List<String>> dims = new HashMap<>();
        if (s != null) {
            dims.put("UserStrategyEnum", Collections.singletonList(s.getCode()));
        }
        return UserRouteContext.of(dims);
    }
}
