package com.xiaomi.micar.site.up.service;

import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025/05/12
 */
@Slf4j
public abstract class AbstractUpService implements UpService {

    @Override
    public final UserStrategyEnum strategy(Long id) {

        if (id == null || id.equals(0L)) {
            return UserStrategyEnum.ALL;
        }
        UserStrategyEnum result = UserStrategyEnum.ALL;
        try {
            result = strategyInner(id);
        } finally {
            log.info("user up match: class={}, label={}, id={}, result={}", this.getClass().getSimpleName(), result, id, result);
        }
        return result;
    }

    public abstract UserStrategyEnum strategyInner(Long id);
}
