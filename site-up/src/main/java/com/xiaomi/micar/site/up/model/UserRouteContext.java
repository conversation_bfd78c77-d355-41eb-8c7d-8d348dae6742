package com.xiaomi.micar.site.up.model;

import lombok.Data;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户多维路由上下文（site-up 侧统一产出）。
 * dimValues: 维度名 -> 该用户在此维度下命中的取值集合（允许多值）。
 * 约定的维度名："UserStrategyEnum"、"CarModelEnum"（可扩展）。
 */
@Data
public class UserRouteContext {

    private Map<String, List<String>> dimValues = new HashMap<>();

    public static UserRouteContext of(Map<String, List<String>> dims) {
        UserRouteContext ctx = new UserRouteContext();
        if (dims != null) ctx.dimValues.putAll(dims);
        return ctx;
    }

    public List<String> valuesOf(String dim) {
        if (dimValues == null) return Collections.emptyList();
        List<String> vals = dimValues.get(dim);
        return vals == null ? Collections.emptyList() : vals;
    }

    public void put(String dim, List<String> values) {
        if (dim == null || values == null) return;
        this.dimValues.put(dim, values);
    }
}

