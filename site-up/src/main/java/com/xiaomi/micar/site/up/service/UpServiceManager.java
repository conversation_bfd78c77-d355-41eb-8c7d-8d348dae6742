package com.xiaomi.micar.site.up.service;

import com.alibaba.nacos.api.config.ConfigService;
import com.google.common.collect.Maps;
import com.mi.car.iccc.starter.user.permit.client.UserPermitQueryClient;
import com.xiaomi.micar.site.up.config.UpProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * 人群处理service
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Component
public class UpServiceManager {

    public static final String NONE_UP_SERVICE = "none";
    public static final String LOCAL_UP_SERVICE = "local";
    public static final String USER_PERMIT_UP_SERVICE = "user-permit";
    public static final String COMPOSITE_UP_SERVICE = "composite";

    @Resource
    private UpProperties upProperties;

    @Resource
    private UserPermitQueryClient userPermitQueryClient;

    @Resource
    private ConfigService configService;

    private final Map<String, UpService> upServiceMap = Maps.newHashMap();

    @PostConstruct
    private void init() {
        String strategy = upProperties.getStrategy();
        switch (strategy) {
            case LOCAL_UP_SERVICE:
                upServiceMap.put(LOCAL_UP_SERVICE, new LocalUpService(upProperties.getLocal(), configService));
                break;
            case USER_PERMIT_UP_SERVICE:
                upServiceMap.put(USER_PERMIT_UP_SERVICE, new UserPermitUpService(userPermitQueryClient));
                break;
            case COMPOSITE_UP_SERVICE:
                LocalUpService localUpService = new LocalUpService(upProperties.getLocal(), configService);
                UserPermitUpService userPermitUpService = new UserPermitUpService(userPermitQueryClient);
                CompositeUpService compositeUpService = new CompositeUpService(localUpService, userPermitUpService);
                upServiceMap.put(LOCAL_UP_SERVICE, localUpService);
                upServiceMap.put(USER_PERMIT_UP_SERVICE, userPermitUpService);
                upServiceMap.put(COMPOSITE_UP_SERVICE, compositeUpService);
                break;
            case NONE_UP_SERVICE:
            default:
                upServiceMap.put(NONE_UP_SERVICE, new NoneUpService());
                break;
        }
    }

    public UpService getUpService() {
        return getUpService(upProperties.getStrategy());
    }

    public UpService getUpService(String strategy) {
        return upServiceMap.get(strategy);
    }

}
