package com.xiaomi.micar.site.up.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 场景代码类型枚举
 * 定义了系统支持的场景代码类型
 */
@Getter
@AllArgsConstructor
public enum UserStrategyEnum {

    CAR_OWNER("car_owner", "车主"),
    CAR_SHARE("car_share", "授权用车人"),
    CAR_LOCKED("car_locked", "已锁单准车主"),

    ALL("all", "默认用户");


    private final String code;
    private final String description;

    /**
     * 根据场景代码获取枚举值
     *
     * @param code 场景代码
     * @return 枚举值，如果不存在则返回null
     */
    public static UserStrategyEnum fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (UserStrategyEnum type : UserStrategyEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
