package com.xiaomi.micar.site.up.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 人群策略配置参数
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@ConfigurationProperties(prefix = "site.up")
@Data
public class UpProperties {

    private String strategy = "composite";

    private LocalConfig local = new LocalConfig();

    @Data
    public static class LocalConfig {
        private JdbcConfig jdbc;
        private String nacosDataId;

        private String queryVersion;
        private String queryData;
    }

    @Data
    public static class JdbcConfig {

        private String url;
        private String username;
        private String password;
        private String driverClassName;

    }

}
