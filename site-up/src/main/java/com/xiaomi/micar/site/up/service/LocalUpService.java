package com.xiaomi.micar.site.up.service;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Maps;
import com.mi.car.iccc.iccccommonutil.util.IcccCommonDateUtil;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.mi.car.iccc.starter.metrics.core.annotation.IcccDuration;
import com.xiaomi.micar.site.up.config.UpProperties;
import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.micar.site.up.model.UserInfo;
import com.xiaomi.micar.site.up.model.UserRouteContext;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.roaringbitmap.longlong.Roaring64Bitmap;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.Assert;

import java.sql.Types;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

import static org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT;

/**
 * 本地人群处理service
 *
 * <AUTHOR>
 * @since 2025/05/12
 */
@Slf4j
public class LocalUpService extends AbstractUpService implements UpService {

    private final UpProperties.LocalConfig localConfig;
    private final ConfigService configService;

    private final JdbcTemplate jdbcTemplate;
    private Map<UserStrategyEnum, Roaring64Bitmap> upBitmap;


    public LocalUpService(UpProperties.LocalConfig localConfig, ConfigService configService) {

        Assert.notNull(localConfig.getJdbc(), "localConfig.jdbc is null");
        Assert.notNull(localConfig.getJdbc().getUrl(), "localConfig.jdbc.url is null");
        Assert.notNull(localConfig.getJdbc().getUsername(), "localConfig.jdbc.username is null");
        Assert.notNull(localConfig.getJdbc().getPassword(), "localConfig.jdbc.password is null");

        this.localConfig = localConfig;
        this.configService = configService;

        upBitmap = Maps.newHashMap();

        // init datasource
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(localConfig.getJdbc().getUrl());
        dataSource.setUsername(localConfig.getJdbc().getUsername());
        dataSource.setPassword(localConfig.getJdbc().getPassword());
        dataSource.setDriverClassName(localConfig.getJdbc().getDriverClassName());
        dataSource.setMaximumPoolSize(1);
        this.jdbcTemplate = new JdbcTemplate(dataSource);

        // nacos获取配置初始化并开启监听
        try {
            String config = configService.getConfig(localConfig.getNacosDataId(), "DEFAULT_GROUP", 5000);
            reloadAll(config);
            configService.addListener(localConfig.getNacosDataId(), "DEFAULT_GROUP", new Listener() {

                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String s) {
                    reloadAll(s);
                }
            });

        } catch (NacosException e) {
            throw new RuntimeException("人群本地处理Nacos初始化异常", e);
        }
    }


    @Override
    @IcccDuration
    public UserStrategyEnum strategyInner(Long id) {
        if (id == null || id.equals(0L)) {
            return UserStrategyEnum.ALL;
        }

        // 是否车主
        if (Optional.ofNullable(upBitmap.get(UserStrategyEnum.CAR_OWNER)).map(bitmap -> bitmap.contains(id)).orElse(false)) {
            return UserStrategyEnum.CAR_OWNER;
        }

        // 是否授权人
        if (Optional.ofNullable(upBitmap.get(UserStrategyEnum.CAR_SHARE)).map(bitmap -> bitmap.contains(id)).orElse(false)) {
            return UserStrategyEnum.CAR_SHARE;
        }

        // 是否准车主
        if (Optional.ofNullable(upBitmap.get(UserStrategyEnum.CAR_LOCKED)).map(bitmap -> bitmap.contains(id)).orElse(false)) {
            return UserStrategyEnum.CAR_LOCKED;
        }

        // 兜底默认
        return UserStrategyEnum.ALL;
    }

    @Override
    public UserRouteContext routeContext(Long id) {
        Map<String, List<String>> dims = new HashMap<>();
        List<String> strategies = new ArrayList<>();
        if (id != null && !id.equals(0L)) {
            if (Optional.ofNullable(upBitmap.get(UserStrategyEnum.CAR_OWNER))
                    .map(b -> b.contains(id)).orElse(false)) {
                strategies.add(UserStrategyEnum.CAR_OWNER.getCode());
            }
            if (Optional.ofNullable(upBitmap.get(UserStrategyEnum.CAR_SHARE))
                    .map(b -> b.contains(id)).orElse(false)) {
                strategies.add(UserStrategyEnum.CAR_SHARE.getCode());
            }
            if (Optional.ofNullable(upBitmap.get(UserStrategyEnum.CAR_LOCKED))
                    .map(b -> b.contains(id)).orElse(false)) {
                strategies.add(UserStrategyEnum.CAR_LOCKED.getCode());
            }
        }
        if (strategies.isEmpty()) {
            strategies.add(UserStrategyEnum.ALL.getCode());
        }
        dims.put("UserStrategyEnum", strategies);
        return UserRouteContext.of(dims);
    }


    @Override
    public void loadAll() {
        Integer date = jdbcTemplate.queryForObject(localConfig.getQueryVersion(), Integer.class);
        Map<String, Object> config = Maps.newHashMap();
        config.put("date", date);
        config.put("updateTime", ISO_8601_EXTENDED_DATETIME_FORMAT.format(new Date()));
        try {
            configService.publishConfig(localConfig.getNacosDataId(), "DEFAULT_GROUP", JsonUtil.toJSONString(config));
        } catch (NacosException e) {
            log.error("public nacos error", e);
        }
    }

    public void reloadAll(String config) {
        int date = JsonUtil.readTree(config).get("date").asInt();
        reloadBitmap(date);
    }

    public void reloadBitmap(Integer date) {
        long startTime = IcccCommonDateUtil.getCurrentTimeMillis();
        Integer limit = 10000;

        Map<UserStrategyEnum, Roaring64Bitmap> tmpUpBitmap = Maps.newHashMap();
        Map<UserStrategyEnum, AtomicInteger> tmpCounter = Maps.newHashMap();
        AtomicInteger counter = new AtomicInteger();
        long lastId = 0L;
        while (true) {
            List<UserInfo> ids = getUserProfile(date, lastId, limit);
            if (ids.isEmpty()) {
                break;
            }
            ids.forEach(u -> {
                counter.incrementAndGet();

                if (u.getOwner() != null && u.getOwner()) {
                    tmpUpBitmap.computeIfAbsent(UserStrategyEnum.CAR_OWNER, k -> new Roaring64Bitmap()).add(u.getMid());
                    tmpCounter.computeIfAbsent(UserStrategyEnum.CAR_OWNER, k -> new AtomicInteger()).incrementAndGet();
                }
                if (u.getSharer() != null && u.getSharer()) {
                    tmpUpBitmap.computeIfAbsent(UserStrategyEnum.CAR_SHARE, k -> new Roaring64Bitmap()).add(u.getMid());
                    tmpCounter.computeIfAbsent(UserStrategyEnum.CAR_SHARE, k -> new AtomicInteger()).incrementAndGet();
                }
                if (u.getLocked() != null && u.getLocked()) {
                    tmpUpBitmap.computeIfAbsent(UserStrategyEnum.CAR_LOCKED, k -> new Roaring64Bitmap()).add(u.getMid());
                    tmpCounter.computeIfAbsent(UserStrategyEnum.CAR_LOCKED, k -> new AtomicInteger()).incrementAndGet();
                }
            });
            lastId = ids.get(ids.size() - 1).getMid();
            log.info("--- reload up processing : date={}, lastId={}, counter={}", date, lastId, counter.get());
        }
        long cost = IcccCommonDateUtil.getCurrentTimeMillis() - startTime;

        log.info("reload up success : date={}, total:{}, cost={}ms", date, counter.get(), cost);
        tmpUpBitmap.forEach((k, v) -> {
            log.info("reload up success : date={}, label={}, total:{}, size={}Bytes", date, k, tmpCounter.get(k).get(), v.getSizeInBytes());
        });

        upBitmap = tmpUpBitmap;
    }

    private List<UserInfo> getUserProfile(Integer date, Long lastId, Integer limit) {
        return jdbcTemplate.query(localConfig.getQueryData(),
                new Object[]{date, lastId, limit},
                new int[]{java.sql.Types.INTEGER, Types.BIGINT, java.sql.Types.INTEGER},
                new BeanPropertyRowMapper<>(UserInfo.class));
    }

}
