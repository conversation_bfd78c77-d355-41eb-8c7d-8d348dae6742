<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mi.carlife</groupId>
        <artifactId>carlife-parent-pom</artifactId>
        <version>1.0.6</version>
    </parent>

    <groupId>com.xiaomi.micar</groupId>
    <artifactId>micar-site</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>site-core</module>
        <module>site-up</module>
        <module>site-cache</module>
        <module>site-server</module>
        <module>site-admin</module>
        <module>site-component</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <poi.version>3.15</poi.version>
        <iccc.starter.version>1.7.15-carlife</iccc.starter.version>
        <iccc.common.util.version>1.0.46</iccc.common.util.version>
        <mi-api-core.version>2.7.12-mone-v5-SNAPSHOT</mi-api-core.version>
        <mi-api-annotation.version>2.7.12-mone-SNAPSHOT</mi-api-annotation.version>
        <youpin-infra-rpc.version>1.28-SNAPSHOT</youpin-infra-rpc.version>
        <order-api.version>1.7.15-RELEASE</order-api.version>
        <fsm-api.version>1.2.5-SNAPSHOT</fsm-api.version>
        <good-api.version>3.6-SNAPSHOT</good-api.version>
        <promotion-api.version>1.21.20-SNAPSHOT</promotion-api.version>
        <coupon-api.version>1.1.9-SNAPSHOT</coupon-api.version>
        <aries-client.version>1.1.6-SNAPSHOT</aries-client.version>
        <xmstore-wms-benefits-api.version>1.0.0-RELEASE</xmstore-wms-benefits-api.version>
        <org.projectlombok.version>1.18.30</org.projectlombok.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <iccc.idcenter.version>1.0.1</iccc.idcenter.version>
        <iccc-api.version>1.1.396</iccc-api.version>
        <poi.version>5.2.3</poi.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <mysql.version>8.0.19</mysql.version>
        <iccc-cnzone-proxy-api.version>1.0.8-RELEASE</iccc-cnzone-proxy-api.version>
        <iccc-user-permit-starter>1.7.24</iccc-user-permit-starter>
        <spring-cloud-starter-alibaba-sentinel>2.1.4.1-mone-SNAPSHOT</spring-cloud-starter-alibaba-sentinel>
        <sentinel.version>1.8.2.1-mone-SNAPSHOT</sentinel.version>
        <mockito.version>3.12.4</mockito.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xiaomi.micar</groupId>
                <artifactId>site-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.micar</groupId>
                <artifactId>site-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.micar</groupId>
                <artifactId>site-component</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.micar</groupId>
                <artifactId>site-up</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- logger starter -->
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-logger-starter</artifactId>
                <version>${iccc.starter.version}</version>
            </dependency>
            <!-- rocketmq starter -->
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-rocketmq-starter</artifactId>
                <version>${iccc.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-fds-starter</artifactId>
                <version>${iccc.starter.version}</version>
            </dependency>
            <!-- common-util -->
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-common-util</artifactId>
                <version>${iccc.common.util.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>11.8</version>
            </dependency>
            <!--mi api-->
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>dubbo-docs-core</artifactId>
                <version>${mi-api-core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>dubbo</groupId>
                        <artifactId>org.apache.dubbo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>dubbo-docs-annotations</artifactId>
                <version>${mi-api-annotation.version}</version>
            </dependency>
            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <!-- youpin -->
            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>youpin-infra-rpc</artifactId>
                <version>${youpin-infra-rpc.version}</version>
            </dependency>
            <!--订单中台-->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>order-api</artifactId>
                <version>${order-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 履约相关接口 -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>fsm-api</artifactId>
                <version>${fsm-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.xiaomi.nr.goods</groupId>
                <artifactId>nr-goods-service-api</artifactId>
                <version>${good-api.version}</version>
            </dependency>

            <!-- 促销活动 -->
            <dependency>
                <artifactId>promotion-api</artifactId>
                <groupId>com.xiaomi.nr</groupId>
                <version>${promotion-api.version}</version>
            </dependency>
            <!-- 优惠券 -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>coupon-api</artifactId>
                <version>${coupon-api.version}</version>
            </dependency>
            <!-- 积分中台 -->
            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>aries-client</artifactId>
                <version>${aries-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.xiaomi.nr</groupId>
                        <artifactId>xiaomi-dubbo-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 寄存服务 -->
            <dependency>
                <groupId>com.xiaomi.cnzone</groupId>
                <artifactId>xmstore-wms-benefits-api</artifactId>
                <version>${xmstore-wms-benefits-api.version}</version>
            </dependency>
            <!-- 中国区 -->

            <!-- idCenter-->
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>id-center</artifactId>
                <version>${iccc.idcenter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-api</artifactId>
                <version>${iccc-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-cnzone-proxy-api</artifactId>
                <version>${iccc-cnzone-proxy-api.version}</version>
            </dependency>
            <!-- Mockito版本管理 -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-user-permit-starter</artifactId>
                <version>${iccc-user-permit-starter}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>${spring-cloud-starter-alibaba-sentinel}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.mibpm</groupId>
                <artifactId>mibpm-bpm-spring-boot-starter</artifactId>
                <version>1.2.6-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.mi.carsale</groupId>
                <artifactId>carsale-product-management-sdk</artifactId>
                <version>${carsale-product-management-sdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <fork>true</fork>
                        <verbose>true</verbose>
                        <encoding>UTF-8</encoding>
                        <compilerArguments>
                            <sourcepath>
                                ${project.basedir}/src/main/java
                            </sourcepath>
                        </compilerArguments>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${org.projectlombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${lombok.mapstruct.binding.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <reuseForks>false</reuseForks>
                        <forkCount>2</forkCount>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.properties</include>
                    <include>*.yml</include>
                    <include>*.json</include>
                    <include>*.xml</include>
                    <include>META-INF/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources/mapper</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.xml</include>
                </includes>
                <targetPath>mapper</targetPath>
            </resource>
        </resources>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>

</project>
