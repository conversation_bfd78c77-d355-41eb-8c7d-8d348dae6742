package com.xiaomi.micar.site.model;

import com.xiaomi.micar.site.component.ComponentConf;
import lombok.Data;

import java.util.List;

/**
 * 用来给客户端返回的页面结构
 * 用于描述整个页面的结构和内容
 */
@Data
public class PageRespV2 {
    /**
     * 页面ID
     */
    private String pageId;
    /**
     * 当前页面试验分桶结果，可能有多层，用于客户端打点
     */
    private List<String> expId;

    /**
     * 页面属性
     */
    private PageInfo page;

    /**
     * 模块列表，key为模块标识，value为模块内容
     */
    private List<ComponentConf> modules;

    /**
     * 帖子ID列表
     */
    private List<String> postIds;

    /**
     * 页面属性
     */
    @Data
    public static class PageInfo {
        /**
         * 页面标题
         */
        private String title;
        /**
         * 页面分组ID
         */
        private String groupKey;
        /**
         * 页面缓存时间，单位秒
         */
        private int cacheMaxAge;

        public void setCacheMaxAge(int cacheMaxAge) {
            this.cacheMaxAge = cacheMaxAge;
        }
    }

    /**
     * 图片信息
     */
    @Data
    public static class ImageInfo {
        /**
         * 图片地址
         */
        private String src;
        /**
         * 图片宽度
         */
        private Integer width;
        /**
         * 图片高度
         */
        private Integer height;
    }

}
