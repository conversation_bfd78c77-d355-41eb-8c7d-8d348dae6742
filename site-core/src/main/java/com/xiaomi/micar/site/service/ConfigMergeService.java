package com.xiaomi.micar.site.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.dao.SitePageModuleEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 配置合并服务
 * 负责将审核通过的草稿配置安全地合并到线上配置中
 * 
 * 核心业务流程：
 * 1. 验证输入参数和配置格式
 * 2. 解析当前线上配置
 * 3. 根据草稿状态确定合并操作类型
 * 4. 执行具体的配置合并操作
 * 5. 验证合并结果的完整性
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class ConfigMergeService {

    @Resource
    private SitePageModuleEngine moduleEngine;

    /**
     * 配置合并操作类型
     */
    public enum ConfigMergeOperation {
        /** 删除组件（下线审核通过） */
        REMOVE_COMPONENT("删除组件"),
        /** 更新或新增组件（修改审核通过） */
        UPDATE_OR_ADD_COMPONENT("更新或新增组件");

        private final String description;

        ConfigMergeOperation(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 配置合并请求
     */
    public static class ConfigMergeRequest {
        private final String currentConfig;
        private final SitePageConfigDraftEntity draft;
        private final ConfigMergeOperation operation;

        public ConfigMergeRequest(String currentConfig, SitePageConfigDraftEntity draft, boolean isOfflineAudit) {
            this.currentConfig = currentConfig;
            this.draft = draft;
            this.operation = isOfflineAudit ? ConfigMergeOperation.REMOVE_COMPONENT : ConfigMergeOperation.UPDATE_OR_ADD_COMPONENT;
        }

        public String getCurrentConfig() { return currentConfig; }
        public SitePageConfigDraftEntity getDraft() { return draft; }
        public ConfigMergeOperation getOperation() { return operation; }
    }

    /**
     * 配置合并上下文
     */
    private static class ConfigMergeContext {
        private final ConfigMergeRequest request;
        private final ObjectNode configNode;
        private final ArrayNode componentsArray;
        private final String moduleId;

        public ConfigMergeContext(ConfigMergeRequest request, ObjectNode configNode, ArrayNode componentsArray) {
            this.request = request;
            this.configNode = configNode;
            this.componentsArray = componentsArray;
            this.moduleId = request.getDraft().getModuleId() != null ? request.getDraft().getModuleId().toString() : null;
        }
        public ObjectNode getConfigNode() { return configNode; }
        public ArrayNode getComponentsArray() { return componentsArray; }
        public String getModuleId() { return moduleId; }
        public SitePageConfigDraftEntity getDraft() { return request.getDraft(); }
        public ConfigMergeOperation getOperation() { return request.getOperation(); }
    }

    /**
     * 合并配置 - 主入口方法
     * 将审核通过的草稿配置安全地合并到线上配置中
     *
     * @param currentConfig  当前线上版本的配置
     * @param draftEntity    草稿的配置
     * @param isOfflineAudit 是否为下线审核
     * @return 合并后的配置
     * @throws ConfigMergeException 配置合并失败时抛出
     */
    public String mergeConfig(String currentConfig, SitePageConfigDraftEntity draftEntity, boolean isOfflineAudit) {
        ConfigMergeRequest request = new ConfigMergeRequest(currentConfig, draftEntity, isOfflineAudit);
        
        log.info("开始配置合并: draftId={}, moduleId={}, operation={}", 
                draftEntity.getId(), draftEntity.getModuleId(), request.getOperation().getDescription());

        try {
            // 1. 验证并准备合并上下文
            ConfigMergeContext context = validateAndPrepareContext(request);
            
            // 2. 执行配置合并操作
            executeMergeOperation(context);
            
            // 3. 验证并返回最终配置
            String result = finalizeAndValidateConfig(context.getConfigNode());
            
            log.info("配置合并成功: draftId={}, operation={}", 
                    draftEntity.getId(), request.getOperation().getDescription());
            
            return result;
            
        } catch (ConfigMergeException e) {
            log.error("配置合并失败: draftId={}, operation={}, error={}", 
                    draftEntity.getId(), request.getOperation().getDescription(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("配置合并异常: draftId={}, operation={}, error={}", 
                    draftEntity.getId(), request.getOperation().getDescription(), e.getMessage(), e);
            throw new ConfigMergeException("配置合并过程中发生未预期的错误: " + e.getMessage(), e);
        }
    }

    /**
     * 预览配置合并 - 用于预览场景
     * 根据草稿状态自动判断合并操作类型
     *
     * @param currentConfig 当前线上版本的配置
     * @param draftEntity   草稿的配置
     * @return 合并后的配置
     * @throws ConfigMergeException 配置合并失败时抛出
     */
    public String mergeConfigForPreview(String currentConfig, SitePageConfigDraftEntity draftEntity) {
        // 根据草稿状态判断是否为下线操作
        boolean isOfflineAudit = (draftEntity.getStatus() != null && 
                                 draftEntity.getStatus().equals(com.xiaomi.micar.site.enums.ConfigStatus.OFFLINE_AUDIT.getCode()));
        
        return mergeConfig(currentConfig, draftEntity, isOfflineAudit);
    }

    /**
     * 验证并准备合并上下文
     */
    private ConfigMergeContext validateAndPrepareContext(ConfigMergeRequest request) {
        // 验证输入参数
        validateMergeRequest(request);
        
        // 解析当前配置
        ObjectNode configNode = parseCurrentConfig(request.getCurrentConfig());
        
        // 获取并验证组件数组
        ArrayNode componentsArray = extractAndValidateComponentsArray(configNode);
        
        return new ConfigMergeContext(request, configNode, componentsArray);
    }

    /**
     * 执行配置合并操作
     */
    private void executeMergeOperation(ConfigMergeContext context) {
        switch (context.getOperation()) {
            case REMOVE_COMPONENT:
                removeComponentFromConfig(context);
                break;
            case UPDATE_OR_ADD_COMPONENT:
                updateOrAddComponentInConfig(context);
                break;
            default:
                throw new ConfigMergeException("不支持的配置合并操作: " + context.getOperation());
        }
    }

    /**
     * 验证并返回最终配置
     */
    private String finalizeAndValidateConfig(ObjectNode configNode) {
        try {
            String result = JsonUtil.toJSONLogString(configNode);
            // 验证结果是否是有效的JSON
            JsonUtil.readTree(result);
            return result;
        } catch (Exception e) {
            throw new ConfigMergeException("合并后的配置格式无效: " + e.getMessage(), e);
        }
    }

    /**
     * 验证合并请求
     */
    private void validateMergeRequest(ConfigMergeRequest request) {
        if (request == null) {
            throw new ConfigMergeException("配置合并请求不能为空");
        }

        String currentConfig = request.getCurrentConfig();
        if (currentConfig == null || currentConfig.trim().isEmpty() || "null".equals(currentConfig.trim())) {
            throw new ConfigMergeException("当前线上配置不能为空");
        }

        SitePageConfigDraftEntity draft = request.getDraft();
        if (draft == null) {
            throw new ConfigMergeException("草稿配置不能为空");
        }

        if (draft.getModuleId() == null) {
            throw new ConfigMergeException("草稿模块ID不能为空");
        }

        // 验证草稿配置格式
        validateDraftConfig(draft);
    }

    /**
     * 验证草稿配置格式
     */
    private void validateDraftConfig(SitePageConfigDraftEntity draft) {
        String config = draft.getConfig();
        if (config == null || config.trim().isEmpty()) {
            throw new ConfigMergeException("草稿配置内容不能为空");
        }

        try {
            JsonNode draftNode = JsonUtil.readTree(config);
            if (!draftNode.isObject()) {
                throw new ConfigMergeException("草稿配置必须是有效的JSON对象");
            }
        } catch (Exception e) {
            throw new ConfigMergeException("草稿配置JSON格式无效: " + e.getMessage(), e);
        }
    }

    /**
     * 解析当前配置
     */
    private ObjectNode parseCurrentConfig(String currentConfig) {
        // 检查配置是否为空（下线的页面配置为null）
        if (currentConfig == null || currentConfig.trim().isEmpty()) {
            throw new ConfigMergeException("当前线上配置为空，无法进行配置合并");
        }

        try {
            JsonNode configNode = JsonUtil.readTree(currentConfig);
            if (!configNode.isObject()) {
                throw new ConfigMergeException("当前线上配置必须是有效的JSON对象");
            }
            return (ObjectNode) configNode;
        } catch (Exception e) {
            throw new ConfigMergeException("解析当前线上配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提取并验证组件数组
     */
    private ArrayNode extractAndValidateComponentsArray(ObjectNode configNode) {
        JsonNode componentsNode = configNode.get("components");
        if (componentsNode == null) {
            throw new ConfigMergeException("当前配置中缺少 'components' 字段");
        }
        
        if (!componentsNode.isArray()) {
            throw new ConfigMergeException("配置中的 'components' 字段必须是数组类型");
        }

        return (ArrayNode) componentsNode;
    }

    /**
     * 从配置中删除组件
     */
    private void removeComponentFromConfig(ConfigMergeContext context) {
        ArrayNode componentsArray = context.getComponentsArray();
        String moduleId = context.getModuleId();

        log.info("开始删除组件: moduleId={}", moduleId);

        for (int i = 0; i < componentsArray.size(); i++) {
            JsonNode component = componentsArray.get(i);
            if (component.has("id") && moduleId.equals(component.get("id").asText())) {
                componentsArray.remove(i);
                log.info("成功删除组件: moduleId={}", moduleId);
                return;
            }
        }

        // 如果没有找到要删除的组件，记录警告但不抛出异常（可能组件已经被删除）
        log.warn("未找到要删除的组件: moduleId={}", moduleId);
    }

    /**
     * 在配置中更新或新增组件
     */
    private void updateOrAddComponentInConfig(ConfigMergeContext context) {
        ArrayNode componentsArray = context.getComponentsArray();
        SitePageConfigDraftEntity draft = context.getDraft();
        String moduleId = context.getModuleId();

        log.info("开始更新或新增组件: moduleId={}", moduleId);

        // 查找并更新匹配的组件
        for (int i = 0; i < componentsArray.size(); i++) {
            JsonNode component = componentsArray.get(i);
            if (component.has("id") && moduleId.equals(component.get("id").asText())) {
                // 找到匹配的组件，只替换config字段
                ObjectNode componentNode = (ObjectNode) component;
                componentNode.put("config", draft.getConfig());
                log.info("成功更新组件配置: moduleId={}", moduleId);
                return;
            }
        }

        // 如果没有找到匹配的组件，则新增组件
        addNewComponentToConfig(context);
    }

    /**
     * 新增组件到配置中
     */
    private void addNewComponentToConfig(ConfigMergeContext context) {
        ArrayNode componentsArray = context.getComponentsArray();
        SitePageConfigDraftEntity draft = context.getDraft();
        String moduleId = context.getModuleId();

        log.info("开始新增组件: moduleId={}", moduleId);

        // 从数据库获取模块元数据
        SitePageModuleEntity moduleEntity = queryModuleEntity(draft.getPageId(), moduleId);

        // 创建新组件节点
        ObjectNode newComponentNode = createComponentNode(moduleEntity, draft.getConfig());

        // 添加到组件数组中
        componentsArray.add(newComponentNode);

        log.info("成功新增组件: moduleId={}", moduleId);
    }

    /**
     * 查询模块实体
     */
    private SitePageModuleEntity queryModuleEntity(String pageId, String moduleId) {
        // moduleId 现在是主键 id 的字符串形式
        SitePageModuleEntity moduleEntity = moduleEngine.lambdaQuery()
                .eq(SitePageModuleEntity::getId, Long.valueOf(moduleId))
                .eq(SitePageModuleEntity::getPageId, pageId)
                .one();

        if (moduleEntity == null) {
            throw new ConfigMergeException(
                String.format("未找到模块元数据，无法新增组件: pageId=%s, moduleId=%s", pageId, moduleId));
        }

        return moduleEntity;
    }

    /**
     * 创建组件节点
     */
    private ObjectNode createComponentNode(SitePageModuleEntity moduleEntity, String config) {
        try {
            ObjectNode componentNode = (ObjectNode) JsonUtil.readTree("{}");

            // 设置必需字段
            componentNode.put("id", moduleEntity.getId().toString()); // 使用主键 id
            componentNode.put("name", moduleEntity.getModuleName());
            componentNode.put("config", config);
            componentNode.put("component", moduleEntity.getComponent());

            // 设置可选字段
            setOptionalComponentFields(componentNode, moduleEntity);

            return componentNode;

        } catch (Exception e) {
            throw new ConfigMergeException(
                String.format("创建组件节点失败: id=%s, error=%s", moduleEntity.getId(), e.getMessage()), e);
        }
    }

    /**
     * 设置组件的可选字段
     */
    private void setOptionalComponentFields(ObjectNode componentNode, SitePageModuleEntity moduleEntity) {
        // priority 字段已从 site_page_module 表中移除，排序逻辑在 site_page_module_strategy 表中处理

        if (moduleEntity.getDataProvider() != null) {
            componentNode.put("dataProvider", moduleEntity.getDataProvider());
        }

        if (moduleEntity.getDataProviderParams() != null) {
            componentNode.put("dataProviderParams", moduleEntity.getDataProviderParams());
        }
    }
}
