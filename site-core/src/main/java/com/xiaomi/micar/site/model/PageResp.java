package com.xiaomi.micar.site.model;

import com.xiaomi.micar.site.component.Component;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 用来给客户端返回的页面结构
 * 用于描述整个页面的结构和内容
 */
@Data
public class PageResp {
    /**
     * 页面ID
     */
    private String pageId;
    /**
     * 当前页面试验分桶结果，可能有多层，用于客户端打点
     */
    private List<String> expId;

    /**
     * 页面属性
     */
    private PageInfo page;

    /**
     * 搜索框配置，没有则不返回
     */
    private SearchConfig search;

    /**
     * 顶部标签列表，没有则不返回
     */
    private TopTabList topTabList;

    /**
     * 模块列表，key为模块标识，value为模块内容
     */
    private Map<String, Component> modules;

    /**
     * 模块列表
     */
    private List<String> moduleList;

    /**
     * 页面属性
     */
    @Data
    public static class PageInfo {
        /**
         * 页面标题
         */
        private String title;
        /**
         * 页面分组ID
         */
        private String groupKey;
    }

    /**
     * 搜索框配置
     */
    @Data
    public static class SearchConfig {
        /**
         * 搜索图标
         */
        private String icon;

        /**
         * 搜索框占位文本
         */
        private String placeholder;

        /**
         * 搜索页面URL
         */
        private String pageUrl;
    }

    /**
     * 顶部标签列表
     */
    @Data
    public static class TopTabList {
        /**
         * 标签项列表
         */
        private List<TabItem> items;
    }

    /**
     * 标签项
     */
    @Data
    public static class TabItem {
        /**
         * 标签类型：native/h5/rn
         */
        private String type;

        /**
         * 页面路径
         */
        private String pagePath;

        /**
         * 标签文本
         */
        private String text;

        /**
         * 是否选中
         */
        private Boolean selected;
    }
} 
