package com.xiaomi.micar.site.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 站点组件配置属性.
 *
 * <p>此类定义了站点组件的所有配置属性，包括缓存设置和API配置。</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "iccc.site.component")
public class SiteComponentProperties {

    /**
     * 默认缓存刷新间隔（毫秒）：10分钟.
     */
    private static final long DEFAULT_CACHE_REFRESH_INTERVAL = 600000;

    /**
     * 默认本地缓存最大数量.
     */
    private static final int DEFAULT_LOCAL_CACHE_MAX_SIZE = 1000;



    /**
     * 默认LRU缓存最大容量.
     */
    private static final int DEFAULT_LRU_CACHE_MAX_SIZE = 5000;



    /**
     * 是否启用组件功能.
     */
    private boolean enabled = true;

    /**
     * 是否启用缓存.
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存刷新间隔（毫秒）.
     * 默认10分钟。
     */
    private long cacheRefreshInterval = DEFAULT_CACHE_REFRESH_INTERVAL;

    /**
     * 缓存配置.
     */
    private Cache cache = new Cache();

    /**
     * API配置.
     */
    private ApiConfig api = new ApiConfig();

    /**
     * 缓存配置类.
     */
    @Data
    public static class Cache {
        /**
         * 是否启用本地缓存
         */
        private boolean localCacheEnabled = true;

        /**
         * 本地缓存最大数量
         */
        private int localCacheMaxSize = DEFAULT_LOCAL_CACHE_MAX_SIZE;



        /**
         * 是否启用LRU缓存淘汰策略
         */
        private boolean lruCacheEnabled = true;

        /**
         * LRU缓存的最大容量.
         * 默认为5000个组合。
         */
        private int lruCacheMaxSize = DEFAULT_LRU_CACHE_MAX_SIZE;




    }

    /**
     * API配置类.
     */
    @Data
    public static class ApiConfig {
        /**
         * API域名配置
         */
        private String domain = "internal-staging.car.miui.srv";

        /**
         * 是否启用HTTPS
         */
        private boolean useHttps = false;

        /**
         * TabFeeds组件配置
         */
        private ComponentApiConfig tabFeeds = new ComponentApiConfig("/api/internal/site/highlight/v1/list", "tabFeeds");

        /**
         * OfficeNews组件配置
         */
        private ComponentApiConfig officeNews = new ComponentApiConfig("/api/internal/site/overall/official/v1/list", "officeNews");

        /**
         * 查询帖子点赞关系
         */
        private ComponentApiConfig postRelation = new ComponentApiConfig("/api/internal/site/post/v1/relation", "postRelation");

        /**
         * 获取完整域名（包含协议）
         *
         * @return 完整域名
         */
        public String getFullDomain() {
            return (useHttps ? "https://" : "http://") + domain;
        }
    }

    /**
     * 组件API配置类
     */
    @Data
    public static class ComponentApiConfig {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * API路径
         */
        private String path;

        /**
         * 组件名称
         */
        private String name;

        /**
         * 标签ID
         * tabId=all 全部
         * 走进工厂 zjgc
         * 公益活动 gyhd
         * 车友活动 cyhd
         */
        private String tabId;

        /**
         * 每页数量限制
         * 不传默认10
         * 最小1
         * 最大20
         */
        private Integer limit;

        /**
         * 带路径的构造函数
         * @param name 组件名称
         * @param path API路径
         */
        public ComponentApiConfig(String path, String name) {
            this.enabled = true;
            this.path = path;
            this.name = name;
        }
    }
}
