package com.xiaomi.micar.site.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.validation.constraints.NotNull;

/**
 * 审核类型
 * 定义不同维度的审核处理逻辑
 *
 * <AUTHOR>
 * @since 2025/04/01
 */
@AllArgsConstructor
@Getter
public enum DraftType {

    TOPIC_PAGE_CONTENT(1, "page", true, "专题页内容审核"),
    MODULE_CONTENT(2, "module", false, "模块内容审核"),
    RECORD_CONTENT(3, "module", false, "记录内容审核"),
    PAGE_STRATEGY_MODIFY(4, "page", true, "模块排序审核");

    private final int code;
    private final String contentType;
    private final boolean isPageLevel; // 是否为页面级别审核
    private final String desc;


    @NotNull
    public static DraftType getByCode(int code) {
        for (DraftType draftType : DraftType.values()) {
            if (draftType.getCode() == code) {
                return draftType;
            }
        }
        throw new RuntimeException("unknown draft type code: " + code);
    }

    /**
     * 判断是否为页面级别审核类型
     */
    public static boolean isPageLevel(Integer code) {
        if (code == null) {
            return false;
        }
        try {
            return getByCode(code).isPageLevel();
        } catch (RuntimeException e) {
            return false;
        }
    }

    /**
     * 获取所有页面级别审核类型的code
     */
    public static int[] getPageLevelCodes() {
        return java.util.Arrays.stream(values())
                .filter(DraftType::isPageLevel)
                .mapToInt(DraftType::getCode)
                .toArray();
    }

    /**
     * 获取所有模块级别审核类型的code
     */
    public static int[] getModuleLevelCodes() {
        return java.util.Arrays.stream(values())
                .filter(type -> !type.isPageLevel())
                .mapToInt(DraftType::getCode)
                .toArray();
    }

}
