package com.xiaomi.micar.site.enums;

import lombok.Getter;

/**
 * 实验状态枚举
 * 用于表示实验的不同状态
 */
@Getter
public enum ExperimentStatus {

    /**
     * 未知状态
     */
    UNKNOWN(0, "未知"),

    /**
     * 激活状态
     */
    ACTIVE(1, "激活"),

    /**
     * 暂停状态
     */
    PAUSED(2, "暂停"),

    /**
     * 结束状态
     */
    ENDED(3, "结束"),

    /**
     * 草稿状态
     */
    DRAFT(4, "草稿");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code        状态码
     * @param description 状态描述
     */
    ExperimentStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没有匹配则返回UNKNOWN
     */
    public static ExperimentStatus fromCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }

        for (ExperimentStatus status : ExperimentStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }

        return UNKNOWN;
    }

    /**
     * 判断是否为激活状态
     *
     * @return 如果是激活状态返回true，否则返回false
     */
    public boolean isActive() {
        return this == ACTIVE;
    }
}
