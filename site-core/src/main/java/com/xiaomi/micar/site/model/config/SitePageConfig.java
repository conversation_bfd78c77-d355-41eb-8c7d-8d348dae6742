package com.xiaomi.micar.site.model.config;

import com.xiaomi.micar.site.enums.PageCodeType;
import lombok.Data;

import java.util.Date;

/**
 * 页面配置表实体类
 * 代表页面级别的配置
 *
 * 对应数据库表: site_page_config
 */
@Data
public class SitePageConfig {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 页面配置，JSON格式
     */
    private String config;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 人群策略状态：1=开启，0=关闭
     */
    private Integer strategyState;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取场景代码类型枚举
     * @return 场景代码类型枚举
     */
    public PageCodeType getSceneCodeType() {
        return PageCodeType.fromCode(pageId);
    }
}
