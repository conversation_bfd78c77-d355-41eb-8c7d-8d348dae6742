package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SitePageConfigEntity;
import com.xiaomi.micar.site.dao.mapper.SitePageConfigMapper;
import org.springframework.stereotype.Component;

/**
 * 页面配置engine
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Component
public class SitePageConfigEngine extends ServiceImpl<SitePageConfigMapper, SitePageConfigEntity> {

    public SitePageConfigEntity getByPageAndVersion(String pageId, Integer version) {
        return this.getOne(new LambdaQueryWrapper<>(SitePageConfigEntity.class)
                .eq(SitePageConfigEntity::getPageId, pageId)
                .eq(SitePageConfigEntity::getVersion, version));
    }

    /**
     * 获取指定页面的最大版本号
     *
     * @param pageId 页面ID
     * @return 最大版本号，如果没有记录则返回null
     */
    public Integer selectMaxVersion(String pageId) {
        return this.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, pageId)
                .orderByDesc(SitePageConfigEntity::getVersion)
                .last("LIMIT 1")
                .oneOpt()
                .map(SitePageConfigEntity::getVersion)
                .orElse(null);
    }

}
