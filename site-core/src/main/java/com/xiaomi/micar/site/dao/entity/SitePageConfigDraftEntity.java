package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaomi.micar.site.enums.ConfigStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面配置表实体类
 * 代表页面级别的配置
 * <p>
 * 对应数据库表: site_page_config_draft
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_page_config_draft", keepGlobalPrefix = true)
public class SitePageConfigDraftEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer type;

    private String groupKey;

    /**
     * 页面id
     */
    private String pageId;

    /**
     * 模块id，只对type=module有效
     */
    private Integer moduleId;

    private String name;

    /**
     * 修改内容
     */
    private String config;

    private String remark;

    private Integer priority;

    private Long exposeFrom;

    private Long exposeTo;

    /**
     * 对应发布的id（site_page_config.id 或 site_page_record.id ）
     */
    private Long releaseId;

    private String operatorId;

    private String operatorName;

    /**
     * 草稿状态
     * {@link  ConfigStatus}
     */
    private Integer status;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableLogic
    private Integer deleted;

}
