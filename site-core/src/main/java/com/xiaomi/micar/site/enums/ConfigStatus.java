package com.xiaomi.micar.site.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.validation.constraints.NotNull;

/**
 * 页面配置状态
 *
 * <AUTHOR>
 * @since 2025/04/01
 */
@AllArgsConstructor
@Getter
public enum ConfigStatus {

    DRAFT(0, "草稿或下线", true, true, 1),
    AUDITING(1, "审核中", false, false, 2),
    AUDITED(2, "已审核", false, false, 3),
    DEPLOYING(3, "部署中", false, false, 4),
    DEPLOYED(4, "已发布", true, false, 8),
    OFFLINE_AUDIT(8, "下线审核中", false, false, 0);

    private final int code;

    private final String desc;

    private final boolean editable;
    private final boolean deletable;

    private final int nextStatus;


    @NotNull
    public static ConfigStatus getByCode(int code) {
        for (ConfigStatus status : ConfigStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new RuntimeException("unknown config status code: " + code);
    }

}
