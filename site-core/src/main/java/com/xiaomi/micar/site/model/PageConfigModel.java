package com.xiaomi.micar.site.model;

import com.xiaomi.micar.site.component.Component;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 页面配置模型
 * 用于表示页面配置数据，包括页面基本信息、组件、实验和用户组配置
 */
@Data
public class PageConfigModel {
    /** 页面ID */
    private String pageId;
    /** 页面标题 */
    private String title;
    /** 页面名称 */
    private String pageName;
    /** 人群策略状态：1=开启，0=关闭 */
    private Integer strategyState;
    /** 页面组件列表 */
    private List<ComponentModel> components;
    /** 页面用户组配置 */
    private List<GroupConfigModel> groupConfig;
    /** 页面实验配置 */
    private List<ExperimentModel> experiments;
    /** 其他页面配置参数 */
    private Map<String, Object> additionalProperties;
    /** 配置版本号（由SitePageConfig/SiteExpConfig/SiteExpBuckets三个模型的版本组合而成）*/
    private String version;

    /**
     * 组件模型
     */
    @Data
    public static class ComponentModel {
        /** 组件ID，模块表是 */
        private String id;
        /** 组件名称 */
        private String name;
        /** 组件类型 */
        private String componentType;
        /** 组件数据提供者 */
        private String dataProvider;
        /** 数据提供者参数，JSON字符串形式 */
        private String dataProviderParams;
        /** 组件配置对象 */
        private Component componentInfo;
        /**
         * 组件模板
         */
        private String template;
        /** 标题标签 */
        private String titleTag;
    }

    /**
     * 用户组配置模型
     */
    @Data
    public static class GroupConfigModel {
        /** 用户组键 */
        private String strategy;
        /** 用户组名称 */
        private String groupName;
        /** 组件配置 */
        private List<String> components;
    }

    /**
     * 实验模型
     */
    @Data
    public static class ExperimentModel {
        /** 实验ID */
        private String experimentId;
        /** 实验名称 */
        private String name;
        /** 实验状态 */
        private Integer status;
        /** 开始时间 */
        private Long startTime;
        /** 结束时间 */
        private Long endTime;
        /** 试验条件类型：0全部，1百分比，2文件人群包上传，3人群包id*/
        private String conditionType;
        /** 试验进入条件 */
        private String conditionRule;
        /** 实验桶 */
        private List<BucketModel> buckets;
        /** 其他实验参数 */
        private Map<String, Object> additionalProperties;
    }

    /**
     * 实验桶模型
     */
    @Data
    public static class BucketModel {
        /** 桶ID */
        private String bucketId;
        /** 页面ID */
        private String pageId;
        /** 分组比例 0-100 */
        private String ratio;
        /** 页面信息，用于页面级实验 */
        private Map<String, Object> pageInfo;
        /** 组件信息列表，每个组件对应一个Component对象 */
        private List<Component> componentInfoList;
        /**
         * 根据流量条件计算出来的分桶集合
         * 例如 conditionRule : {"quota":24}
         *
         * 一共 1000个桶、假如 ExperimentModel 下面有三个实验分组。 流量分别是 20%, 30%, 50%
         * 那么这三个 BucketModel 的 bucketIds 分别是  1000*0.2个  1000*0.3个  1000*0.5个
         */
        private List<String> bucketIds;
    }

    /**
     * 图片信息模型
     */
    @Data
    public static class ImageInfo {
        /** 图片地址 */
        private String src;
        /** 图片宽度 */
        private Integer width;
        /** 图片高度 */
        private Integer height;
    }
}
