package com.xiaomi.micar.site.convert;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Iterator;

/**
 * JSON 配置合并工具类
 * 用于合并模板配置和模块配置
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
public class JsonConfigMergeUtil {

    /**
     * 合并两个 JSON 配置
     * 模块配置会覆盖模板配置中的同名字段
     *
     * @param templateConfig 模板配置JSON字符串（基础配置）
     * @param moduleConfig   模块配置JSON字符串（覆盖配置）
     * @return 合并后的JSON字符串
     */
    public static String mergeConfigs(String templateConfig, String moduleConfig) {
        try {
            // 如果模板配置为空，直接返回模块配置
            if (templateConfig == null || templateConfig.trim().isEmpty()) {
                return moduleConfig != null ? moduleConfig : "";
            }

            // 如果模块配置为空，直接返回模板配置
            if (moduleConfig == null || moduleConfig.trim().isEmpty()) {
                return templateConfig;
            }

            // 解析 JSON
            JsonNode templateNode = JsonUtil.readTree(templateConfig);
            JsonNode moduleNode = JsonUtil.readTree(moduleConfig);

            // 如果解析失败，返回模块配置（优先级更高）
            if (templateNode == null) {
                log.warn("模板配置JSON解析失败，使用模块配置: templateConfig={}", templateConfig);
                return moduleConfig;
            }

            if (moduleNode == null) {
                log.warn("模块配置JSON解析失败，使用模板配置: moduleConfig={}", moduleConfig);
                return templateConfig;
            }

            // 执行深度合并
            JsonNode mergedNode = deepMerge(templateNode, moduleNode);

            return JsonUtil.toJSONString(mergedNode);

        } catch (Exception e) {
            log.error("JSON配置合并失败，返回模块配置: templateConfig={}, moduleConfig={}, error={}",
                    templateConfig, moduleConfig, e.getMessage(), e);
            return moduleConfig != null ? moduleConfig : "";
        }
    }

    /**
     * 深度合并两个 JsonNode
     * 模块配置会覆盖模板配置中的同名字段
     *
     * @param templateNode 模板配置节点（基础配置）
     * @param moduleNode   模块配置节点（覆盖配置）
     * @return 合并后的节点
     */
    private static JsonNode deepMerge(JsonNode templateNode, JsonNode moduleNode) {
        // 如果模块节点不是对象类型，直接返回模块节点（完全覆盖）
        if (!moduleNode.isObject()) {
            return moduleNode;
        }

        // 如果模板节点不是对象类型，直接返回模块节点
        if (!templateNode.isObject()) {
            return moduleNode;
        }

        // 创建合并结果节点，从模板节点开始
        ObjectNode mergedNode;
        try {
            mergedNode = (ObjectNode) JsonUtil.readTree(JsonUtil.toJSONString(templateNode));
        } catch (Exception e) {
            log.error("创建合并节点失败: {}", e.getMessage(), e);
            return moduleNode;
        }

        // 遍历模块节点的所有字段
        Iterator<String> fieldNames = moduleNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            JsonNode moduleValue = moduleNode.get(fieldName);
            JsonNode templateValue = mergedNode.get(fieldName);

            if (templateValue != null && templateValue.isObject() && moduleValue.isObject()) {
                // 如果两个值都是对象，递归合并
                JsonNode mergedValue = deepMerge(templateValue, moduleValue);
                mergedNode.set(fieldName, mergedValue);
            } else if (templateValue != null && templateValue.isArray() && moduleValue.isArray()) {
                // 如果两个值都是数组，合并数组（模块配置的数组会完全覆盖模板配置的数组）
                mergedNode.set(fieldName, moduleValue);
            } else {
                // 其他情况，模块配置直接覆盖模板配置
                mergedNode.set(fieldName, moduleValue);
            }
        }

        return mergedNode;
    }

    /**
     * 验证 JSON 字符串是否有效
     *
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }

        try {
            JsonUtil.readTree(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 格式化 JSON 字符串
     * 如果格式化失败，返回原字符串
     *
     * @param jsonString JSON字符串
     * @return 格式化后的JSON字符串
     */
    public static String formatJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            JsonNode node = JsonUtil.readTree(jsonString);
            return JsonUtil.toJSONString(node);
        } catch (Exception e) {
            log.warn("JSON格式化失败，返回原字符串: jsonString={}, error={}", jsonString, e.getMessage());
            return jsonString;
        }
    }
}
