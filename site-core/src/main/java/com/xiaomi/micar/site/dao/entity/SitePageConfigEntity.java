package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面配置表实体类
 * 代表页面级别的配置
 * <p>
 * 对应数据库表: site_page_config
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_page_config", keepGlobalPrefix = true)
public class SitePageConfigEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 页面配置，JSON格式
     */
    private String config;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

}
