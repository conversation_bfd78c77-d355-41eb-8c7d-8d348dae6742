package com.xiaomi.micar.site.filter;


import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/6
 */
public class SiteBizException extends RuntimeException {
    private final RtCode rtCode;
    private String message;

    public SiteBizException(RtCode rtCode) {
        this.rtCode = rtCode;
    }

    public SiteBizException(RtCode rtCode, Exception e) {
        super(e);
        this.rtCode = rtCode;
    }

    public SiteBizException(RtCode rtCode, String message) {
        this.rtCode = rtCode;
        this.message = message;
    }

    public RtCode getRtCode() {
        return rtCode;
    }

    @Override
    public String getMessage() {
        return Optional.ofNullable(message).orElse(rtCode.getMessage());
    }
}
