package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.dao.mapper.SitePageInfoMapper;
import com.xiaomi.micar.site.enums.LoadTypeEnum;
import com.xiaomi.micar.site.enums.PageTypeEnum;
import com.xiaomi.micar.site.enums.StrategyStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 页面信息engine
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Slf4j
@Component
public class SitePageInfoEngine extends ServiceImpl<SitePageInfoMapper, SitePageInfoEntity> {

    /**
     * 根据页面ID获取页面信息
     */
    public SitePageInfoEntity getByPageId(String pageId) {
        return lambdaQuery()
                .eq(SitePageInfoEntity::getPageId, pageId)
                .one();
    }

    /**
     * 获取或创建页面信息
     */
    public SitePageInfoEntity getOrCreatePageInfo(String pageId) {
        return getOrCreatePageInfo(pageId, PageTypeEnum.TOPIC_PAGE, LoadTypeEnum.PAGE_DIRECT);
    }

    /**
     * 获取或创建页面信息（支持指定页面类型和加载方式）
     */
    public SitePageInfoEntity getOrCreatePageInfo(String pageId, PageTypeEnum pageType, LoadTypeEnum loadType) {
        SitePageInfoEntity pageInfo = getByPageId(pageId);

        if (pageInfo == null) {
            // 新建页面：创建页面信息记录
            pageInfo = new SitePageInfoEntity();
            pageInfo.setPageId(pageId);
            pageInfo.setPageName(pageId);
            pageInfo.setPageType(pageType.getCode());
            pageInfo.setLoadType(loadType.getCode());
            pageInfo.setVersion(0);
            pageInfo.setStrategyState(StrategyStateEnum.DISABLED.getCode());
            pageInfo.setStatus(1); // 默认上线状态

            if (!save(pageInfo)) {
                throw new RuntimeException("创建页面信息失败: pageId=" + pageId);
            }

            log.info("创建新页面信息: pageId={}, pageType={}, loadType={}", pageId, pageType.getDesc(), loadType.getDesc());
        }

        return pageInfo;
    }
}
