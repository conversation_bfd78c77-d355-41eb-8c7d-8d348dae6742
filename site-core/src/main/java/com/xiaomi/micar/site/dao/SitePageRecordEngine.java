package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.car.iccc.iccccommonutil.util.IcccCommonDateUtil;
import com.xiaomi.micar.site.dao.entity.SitePageRecordEntity;
import com.xiaomi.micar.site.dao.mapper.SitePageRecordMapper;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 页面配置记录engine
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Component
public class SitePageRecordEngine extends ServiceImpl<SitePageRecordMapper, SitePageRecordEntity> {


    public List<SitePageRecordEntity> getAllExposed(String pageId, String moduleId) {
        long now = IcccCommonDateUtil.getCurrentTimeMillis();
        List<SitePageRecordEntity> list = this.list(new LambdaQueryWrapper<>(SitePageRecordEntity.class)
                .eq(SitePageRecordEntity::getPageId, pageId)
                .eq(SitePageRecordEntity::getModuleId, moduleId)
                .orderByAsc(SitePageRecordEntity::getPriority)
                .orderByDesc(SitePageRecordEntity::getUpdateTime)); // 优先级相同时，按更新时间倒序（最近的在前）
        return list.stream()
                .filter(record -> shouldExpose(record, now))
                .collect(Collectors.toList());
    }

    public List<SitePageRecordEntity> getExposeList(String pageId, String moduleId) {
        long now = IcccCommonDateUtil.getCurrentTimeMillis();
        List<SitePageRecordEntity> list = this.list(new LambdaQueryWrapper<>(SitePageRecordEntity.class)
                .eq(SitePageRecordEntity::getPageId, pageId)
                .eq(SitePageRecordEntity::getModuleId, moduleId)
                .orderByAsc(SitePageRecordEntity::getPriority)
                .orderByDesc(SitePageRecordEntity::getUpdateTime)); // 优先级相同时，按更新时间倒序（最近的在前）

        return list.stream()
                .filter(record -> shouldExpose(record, now))
                .collect(Collectors.groupingBy(SitePageRecordEntity::getPriority)) // 按优先级分组
                .values()
                .stream()
                .map(records -> records.stream().max(Comparator.comparing(SitePageRecordEntity::getUpdateTime)).get()) // 每个优先级组取第一个（即最近更新的）
                .sorted(Comparator.comparing(SitePageRecordEntity::getPriority)) // 重新按优先级排序
                .collect(Collectors.toList());
    }

    public List<SitePageRecordEntity> findByDraftIds(Collection<Long> draftIds) {
        return this.list(new LambdaQueryWrapper<>(SitePageRecordEntity.class)
                .in(SitePageRecordEntity::getId, draftIds));
    }

    private boolean shouldExpose(SitePageRecordEntity record, long now) {
        return (record.getExposeFrom() == 0 || record.getExposeFrom() <= now) && (record.getExposeTo() == 0 || record.getExposeTo() >= now);
    }
}
