package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面缓存存储表实体类
 * 用于在MySQL中存储页面缓存数据，Nacos只存储批次号进行通知
 *
 * <AUTHOR>
 * @since 2025/05/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_page_cache_storage", keepGlobalPrefix = true)
public class SitePageCacheStorageEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 组合键（页面+策略+实验组合的唯一标识）
     */
    private String combinationKey;

    /**
     * 配置内容（JSON格式的PageRespV2）
     */
    private String config;

    /**
     * 批次ID（用于标识本次更新的批次）
     */
    private String batchId;

    /**
     * 版本号（1=V1版本，2=V2版本）
     */
    private Integer version;

    /**
     * 配置版本号（后台配置的版本）
     */
    private Integer configVersion;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    /**
     * 逻辑删除标记
     */
    @JsonIgnore
    private Integer deleted;
}
