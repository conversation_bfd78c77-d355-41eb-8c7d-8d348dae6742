package com.xiaomi.micar.site.core.monitoring;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 缓存监控注解
 * 用于自动收集缓存相关的性能指标
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheMonitoring {

    /**
     * 监控指标名称
     * 如果为空，则使用方法名作为指标名称
     */
    String value() default "";

    /**
     * 监控指标前缀
     * 默认为 "cache"
     */
    String prefix() default "cache";

    /**
     * 额外的标签
     * 格式: ["key1=value1", "key2=value2"]
     */
    String[] tags() default {};

    /**
     * 是否记录方法执行耗时
     */
    boolean recordDuration() default true;

    /**
     * 是否记录方法调用次数
     */
    boolean recordCount() default true;

    /**
     * 是否记录方法成功率
     * 基于是否抛出异常来判断成功/失败
     */
    boolean recordSuccessRate() default true;

    /**
     * 是否记录返回值大小
     * 仅对字符串返回值有效，记录字符串长度
     */
    boolean recordResponseSize() default false;

    /**
     * 监控类型
     */
    MonitoringType type() default MonitoringType.PERFORMANCE;

    /**
     * 监控类型枚举
     */
    enum MonitoringType {
        /**
         * 性能监控：耗时、QPS等
         */
        PERFORMANCE,
        
        /**
         * 业务监控：命中率、成功率等
         */
        BUSINESS,
        
        /**
         * 资源监控：内存、线程池等
         */
        RESOURCE,
        
        /**
         * 错误监控：异常率、失败次数等
         */
        ERROR
    }
}
