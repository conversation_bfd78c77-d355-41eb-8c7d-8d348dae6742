package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.car.iccc.iccccommonutil.util.IcccCommonDateUtil;
import com.xiaomi.micar.site.dao.entity.SiteExpConfigEntity;
import com.xiaomi.micar.site.dao.mapper.SiteExpConfigMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 试验配置engine
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Component
public class SiteExpConfigEngine extends ServiceImpl<SiteExpConfigMapper, SiteExpConfigEntity> {

    /**
     * 获取所有活跃状态的试验列表
     *
     * @return 试验列表
     */
    public List<SiteExpConfigEntity> getActiveExpConfigList() {
        long now = IcccCommonDateUtil.getCurrentTimeMillis();
        return this.list(new LambdaQueryWrapper<>(SiteExpConfigEntity.class)
                .eq(SiteExpConfigEntity::getStatus, 1)
                .le(SiteExpConfigEntity::getStartTime, now)
                .ge(SiteExpConfigEntity::getEndTime, now)
        );
    }

}
