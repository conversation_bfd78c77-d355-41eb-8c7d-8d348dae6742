package com.xiaomi.micar.site.enums;

/**
 * 人群策略状态枚举
 * 
 * <AUTHOR>
 * @since 2025/06/29
 */
public enum StrategyStateEnum {

    /**
     * 关闭人群策略
     */
    DISABLED(0, "关闭"),

    /**
     * 开启人群策略
     */
    ENABLED(1, "开启");

    private final Integer code;
    private final String desc;

    StrategyStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，未找到返回null
     */
    public static StrategyStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StrategyStateEnum strategyState : values()) {
            if (strategyState.getCode().equals(code)) {
                return strategyState;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的状态码
     *
     * @param code 状态码
     * @return true=有效，false=无效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 检查是否开启人群策略
     *
     * @param code 状态码
     * @return true=开启，false=关闭或无效
     */
    public static boolean isEnabled(Integer code) {
        return ENABLED.getCode().equals(code);
    }
}