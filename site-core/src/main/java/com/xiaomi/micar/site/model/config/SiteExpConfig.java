package com.xiaomi.micar.site.model.config;

import lombok.Data;

import java.util.Date;

/**
 * 针对页面的实验表实体类
 *
 * 对应数据库表: site_exp_config
 */
@Data
public class SiteExpConfig {
    /**
     * 主键ID，实验ID
     */
    private Long id;

    /**
     * 实验名称
     */
    private String name;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 实验条件类型
     */
    private String conditionType;

    /**
     * 实验进入条件
     */
    private String conditionRule;

    /**
     * 实验开始时间
     */
    private Long startTime;

    /**
     * 实验结束时间
     */
    private Long endTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态
     * @see com.mi.car.iccc.starter.site.enums.ExperimentStatus
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取实验ID（兼容旧代码）
     *
     * @return 实验ID字符串
     */
    public String getExperimentId() {
        return id != null ? String.valueOf(id) : null;
    }
}
