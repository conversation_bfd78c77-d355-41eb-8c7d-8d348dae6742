package com.xiaomi.micar.site.convert;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 埋点追踪配置工具类
 * 负责处理模块的埋点配置合并逻辑
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Slf4j
public class TrackConfigUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String TRACK_FIELD_NAME = "_track";

    /**
     * 将埋点配置合并到模块配置中
     * 
     * @param moduleConfig 原始模块配置JSON字符串
     * @param trackConfig 埋点配置JSON字符串
     * @return 合并后的配置JSON字符串
     */
    public static String mergeTrackConfig(String moduleConfig, String trackConfig) {
        // 如果埋点配置为空，直接返回原配置
        if (!StringUtils.hasText(trackConfig)) {
            return moduleConfig;
        }

        // 如果模块配置为空，创建空对象
        if (!StringUtils.hasText(moduleConfig)) {
            moduleConfig = "{}";
        }

        try {
            // 解析模块配置
            JsonNode moduleNode = objectMapper.readTree(moduleConfig);
            if (!moduleNode.isObject()) {
                log.warn("模块配置不是有效的JSON对象，跳过埋点配置合并: moduleConfig={}", moduleConfig);
                return moduleConfig;
            }

            // 解析埋点配置
            JsonNode trackNode = objectMapper.readTree(trackConfig);
            if (!trackNode.isObject()) {
                log.warn("埋点配置不是有效的JSON对象，跳过埋点配置合并: trackConfig={}", trackConfig);
                return moduleConfig;
            }

            // 创建新的配置对象
            ObjectNode resultNode = (ObjectNode) moduleNode.deepCopy();
            
            // 添加 _track 字段
            resultNode.set(TRACK_FIELD_NAME, trackNode);

            String result = objectMapper.writeValueAsString(resultNode);
            log.debug("埋点配置合并成功: moduleConfig={}, trackConfig={}, result={}", 
                    moduleConfig, trackConfig, result);

            return result;

        } catch (Exception e) {
            log.error("埋点配置合并失败，返回原配置: moduleConfig={}, trackConfig={}, error={}", 
                    moduleConfig, trackConfig, e.getMessage(), e);
            return moduleConfig;
        }
    }

    /**
     * 从配置中提取埋点信息
     * 
     * @param config 包含埋点信息的配置JSON字符串
     * @return 埋点配置JSON字符串，如果不存在则返回null
     */
    public static String extractTrackConfig(String config) {
        if (!StringUtils.hasText(config)) {
            return null;
        }

        try {
            JsonNode configNode = objectMapper.readTree(config);
            if (!configNode.isObject()) {
                return null;
            }

            JsonNode trackNode = configNode.get(TRACK_FIELD_NAME);
            if (trackNode == null || trackNode.isNull()) {
                return null;
            }

            return objectMapper.writeValueAsString(trackNode);

        } catch (Exception e) {
            log.warn("提取埋点配置失败: config={}, error={}", config, e.getMessage());
            return null;
        }
    }

    /**
     * 验证埋点配置格式是否正确
     * 
     * @param trackConfig 埋点配置JSON字符串
     * @return 是否为有效的埋点配置
     */
    public static boolean isValidTrackConfig(String trackConfig) {
        if (!StringUtils.hasText(trackConfig)) {
            return true; // 空配置认为是有效的
        }

        try {
            JsonNode trackNode = objectMapper.readTree(trackConfig);
            return trackNode.isObject();
        } catch (Exception e) {
            log.debug("埋点配置格式验证失败: trackConfig={}, error={}", trackConfig, e.getMessage());
            return false;
        }
    }

    /**
     * 创建默认的埋点配置
     * 
     * @param sectionId 区域ID
     * @param sectionName 区域名称
     * @return 默认埋点配置JSON字符串
     */
    public static String createDefaultTrackConfig(String sectionId, String sectionName) {
        try {
            ObjectNode trackNode = objectMapper.createObjectNode();
            
            if (StringUtils.hasText(sectionId)) {
                trackNode.put("sectionId", sectionId);
            }
            
            if (StringUtils.hasText(sectionName)) {
                trackNode.put("sectionName", sectionName);
            }

            return objectMapper.writeValueAsString(trackNode);

        } catch (Exception e) {
            log.error("创建默认埋点配置失败: sectionId={}, sectionName={}, error={}", 
                    sectionId, sectionName, e.getMessage(), e);
            return "{}";
        }
    }

    /**
     * 移除配置中的埋点信息
     * 
     * @param config 包含埋点信息的配置JSON字符串
     * @return 移除埋点信息后的配置JSON字符串
     */
    public static String removeTrackConfig(String config) {
        if (!StringUtils.hasText(config)) {
            return config;
        }

        try {
            JsonNode configNode = objectMapper.readTree(config);
            if (!configNode.isObject()) {
                return config;
            }

            ObjectNode resultNode = (ObjectNode) configNode.deepCopy();
            resultNode.remove(TRACK_FIELD_NAME);

            return objectMapper.writeValueAsString(resultNode);

        } catch (Exception e) {
            log.warn("移除埋点配置失败，返回原配置: config={}, error={}", config, e.getMessage());
            return config;
        }
    }

    /**
     * 检查配置中是否包含埋点信息
     * 
     * @param config 配置JSON字符串
     * @return 是否包含埋点信息
     */
    public static boolean hasTrackConfig(String config) {
        if (!StringUtils.hasText(config)) {
            return false;
        }

        try {
            JsonNode configNode = objectMapper.readTree(config);
            if (!configNode.isObject()) {
                return false;
            }

            JsonNode trackNode = configNode.get(TRACK_FIELD_NAME);
            return trackNode != null && !trackNode.isNull() && trackNode.isObject();

        } catch (Exception e) {
            log.debug("检查埋点配置失败: config={}, error={}", config, e.getMessage());
            return false;
        }
    }

    /**
     * 获取埋点字段名称
     * 
     * @return 埋点字段名称
     */
    public static String getTrackFieldName() {
        return TRACK_FIELD_NAME;
    }
}
