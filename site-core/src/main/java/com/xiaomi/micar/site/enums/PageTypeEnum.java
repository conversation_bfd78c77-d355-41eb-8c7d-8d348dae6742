package com.xiaomi.micar.site.enums;

/**
 * 页面类型枚举
 * 
 * <AUTHOR>
 * @since 2025/06/29
 */
public enum PageTypeEnum {

    /**
     * 专题页
     */
    TOPIC_PAGE(1, "专题页"),

    /**
     * 普通页面
     */
    NORMAL_PAGE(2, "普通页面");

    private final Integer code;
    private final String desc;

    PageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 页面类型码
     * @return 对应的枚举，未找到返回null
     */
    public static PageTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PageTypeEnum pageType : values()) {
            if (pageType.getCode().equals(code)) {
                return pageType;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的页面类型码
     *
     * @param code 页面类型码
     * @return true=有效，false=无效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 检查是否为专题页
     *
     * @param code 页面类型码
     * @return true=专题页，false=其他类型或无效
     */
    public static boolean isTopicPage(Integer code) {
        return TOPIC_PAGE.getCode().equals(code);
    }

    /**
     * 检查是否为普通页面
     *
     * @param code 页面类型码
     * @return true=普通页面，false=其他类型或无效
     */
    public static boolean isNormalPage(Integer code) {
        return NORMAL_PAGE.getCode().equals(code);
    }
}