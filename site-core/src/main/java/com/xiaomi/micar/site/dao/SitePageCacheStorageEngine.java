package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SitePageCacheStorageEntity;
import com.xiaomi.micar.site.dao.mapper.SitePageCacheStorageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 页面缓存存储Engine
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Slf4j
@Component
public class SitePageCacheStorageEngine extends ServiceImpl<SitePageCacheStorageMapper, SitePageCacheStorageEntity> {

    /**
     * 根据页面ID和批次ID获取所有缓存数据
     *
     * @param pageId 页面ID
     * @param batchId 批次ID
     * @return 缓存存储实体列表
     */
    @Transactional(readOnly = true)
    public List<SitePageCacheStorageEntity> getByPageIdAndBatchId(String pageId, String batchId) {
        return list(new LambdaQueryWrapper<SitePageCacheStorageEntity>()
                .eq(SitePageCacheStorageEntity::getPageId, pageId)
                .eq(SitePageCacheStorageEntity::getBatchId, batchId));
    }

    /**
     * 根据批次ID获取所有缓存数据
     *
     * @param batchId 批次ID
     * @return 缓存存储实体列表
     */
    public List<SitePageCacheStorageEntity> getByBatchId(String batchId) {
        return list(new LambdaQueryWrapper<SitePageCacheStorageEntity>()
                .eq(SitePageCacheStorageEntity::getBatchId, batchId));
    }

    /**
     * 批量保存缓存数据
     *
     * @param entities 缓存实体列表
     * @return 是否保存成功
     */
    public boolean saveBatch(List<SitePageCacheStorageEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("缓存实体列表为空，跳过保存");
            return true;
        }

        try {
            return super.saveBatch(entities);
        } catch (Exception e) {
            log.error("批量保存缓存数据失败", e);
            return false;
        }
    }
}
