package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面信息表实体类
 * <p>
 * 对应数据库表: site_page_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_page_info", keepGlobalPrefix = true)
public class SitePageInfoEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String pageId;

    private String pageName;

    private Integer version;

    private String sha256;

    /**
     * 页面加载方式：1=从site_page_config直接获取，2=从site_page_module聚合
     */
    private Integer loadType;

    /**
     * 页面类型：1=专题页，2=普通页面
     */
    private Integer pageType;

    /**
     * 人群策略状态：1=开启，0=关闭
     */
    private Integer strategyState;

    /**
     * 页面上线状态：1=上线，0=未上线，默认1
     */
    private Integer status;

    private Date createTime;

    private Date updateTime;

}
