package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SiteTemplateEntity;
import com.xiaomi.micar.site.dao.mapper.SiteTemplateMapper;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模板配置Engine
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Component
public class SiteTemplateEngine extends ServiceImpl<SiteTemplateMapper, SiteTemplateEntity> {

    /**
     * 根据模板ID获取模板配置
     *
     * @param template 模板ID
     * @return 模板实体
     */
    public SiteTemplateEntity getByTemplate(String template) {
        return lambdaQuery()
                .eq(SiteTemplateEntity::getTemplate, template)
                .eq(SiteTemplateEntity::getStatus, 1) // 只查询启用状态的模板
                .one();
    }

    /**
     * 批量获取模板配置
     *
     * @param templates 模板ID列表
     * @return 模板ID -> 模板实体的映射
     */
    public Map<String, SiteTemplateEntity> getByTemplates(List<String> templates) {
        if (templates == null || templates.isEmpty()) {
            return Collections.emptyMap();
        }

        List<SiteTemplateEntity> templateEntities = lambdaQuery()
                .in(SiteTemplateEntity::getTemplate, templates)
                .eq(SiteTemplateEntity::getStatus, 1) // 只查询启用状态的模板
                .list();

        return templateEntities.stream()
                .collect(Collectors.toMap(
                        SiteTemplateEntity::getTemplate,
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
    }

    /**
     * 获取所有启用状态的模板
     *
     * @return 模板列表
     */
    public List<SiteTemplateEntity> getAllEnabled() {
        return lambdaQuery()
                .eq(SiteTemplateEntity::getStatus, 1)
                .orderByAsc(SiteTemplateEntity::getTemplate)
                .list();
    }
}
