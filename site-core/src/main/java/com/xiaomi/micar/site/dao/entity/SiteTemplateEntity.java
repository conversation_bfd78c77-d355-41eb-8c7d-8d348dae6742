package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 模板配置表实体类
 * 存储模板的基础配置信息
 * <p>
 * 对应数据库表: site_template
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_template", keepGlobalPrefix = true)
public class SiteTemplateEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板ID（唯一标识）
     */
    private String template;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板配置JSON
     * 包含模板的默认配置信息
     */
    private String config;

    /**
     * 模板状态
     * 1: 启用, 0: 禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    private Integer deleted;
}
