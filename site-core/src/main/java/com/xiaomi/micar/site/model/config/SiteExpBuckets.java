package com.xiaomi.micar.site.model.config;

import lombok.Data;

import java.util.Date;

/**
 * 针对页面的实验分桶实体类
 *
 * 对应数据库表: site_exp_bucket
 */
@Data
public class SiteExpBuckets {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 实验ID
     */
    private Long expId;

    /**
     * 分桶ID
     */
    private String bucketId;

    /**
     * 分组比例 0-100
     */
    private String ratio;

    /**
     * 页面配置，JSON格式
     */
    private String config;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取实验ID（兼容旧代码）
     *
     * @return 实验ID字符串
     */
    public String getExperimentId() {
        return expId != null ? String.valueOf(expId) : null;
    }

    public String getName() {
        return bucketId;
    }
}
