package com.xiaomi.micar.site.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xiaomi.micar.site.dao.SitePageModuleEngine;
import com.xiaomi.micar.site.dao.SitePageModuleStrategyEngine;
import com.xiaomi.micar.site.dao.SiteTemplateEngine;
import com.xiaomi.micar.site.dao.entity.SitePageModuleEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleStrategyEntity;
import com.xiaomi.micar.site.dao.entity.SiteTemplateEntity;
import com.xiaomi.micar.site.convert.TrackConfigUtil;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 页面配置聚合服务
 * 负责从基础表中读取数据并合并生成最终的页面配置
 *
 * 核心业务流程：
 * 1. 从 site_page_module 表获取所有模块的基础信息和配置
 * 2. 从 site_page_module_strategy 表获取模块顺序策略，构建 groupConfig
 * 3. 将模块信息和策略配置合并生成最终的页面配置
 *
 * 注意：page_record 表不参与页面配置的构建，它用于10秒缓存构建时
 * 为有 dataProvider 的模块提供具体的子项目数据
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class PageConfigAggregationService {

    @Resource
    private SitePageModuleEngine moduleEngine;

    @Resource
    private SitePageModuleStrategyEngine moduleStrategyEngine;

    @Resource
    private SiteTemplateEngine templateEngine;



    /**
     * 聚合页面配置
     * 从基础表中读取数据并合并生成最终的页面配置
     *
     * @param pageId 页面ID
     * @return 聚合后的页面配置JSON字符串
     */
    public String aggregatePageConfig(String pageId) {
        log.info("开始聚合页面配置: pageId={}", pageId);

        try {
            // 1. 获取页面所有模块
            List<SitePageModuleEntity> modules = getPageModules(pageId);
            if (modules.isEmpty()) {
                log.warn("页面没有配置任何模块: pageId={}", pageId);
                return createEmptyPageConfig();
            }

            // 1.1. 过滤掉已下线的模块（config 为 null 或空）
            List<SitePageModuleEntity> activeModules = modules.stream()
                    .filter(module -> module.getConfig() != null && !module.getConfig().trim().isEmpty())
                    .collect(Collectors.toList());

            if (activeModules.isEmpty()) {
                log.warn("页面所有模块都已下线: pageId={}, 原模块数量={}", pageId, modules.size());
                return createEmptyPageConfig();
            }

            // 2. 获取模块顺序策略
            List<SitePageModuleStrategyEntity> strategies = getModuleStrategies(pageId);

            // 3. 获取模板配置（使用过滤后的活跃模块）
            Map<String, SiteTemplateEntity> templateMap = getTemplateConfigs(activeModules);

            // 4. 构建页面配置（使用过滤后的活跃模块）
            ObjectNode pageConfig = buildPageConfig(activeModules, strategies, templateMap);

            String result = JsonUtil.toJSONString(pageConfig);
            log.info("页面配置聚合成功: pageId={}, 原模块数量={}, 活跃模块数量={}, 策略数量={}, 模板数量={}",
                    pageId, modules.size(), activeModules.size(), strategies.size(), templateMap.size());

            return result;

        } catch (Exception e) {
            log.error("页面配置聚合失败: pageId={}, error={}", pageId, e.getMessage(), e);
            throw new RuntimeException("页面配置聚合失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取页面所有模块
     */
    private List<SitePageModuleEntity> getPageModules(String pageId) {
        return moduleEngine.lambdaQuery()
                .eq(SitePageModuleEntity::getPageId, pageId)
                .orderByAsc(SitePageModuleEntity::getId)  // 按 ID 排序，priority 字段已移除
                .list();
    }

    /**
     * 获取模块顺序策略
     */
    private List<SitePageModuleStrategyEntity> getModuleStrategies(String pageId) {
        return moduleStrategyEngine.getByPageId(pageId);
    }

    /**
     * 获取模板配置
     */
    public Map<String, SiteTemplateEntity> getTemplateConfigs(List<SitePageModuleEntity> modules) {
        // 提取所有模块使用的模板ID
        List<String> templateIds = modules.stream()
                .map(SitePageModuleEntity::getTemplate)
                .filter(template -> template != null && !template.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());

        if (templateIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量获取模板配置
        return templateEngine.getByTemplates(templateIds);
    }

    /**
     * 构建页面配置
     */
    public ObjectNode buildPageConfig(List<SitePageModuleEntity> modules,
                                     List<SitePageModuleStrategyEntity> strategies,
                                     Map<String, SiteTemplateEntity> templateMap) {

        try {
            ObjectNode pageConfig = (ObjectNode) JsonUtil.readTree("{}");

            // 构建基础页面信息 - 根据实际格式调整
            buildBasicPageInfo(pageConfig);

            // 构建策略配置 (groupConfig) 和对应的组件数组
            buildGroupConfigAndComponents(pageConfig, modules, strategies, templateMap);

            return pageConfig;
        } catch (Exception e) {
            throw new RuntimeException("构建页面配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建基础页面信息
     */
    private void buildBasicPageInfo(ObjectNode pageConfig) {
        // 根据实际格式，可能需要添加 title 等字段
        // 这里先保持简单，具体字段可以根据需要调整
    }

    /**
     * 构建策略配置和对应的组件数组
     * 根据策略分组，为每个策略构建对应的模块集合
     */
    private void buildGroupConfigAndComponents(ObjectNode pageConfig,
                                             List<SitePageModuleEntity> modules,
                                             List<SitePageModuleStrategyEntity> strategies,
                                             Map<String, SiteTemplateEntity> templateMap) {
        try {
            // 创建模块ID到模块实体的映射，使用 module_id 字段替代数据库主键
            Map<String, SitePageModuleEntity> moduleMap = modules.stream()
                    .filter(module -> module.getModuleId() != null && !module.getModuleId().trim().isEmpty())
                    .collect(Collectors.toMap(
                            SitePageModuleEntity::getModuleId,
                            Function.identity(),
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            // 构建所有模块的组件数组（用于 components 字段）
            ArrayNode componentsArray = buildAllComponentsArray(modules, templateMap);
            pageConfig.set("components", componentsArray);

            // 构建 groupConfig 数组
            ArrayNode groupConfigArray = (ArrayNode) JsonUtil.readTree("[]");

            if (strategies.isEmpty()) {
                // 如果没有策略配置，创建一个默认的全部用户组
                ObjectNode defaultGroup = createDefaultGroup();
                groupConfigArray.add(defaultGroup);
            } else {
                // 按策略分组
                Map<String, List<SitePageModuleStrategyEntity>> strategyGroups = strategies.stream()
                        .collect(Collectors.groupingBy(SitePageModuleStrategyEntity::getStrategy));

                for (Map.Entry<String, List<SitePageModuleStrategyEntity>> entry : strategyGroups.entrySet()) {
                    String strategy = entry.getKey();
                    List<SitePageModuleStrategyEntity> strategyModules = entry.getValue();

                    // 构建该策略的模块ID列表
                    ArrayNode moduleList = buildStrategyModuleList(strategyModules, moduleMap);

                    // 创建分组配置
                    ObjectNode groupConfig = (ObjectNode) JsonUtil.readTree("{}");
                    groupConfig.set("list", moduleList);
                    groupConfig.put("name", getStrategyDisplayName(strategy));
                    groupConfig.put("strategy", strategy);
                    groupConfig.put("default", true);  // 根据实际业务逻辑设置

                    groupConfigArray.add(groupConfig);
                }
            }

            pageConfig.set("groupConfig", groupConfigArray);

            // 添加 groupId 字段
            pageConfig.put("groupId", "1");  // 根据实际业务逻辑设置

        } catch (Exception e) {
            throw new RuntimeException("构建分组配置和组件数组失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建所有模块的组件数组
     */
    private ArrayNode buildAllComponentsArray(List<SitePageModuleEntity> modules,
                                            Map<String, SiteTemplateEntity> templateMap) {
        try {
            ArrayNode componentsArray = (ArrayNode) JsonUtil.readTree("[]");

            for (SitePageModuleEntity module : modules) {
                ObjectNode component = buildComponent(module, templateMap);
                if (component != null) {  // 只添加非空组件（已下线的模块会返回null）
                    componentsArray.add(component);
                }
            }

            return componentsArray;
        } catch (Exception e) {
            throw new RuntimeException("构建组件数组失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建策略的模块ID列表
     * 从 site_page_module_strategy 表的 config 字段中解析模块ID列表
     */
    private ArrayNode buildStrategyModuleList(List<SitePageModuleStrategyEntity> strategyModules,
                                            Map<String, SitePageModuleEntity> moduleMap) {
        try {
            ArrayNode moduleList = (ArrayNode) JsonUtil.readTree("[]");

            // 按优先级排序
            strategyModules.stream()
                    .sorted((a, b) -> Integer.compare(a.getPriority(), b.getPriority()))
                    .forEach(strategyModule -> {
                        String config = strategyModule.getConfig();
                        if (config != null && !config.trim().isEmpty()) {
                            try {
                                // 解析 config 字段，期望格式为 JSON 数组，如 [1, 2, 3]
                                JsonNode configNode = JsonUtil.readTree(config);
                                if (configNode.isArray()) {
                                    ArrayNode configArray = (ArrayNode) configNode;
                                    for (JsonNode moduleIdNode : configArray) {
                                        String moduleId = moduleIdNode.asText();
                                        if (moduleId != null && !moduleId.trim().isEmpty()) {
                                            // 检查模块是否存在且未下线
                                            SitePageModuleEntity module = moduleMap.get(moduleId);
                                            if (module != null && module.getConfig() != null && !module.getConfig().trim().isEmpty()) {
                                                moduleList.add(moduleId);
                                            } else {
                                                log.debug("跳过不存在或已下线的模块: moduleId={}, strategy={}",
                                                        moduleId, strategyModule.getStrategy());
                                            }
                                        }
                                    }
                                } else {
                                    log.warn("策略配置格式错误，期望数组格式: strategy={}, config={}",
                                            strategyModule.getStrategy(), config);
                                }
                            } catch (Exception e) {
                                log.error("解析策略配置失败: strategy={}, config={}, error={}",
                                        strategyModule.getStrategy(), config, e.getMessage());
                            }
                        } else {
                            log.debug("策略配置为空: strategy={}", strategyModule.getStrategy());
                        }
                    });

            return moduleList;
        } catch (Exception e) {
            throw new RuntimeException("构建策略模块列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建单个组件
     */
    private ObjectNode buildComponent(SitePageModuleEntity module, Map<String, SiteTemplateEntity> templateMap) {
        // 如果模块配置为空，说明模块已下线，跳过该模块
        if (module.getConfig() == null || module.getConfig().trim().isEmpty()) {
            log.debug("跳过已下线的模块: id={}", module.getId());
            return null;
        }

        try {
            ObjectNode component = (ObjectNode) JsonUtil.readTree("{}");

            // 基础模块信息
            component.put("id", module.getModuleId().toString()); // 使用主键 id
            component.put("name", module.getModuleName());
            component.put("component", module.getComponent());
            component.put("template", module.getTemplate());

            // layout 字段，如果为空则设置为 null
            if (module.getTemplate() != null && !module.getTemplate().trim().isEmpty()) {
                component.put("layout", (String) null);  // 根据实际需求设置
            }

            // dataProviderParams 字段，只有在不为空时才设置
            if (module.getDataProviderParams() != null && !module.getDataProviderParams().trim().isEmpty()) {
                component.put("dataProviderParams", module.getDataProviderParams());
            }

            // dataProvider 字段，只有在不为空时才设置
            if (module.getDataProvider() != null && !module.getDataProvider().trim().isEmpty()) {
                component.put("dataProvider", module.getDataProvider());
            }

            // 模块配置合并 - 将模板配置与模块配置进行合并
            String mergedConfig = mergeModuleConfig(module, templateMap);
            if (mergedConfig != null && !mergedConfig.trim().isEmpty()) {
                // config 字段直接设置为字符串
                component.put("config", mergedConfig);
            } else {
                component.put("config", "");
            }

            return component;
        } catch (Exception e) {
            throw new RuntimeException("构建组件失败: id=" + module.getId() + ", error=" + e.getMessage(), e);
        }
    }

    /**
     * 合并模块配置
     * 将模板配置与模块配置进行合并，模块配置优先级更高
     * 同时将模板的 config 作为埋点追踪配置合并到 _track 字段中
     */
    private String mergeModuleConfig(SitePageModuleEntity module, Map<String, SiteTemplateEntity> templateMap) {
        String moduleConfig = module.getConfig();
        String templateId = module.getTemplate();

        // 如果没有模板ID，直接返回模块配置
        if (templateId == null || templateId.trim().isEmpty()) {
            return moduleConfig;
        }

        // 获取模板配置
        SiteTemplateEntity templateEntity = templateMap.get(templateId);
        if (templateEntity == null) {
            log.debug("未找到模板配置: templateId={}, id={}", templateId, module.getId());
            return moduleConfig;
        }

        String templateConfig = templateEntity.getConfig();

        // 第一步：使用模板的 config 作为埋点追踪配置，合并到模块配置的 _track 字段
        String configWithTrack = TrackConfigUtil.mergeTrackConfig(moduleConfig, templateConfig);

        log.debug("埋点配置合并完成: id={}, templateId={}, templateConfig={}, moduleConfig={}, finalConfig={}",
                module.getId(), templateId, templateConfig, moduleConfig, configWithTrack);

        return configWithTrack;
    }



    /**
     * 创建默认分组
     */
    private ObjectNode createDefaultGroup() {
        try {
            ObjectNode defaultGroup = (ObjectNode) JsonUtil.readTree("{}");
            ArrayNode emptyList = (ArrayNode) JsonUtil.readTree("[]");

            defaultGroup.set("list", emptyList);
            defaultGroup.put("name", "全部用户");
            defaultGroup.put("strategy", "all");
            defaultGroup.put("default", true);

            return defaultGroup;
        } catch (Exception e) {
            throw new RuntimeException("创建默认分组失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取策略显示名称
     */
    private String getStrategyDisplayName(String strategy) {
        switch (strategy) {
            case "all":
                return "全部用户";
            case "car_owner":
                return "车主";
            default:
                return strategy;
        }
    }

    /**
     * 创建空的页面配置
     */
    public String createEmptyPageConfig() {
        try {
            ObjectNode emptyConfig = (ObjectNode) JsonUtil.readTree("{}");

            // 设置空的组件数组
            emptyConfig.set("components", (ArrayNode) JsonUtil.readTree("[]"));

            // 设置默认的 groupConfig
            ArrayNode groupConfigArray = (ArrayNode) JsonUtil.readTree("[]");
            ObjectNode defaultGroup = createDefaultGroup();
            groupConfigArray.add(defaultGroup);
            emptyConfig.set("groupConfig", groupConfigArray);

            // 设置 groupId
            emptyConfig.put("groupId", "1");

            return JsonUtil.toJSONString(emptyConfig);
        } catch (Exception e) {
            throw new RuntimeException("创建空页面配置失败: " + e.getMessage(), e);
        }
    }
}
