package com.xiaomi.micar.site.filter;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/1/6
 */
@Getter
public enum RtCode {
    SUCCESS(0, "成功"),
    ERROR(500, "服务器内部错误");

    private final int code;
    private final String message;

    RtCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    private static final Map<Integer, RtCode> ENUM_MAP = new HashMap<>();

    static {
        for (RtCode rtCode : RtCode.values()) {
            ENUM_MAP.put(rtCode.getCode(), rtCode);
        }
    }

    public static RtCode getByCode(int code) {
        return ENUM_MAP.get(code);
    }
}