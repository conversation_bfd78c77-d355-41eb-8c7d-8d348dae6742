package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 页面模块元数据配置
 * <p>
 * 对应数据库表: site_page_module
 */
@Data
@TableName(value = "site_page_module", keepGlobalPrefix = true)
public class SitePageModuleEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    private String moduleId;

    private String pageId;

    private String moduleName;
    private String component;

    /**
     * 模板ID
     */
    private String template;

    private String dataProvider;
    private String dataProviderParams;

    /**
     * 模块配置JSON
     */
    private String config;

    private Date createTime;
    private Date updateTime;

    @TableLogic
    private Integer deleted;
}
