package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 针对页面的实验分桶实体类
 * <p>
 * 对应数据库表: site_exp_bucket
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_exp_bucket", keepGlobalPrefix = true)
public class SiteExpBucketEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 实验ID
     */
    private Long expId;

    /**
     * 分桶ID
     */
    private String bucketId;

    /**
     * 分组比例 0-100
     */
    private String ratio;

    /**
     * 页面配置，JSON格式
     */
    private String config;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
