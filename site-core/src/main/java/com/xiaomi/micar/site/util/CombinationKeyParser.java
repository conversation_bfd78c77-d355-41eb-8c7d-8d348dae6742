package com.xiaomi.micar.site.util;

import org.apache.commons.lang3.StringUtils;

public final class CombinationKeyParser {
    private CombinationKeyParser() {}

    public static String extractRouteCode(String key) {
        if (key == null) {
            return null;
        }
        String keyWithoutVersion = key;
        if (key.startsWith("V1_") || key.startsWith("V2_")) {
            keyWithoutVersion = key.substring(3);
        }
        int p1 = keyWithoutVersion.indexOf('|');
        if (p1 < 0) {
            return null;
        }
        int p2 = keyWithoutVersion.indexOf("|exp:");
        String sub = p2 > 0 ? keyWithoutVersion.substring(p1 + 1, p2) : keyWithoutVersion.substring(p1 + 1);
        return StringUtils.isBlank(sub) ? null : sub;
    }
}
