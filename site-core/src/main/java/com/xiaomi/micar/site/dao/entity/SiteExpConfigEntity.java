package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 针对页面的实验表实体类
 * <p>
 * 对应数据库表: site_exp_config
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_exp_config", keepGlobalPrefix = true)
public class SiteExpConfigEntity {

    /**
     * 主键ID，实验ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 实验名称
     */
    private String name;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 实验条件类型
     */
    private String conditionType;

    /**
     * 实验进入条件
     */
    private String conditionRule;

    /**
     * 实验开始时间
     */
    private Long startTime;

    /**
     * 实验结束时间
     */
    private Long endTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
