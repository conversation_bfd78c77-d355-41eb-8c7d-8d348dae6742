package com.xiaomi.micar.site.core.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Supplier;

/**
 * 缓存监控增强器
 * 提供优雅的方式在业务代码中添加监控，避免重复代码
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "cache.monitoring.enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnBean(CacheMonitoringUtils.class)
public class CacheMonitoringEnhancer {

    @Resource
    private CacheMonitoringUtils monitoringUtils;

    /**
     * 监控阶段执行时间
     * 使用方式: monitorStage("preload.data.fetch", () -> { 业务逻辑 })
     */
    public <T> T monitorStage(String stageName, Supplier<T> stageLogic, String... tags) {
        long startTime = System.currentTimeMillis();
        boolean success = false;
        
        try {
            T result = stageLogic.get();
            success = true;
            return result;
        } catch (Exception e) {
            success = false;
            throw e;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            monitoringUtils.recordDuration("stage." + stageName + ".duration", duration, tags);
        }
    }

    /**
     * 监控阶段执行时间（无返回值）
     */
    public void monitorStage(String stageName, Runnable stageLogic, String... tags) {
        monitorStage(stageName, () -> {
            stageLogic.run();
            return null;
        }, tags);
    }

    /**
     * 记录计数指标（优化版，避免 for 循环）
     */
    public void recordCount(String metricName, long count, String... tags) {
        if (count <= 0) {
            return;
        }
        monitoringUtils.recordCount(metricName, (double) count, tags);
    }

    /**
     * 记录缓存命中（移除高基数标签）
     */
    public void recordCacheHit(String cacheType, boolean hit, String... tags) {
        // 过滤掉高基数标签（如 cache_key）
        String[] filteredTags = filterHighCardinalityTags(tags);
        String[] hitTags = appendTag(filteredTags, "result", hit ? "hit" : "miss");
        monitoringUtils.recordCount("cache." + cacheType + ".access", hitTags);
    }

    /**
     * 记录降级处理
     */
    public void recordFallback(String service, String reason, String... tags) {
        String[] filteredTags = filterHighCardinalityTags(tags);
        String[] fallbackTags = appendTag(filteredTags, "reason", reason);
        monitoringUtils.recordCount(service + ".fallback", fallbackTags);
    }

    /**
     * 记录用户类型分布
     */
    public void recordUserType(String userType, String... tags) {
        String[] filteredTags = filterHighCardinalityTags(tags);
        String[] userTags = appendTag(filteredTags, "user_type", userType);
        monitoringUtils.recordCount("user.type.distribution", userTags);
    }

    /**
     * 记录版本分布（移除本地累积）
     */
    public void recordVersionDistribution(String version, String... tags) {
        String[] filteredTags = filterHighCardinalityTags(tags);
        String[] versionTags = appendTag(filteredTags, "version", version);
        monitoringUtils.recordCount("version.distribution", versionTags);
    }

    /**
     * 记录缓存大小分布（简化版，使用计数器）
     */
    public void recordCacheSizeDistribution(String cacheType, long size, String... tags) {
        // 只在缓存大小超过阈值时记录，避免频繁监控
        if (size > 1024 * 1024) { // 大于1MB才记录
            String sizeRange = getSizeRange(size);
            String[] filteredTags = filterHighCardinalityTags(tags);
            String[] sizeTags = appendTag(filteredTags, "size_range", sizeRange);
            monitoringUtils.recordCount("cache.size.distribution", sizeTags);
        }
    }

    /**
     * 获取大小范围
     */
    private String getSizeRange(long size) {
        if (size < 1024) return "0-1KB";
        else if (size < 10 * 1024) return "1-10KB";
        else if (size < 100 * 1024) return "10-100KB";
        else if (size < 1024 * 1024) return "100KB-1MB";
        else if (size < 10 * 1024 * 1024) return "1-10MB";
        else return "10MB+";
    }

    /**
     * 过滤高基数标签，避免指标爆炸
     */
    private String[] filterHighCardinalityTags(String[] tags) {
        if (tags == null || tags.length == 0) {
            return tags;
        }
        
        return java.util.Arrays.stream(tags)
            .filter(tag -> !tag.startsWith("cache_key=") && !tag.startsWith("user_id="))
            .toArray(String[]::new);
    }

    /**
     * 添加标签
     */
    private String[] appendTag(String[] originalTags, String key, String value) {
        String[] newTags = new String[originalTags.length + 1];
        System.arraycopy(originalTags, 0, newTags, 0, originalTags.length);
        newTags[originalTags.length] = key + "=" + value;
        return newTags;
    }
}