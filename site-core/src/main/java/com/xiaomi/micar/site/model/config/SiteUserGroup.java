package com.xiaomi.micar.site.model.config;

import com.xiaomi.micar.site.enums.GroupCodeType;
import lombok.Data;

/**
 * 用户分组表实体类
 * 代表不同类型的用户分组（如车主、游客等）
 */
@Data
public class SiteUserGroup {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分组名称（如车主、游客等）
     */
    private String name;

    /**
     * 分组规则
     */
    private String ruleCondition;
    /**
     * 分组标识
     */
    private String groupKey;
    /**
     * 组件ID列表，以逗号分隔
     */
    private String components;

    /**
     * 获取用户组类型枚举
     *
     * @return 用户组类型枚举
     */
    public GroupCodeType getGroupType() {
        // 根据名称匹配对应的GroupCodeType
        if (name != null) {
            if (name.contains("车主")) {
                return GroupCodeType.CAR_OWNER;
            } else if (name.contains("游客")) {
                return GroupCodeType.NON_CAR_OWNER;
            }
        }
        return GroupCodeType.DEFAULT;
    }

}
