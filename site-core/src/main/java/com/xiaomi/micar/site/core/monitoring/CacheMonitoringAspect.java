package com.xiaomi.micar.site.core.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 缓存监控AOP切面
 * 自动收集带有 @CacheMonitoring 注解的方法的性能指标
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Slf4j
@Aspect
@Component
@ConditionalOnProperty(name = "cache.monitoring.enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnBean(CacheMonitoringUtils.class)
@Order(1) // 确保监控切面优先执行
public class CacheMonitoringAspect {

    @Resource
    private CacheMonitoringUtils monitoringUtils;

    /**
     * 环绕通知：处理带有 @CacheMonitoring 注解的方法
     */
    @Around("@annotation(cacheMonitoring)")
    public Object monitorCacheMethod(ProceedingJoinPoint joinPoint, CacheMonitoring cacheMonitoring) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = getMethodName(joinPoint);
        String metricName = buildMetricName(cacheMonitoring, methodName);
        String[] tags = buildTags(cacheMonitoring, joinPoint);
        
        boolean success = false;
        Object result = null;
        
        try {
            // 记录方法调用次数
            if (cacheMonitoring.recordCount()) {
                monitoringUtils.recordCount(metricName + ".invocations", tags);
            }
            
            // 执行目标方法
            result = joinPoint.proceed();
            success = true;
            
            // 记录响应大小（使用分布型指标）
            if (cacheMonitoring.recordResponseSize() && result instanceof String) {
                String response = (String) result;
                long responseSize = response.getBytes("UTF-8").length;
                monitoringUtils.recordResponseSize(metricName, responseSize, tags);
            }
            
            return result;
            
        } catch (Throwable throwable) {
            success = false;
            
            // 记录异常信息
            String[] errorTags = appendTag(tags, "error", throwable.getClass().getSimpleName());
            monitoringUtils.recordCount(metricName + ".errors", errorTags);
            
            log.warn("方法执行异常: {}.{}, error: {}", 
                joinPoint.getTarget().getClass().getSimpleName(), 
                methodName, 
                throwable.getMessage());
            
            throw throwable;
            
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录执行耗时
            if (cacheMonitoring.recordDuration()) {
                monitoringUtils.recordDuration(metricName + ".duration", duration, tags);
            }
            
            // 记录成功率
            if (cacheMonitoring.recordSuccessRate()) {
                monitoringUtils.recordSuccessRate(metricName, success, tags);
            }
            
            if (log.isDebugEnabled()) {
                log.debug("方法监控完成: {}.{}, 耗时: {}ms, 成功: {}", 
                    joinPoint.getTarget().getClass().getSimpleName(), 
                    methodName, 
                    duration, 
                    success);
            }
        }
    }

    /**
     * 构建指标名称
     */
    private String buildMetricName(CacheMonitoring annotation, String methodName) {
        String prefix = annotation.prefix();
        String value = annotation.value();
        
        if (value.isEmpty()) {
            value = methodName;
        }
        
        return prefix + "." + value;
    }

    /**
     * 构建标签数组（过滤高基数标签）
     */
    private String[] buildTags(CacheMonitoring annotation, ProceedingJoinPoint joinPoint) {
        List<String> tagList = new ArrayList<>();
        
        // 添加注解中定义的标签（过滤高基数标签）
        for (String tag : annotation.tags()) {
            if (!isHighCardinalityTag(tag)) {
                tagList.add(tag);
            }
        }
        
        // 添加默认标签
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = getMethodName(joinPoint);
        
        tagList.add("class=" + className);
        tagList.add("method=" + methodName);
        tagList.add("type=" + annotation.type().name().toLowerCase());
        
        return tagList.toArray(new String[0]);
    }

    /**
     * 检查是否为高基数标签
     */
    private boolean isHighCardinalityTag(String tag) {
        return tag.startsWith("cache_key=") || 
               tag.startsWith("user_id=") || 
               tag.contains("uuid=") ||
               tag.contains("timestamp=");
    }

    /**
     * 获取方法名
     */
    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return signature.getMethod().getName();
    }

    /**
     * 添加标签
     */
    private String[] appendTag(String[] originalTags, String key, String value) {
        String[] newTags = new String[originalTags.length + 1];
        System.arraycopy(originalTags, 0, newTags, 0, originalTags.length);
        newTags[originalTags.length] = key + "=" + value;
        return newTags;
    }
}