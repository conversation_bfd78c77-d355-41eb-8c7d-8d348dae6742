package com.xiaomi.micar.site.filter;

import com.xiaomi.hera.trace.context.TraceIdUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/7/10 下午9:29
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResultUtils {

    public static boolean isSuccess(Result<?> result) {
        return ObjectUtils.isNotEmpty(result) && result.getCode() == GeneralCodes.OK.getCode();
    }

    /**
     * 请求参数异常
     *
     * @param message 请求参数异常信息
     * @return result
     */
    public static <T> Result<T> paramError(String message) {
        return Result.fail(GeneralCodes.ParamError, message);
    }

    /**
     * 内部业务逻辑异常
     *
     * @param message 内部业务逻辑异常信息
     * @return result
     */
    public static <T> Result<T> bizError(String message) {

        Result fail = Result.fail(GeneralCodes.InternalError, message);
        fail.setTraceId(TraceIdUtil.traceId());
        return fail;
    }

    /**
     * 依赖三方接口异常
     *
     * @param message 依赖三方接口异常信息
     * @return result
     */
    public static <T> Result<T> remoteError(String message) {
        Result fail = Result.fail(GeneralCodes.ServerIsBuzy, message);
        fail.setTraceId(TraceIdUtil.traceId());
        return fail;
    }

    /**
     * 默认兜底异常
     *
     * @param message 兜底异常信息
     * @return result
     */
    public static <T> Result<T> commonError(String message) {
        Result fail = Result.fail(GeneralCodes.InternalError, message);
        fail.setTraceId(TraceIdUtil.traceId());
        return fail;
    }

    /**
     * 内部业务逻辑异常
     *
     * @param errorCode 错误码
     * @param message   内部业务逻辑异常信息
     * @return result
     */
    public static <T> Result<T> bizError(ErrorCode errorCode, String message) {
        Result fail = Result.fail(errorCode, message);
        fail.setTraceId(TraceIdUtil.traceId());

        return fail;
    }
}
