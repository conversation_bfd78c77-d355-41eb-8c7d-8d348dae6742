package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.mapper.SitePageConfigDraftMapper;
import org.springframework.stereotype.Component;

/**
 * 页面配置草稿engine
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Component
public class SitePageConfigDraftEngine extends ServiceImpl<SitePageConfigDraftMapper, SitePageConfigDraftEntity> {

    public SitePageConfigDraftEntity getById(Long id, String pageId, Integer moduleId) {
        return getOne(new LambdaQueryWrapper<>(SitePageConfigDraftEntity.class)
                .eq(SitePageConfigDraftEntity::getId, id)
                .eq(SitePageConfigDraftEntity::getPageId, pageId)
                .eq(SitePageConfigDraftEntity::getModuleId, moduleId));
    }

}
