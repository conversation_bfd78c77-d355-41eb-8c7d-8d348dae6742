package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户白名单管理
 * <p>
 * 对应数据库表: site_white_user
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_white_user", keepGlobalPrefix = true)
public class SiteWhiteUserEntity {

    /**
     * 主键ID，实验ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 账号（邮箱前缀）
     */
    private String account;

    /**
     * 姓名
     */
    private String userName;

    /**
     * mid
     */
    private String mid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
