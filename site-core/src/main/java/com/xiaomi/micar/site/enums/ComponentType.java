package com.xiaomi.micar.site.enums;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.component.ActivityListComponent;
import com.xiaomi.micar.site.component.BannerComponent;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.component.NavigationComponent;
import com.xiaomi.micar.site.component.NewsComponent;
import com.xiaomi.micar.site.component.TabFeedsComponent;
import com.xiaomi.micar.site.component.AppointmentTabsComponent;
import com.xiaomi.micar.site.component.ServiceActionPanelComponent;
import com.xiaomi.micar.site.component.TabNewsComponent;
import com.xiaomi.micar.site.component.TabsComponent;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 组件类型枚举
 * 每个枚举值关联对应的组件类，简化组件实例创建
 */
@Getter
public enum ComponentType {
    /**
     * 标签列表组件
     */
    TABS("Tabs", "标签列表组件", TabsComponent.class),
    NAVIGATION("Navigation", "导航列表组件", NavigationComponent.class),
    TAB_NEWS("TabNews", "多标签图文组件", TabNewsComponent.class),
    NEWS("News", "图文列表组件", NewsComponent.class),
    ACTIVITY_LIST("ActivityList", "活动列表组件", ActivityListComponent.class),
    BANNER("Banner", "横幅广告组件", BannerComponent.class),
    TAB_FEEDS("TabFeeds", "多标签精彩瞬间组件", TabFeedsComponent.class),
    APPOINTMENT_TABS("AppointmentTabs", "服务预约标签组件", AppointmentTabsComponent.class),
    SERVICE_ACTION_PANEL("ServiceActionPanel", "业务快捷面板组件", ServiceActionPanelComponent.class),
    ;

    private final String code;
    private final String description;
    private final Class<? extends Component> componentClass;

    // 组件类型缓存，用于快速查找
    private static final Map<String, ComponentType> TYPE_CACHE = new ConcurrentHashMap<>();

    // 组件类缓存，用于通过组件类快速查找组件类型
    private static final Map<Class<? extends Component>, ComponentType> CLASS_CACHE = new ConcurrentHashMap<>();

    // 静态初始化块，初始化缓存
    static {
        for (ComponentType type : ComponentType.values()) {
            TYPE_CACHE.put(type.getCode().toLowerCase(), type);
            CLASS_CACHE.put(type.getComponentClass(), type);
        }
    }

    ComponentType(String code, String description, Class<? extends Component> componentClass) {
        this.code = code;
        this.description = description;
        this.componentClass = componentClass;
    }

    /**
     * 根据组件代码获取枚举值
     *
     * @param code 组件代码
     * @return 枚举值，如果不存在则返回null
     */
    public static ComponentType fromCode(String code) {
        if (code == null) {
            return null;
        }

        // 使用缓存快速查找
        return TYPE_CACHE.get(code.toLowerCase());
    }

    /**
     * 判断给定的代码是否为有效的组件类型
     *
     * @param code 组件代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return fromCode(code) != null;
    }

    /**
     * 从组件数据创建组件对象
     * 适用于PageConfigService中的数据结构
     *
     * @param componentData 组件数据
     * @return 组件对象，如果创建失败则返回null
     */
    public static Component createComponentFromData(Map<String, Object> componentData) {
        if (componentData == null) {
            return null;
        }

        String componentType = (String) componentData.get("component");
        if (componentType == null || componentType.isEmpty()) {
            return null;
        }

        Object configObj = componentData.get("config");
        if (configObj == null) {
            return null;
        }

        try {
            // 从缓存获取组件类型
            ComponentType type = TYPE_CACHE.get(componentType.toLowerCase());
            if (type == null) {
                return null;
            }

            String configJson = JsonUtil.toJSONString(configObj);
            Component componentInfo = JsonUtil.parseObject(configJson, type.getComponentClass());
            if (componentInfo != null) {
                componentInfo.setId((String) componentData.get("id"));
            }
            return componentInfo;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据组件类查找对应的组件类型
     *
     * @param componentClass 组件类
     * @return 对应的组件类型，如果不存在则返回null
     */
    public static ComponentType fromComponentClass(Class<? extends Component> componentClass) {
        if (componentClass == null) {
            return null;
        }
        return CLASS_CACHE.get(componentClass);
    }

    /**
     * 创建组件实例
     * 用于diff功能，创建一个空的组件实例
     *
     * @return 组件实例，如果创建失败则返回null
     */
    public Component createInstance() {
        try {
            return componentClass.newInstance();
        } catch (Exception e) {
            return null;
        }
    }

}
