package com.xiaomi.micar.site.constants;

/**
 * 站点组件常量类
 * 定义所有站点组件相关的常量，避免硬编码
 */
public final class SiteComponentConstants {

    private SiteComponentConstants() {
        // 私有构造函数防止实例化
    }

    /**
     * 实验相关常量
     */
    public static final class Experiment {
        public static final String EXPERIMENT_ID = "experimentId";
    }

    /**
     * 实验桶相关常量
     */
    public static final class Bucket {
        public static final String BUCKET_ID = "bucketId";
    }

    /**
     * 组合键相关常量
     */
    public static final class CombinationKey {
        public static final String EXPERIMENT_PREFIX = "exp:";
        public static final String DELIMITER = "|";
        public static final String BUCKET_DELIMITER = ":";
        public static final String COMBINATION_DELIMITER = ",";
    }

}
