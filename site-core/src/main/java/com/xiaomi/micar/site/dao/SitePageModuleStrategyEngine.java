package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SitePageModuleStrategyEntity;
import com.xiaomi.micar.site.dao.mapper.SitePageModuleStrategyMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 页面模块分组配置Engine
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Component
public class SitePageModuleStrategyEngine extends ServiceImpl<SitePageModuleStrategyMapper, SitePageModuleStrategyEntity> {

    /**
     * 根据页面ID和策略获取模块列表
     *
     * @param pageId   页面ID
     * @param strategy 策略
     * @return 模块列表
     */
    public List<SitePageModuleStrategyEntity> getByPageIdAndStrategy(String pageId, String strategy) {
        return list(new LambdaQueryWrapper<SitePageModuleStrategyEntity>()
                .eq(SitePageModuleStrategyEntity::getPageId, pageId)
                .eq(SitePageModuleStrategyEntity::getStrategy, strategy)
                .orderByAsc(SitePageModuleStrategyEntity::getPriority));
    }

    /**
     * 根据页面ID获取所有策略的模块配置
     *
     * @param pageId 页面ID
     * @return 模块配置列表
     */
    public List<SitePageModuleStrategyEntity> getByPageId(String pageId) {
        return list(new LambdaQueryWrapper<SitePageModuleStrategyEntity>()
                .eq(SitePageModuleStrategyEntity::getPageId, pageId)
                .orderByAsc(SitePageModuleStrategyEntity::getStrategy)
                .orderByAsc(SitePageModuleStrategyEntity::getPriority));
    }

    /**
     * 删除指定页面和策略下的所有模块配置
     *
     * @param pageId   页面ID
     * @param strategy 策略
     * @return 是否删除成功
     */
    public boolean deleteByPageIdAndStrategy(String pageId, String strategy) {
        return remove(new LambdaQueryWrapper<SitePageModuleStrategyEntity>()
                .eq(SitePageModuleStrategyEntity::getPageId, pageId)
                .eq(SitePageModuleStrategyEntity::getStrategy, strategy));
    }

}
