package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面配置表实体类
 * 代表页面级别的配置
 * <p>
 * 对应数据库表: site_page_record
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_page_record", keepGlobalPrefix = true)
public class SitePageRecordEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String pageId;
    private String moduleId;
    private String recordGroup;

    /**
     * 修改内容
     */
    private String config;

    private Long parentId;

    private String remark;

    private Integer priority;

    private Long exposeFrom;

    private Long exposeTo;

    private String operatorId;

    private String operatorName;

    private Date createTime;

    private Date updateTime;

    @TableLogic
    @JsonIgnore
    private Integer deleted;

}
