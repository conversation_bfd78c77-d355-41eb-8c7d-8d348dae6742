package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaomi.micar.site.dao.entity.SiteWhiteUserEntity;
import com.xiaomi.micar.site.dao.mapper.SiteWhiteUserMapper;
import com.xiaomi.micar.site.model.CommonPagedData;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 白名单engine
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Component
public class SiteWhiteUserEngine extends ServiceImpl<SiteWhiteUserMapper, SiteWhiteUserEntity> {


    /**
     * 记录列表（分页）
     *
     * @param pageNo
     * @param size
     * @return
     */
    public CommonPagedData<SiteWhiteUserEntity> pagedData(Long pageNo, Long size) {

        Page<SiteWhiteUserEntity> page = page(PageDTO.of(pageNo, size), new LambdaQueryWrapper<>(SiteWhiteUserEntity.class)
                .orderByAsc(SiteWhiteUserEntity::getUpdateTime)
        );

        CommonPagedData<SiteWhiteUserEntity> result = new CommonPagedData<>();
        result.setPage(page.getCurrent());
        result.setTotal(page.getTotal());
        result.setSize(size);
        result.setRecords(page.getRecords());

        return result;
    }

    public void saveOrUpdateUser(SiteWhiteUserEntity entity) {
        SiteWhiteUserEntity exist = null;
        if(entity.getId() == null) {
            exist = new SiteWhiteUserEntity();
        } else {
            exist = getById(entity.getId());
            Assert.notNull(exist, "用户不存在");
        }

        exist.setAccount(entity.getAccount());
        exist.setUserName(entity.getUserName());
        exist.setMid(entity.getMid());

        saveOrUpdate(exist);
    }

}
