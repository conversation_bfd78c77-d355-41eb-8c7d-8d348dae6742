package com.xiaomi.micar.site.enums;

/**
 * APP页面场景代码类型枚举
 * 表示APP上的不同页面
 */
public enum PageCodeType {

    EXPLORE("explore", "探索页面"),
    ACTIVITY("activity", "活动"),
    ;

    private final String code;
    private final String description;

    PageCodeType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据场景代码获取枚举值
     *
     * @param code 场景代码
     * @return 枚举值，如果不存在则返回null
     */
    public static PageCodeType fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (PageCodeType type : PageCodeType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    /**
     * 判断给定的代码是否为有效的场景代码类型
     *
     * @param code 场景代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return fromCode(code) != null;
    }
}
