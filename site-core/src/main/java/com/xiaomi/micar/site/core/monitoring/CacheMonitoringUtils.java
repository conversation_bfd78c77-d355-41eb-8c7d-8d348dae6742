package com.xiaomi.micar.site.core.monitoring;

import com.google.common.collect.Maps;
import com.mi.car.iccc.starter.metrics.util.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.time.Duration;

/**
 * 缓存监控工具类
 * 基于 Metrics 类实现，提供统一的监控指标收集方法
 * 保持原有 API 兼容性，直接使用 Metrics 类的缓存机制
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Slf4j
@Component
public class CacheMonitoringUtils {

    /**
     * 记录方法执行耗时
     *
     * @param metricName 指标名称
     * @param duration   耗时（毫秒）
     * @param tags       标签数组
     */
    public void recordDuration(String metricName, long duration, String... tags) {
        try {
            Map<String, String> tagMap = parseTags(tags);
            Metrics.timer(metricName, tagMap, Duration.ofMillis(duration));
            
            if (log.isDebugEnabled()) {
                log.debug("记录耗时指标: {} = {}ms, tags: {}", metricName, duration, String.join(",", tags));
            }
        } catch (Exception e) {
            log.warn("记录耗时指标失败: {}", e.getMessage());
        }
    }

    /**
     * 记录方法调用次数
     *
     * @param metricName 指标名称
     * @param tags       标签数组
     */
    public void recordCount(String metricName, String... tags) {
        try {
            Map<String, String> tagMap = parseTags(tags);
            Metrics.counter(metricName, tagMap);
            
            if (log.isDebugEnabled()) {
                log.debug("记录计数指标: {}, tags: {}", metricName, String.join(",", tags));
            }
        } catch (Exception e) {
            log.warn("记录计数指标失败: {}", e.getMessage());
        }
    }

    /**
     * 记录方法调用次数（指定增量）
     */
    public void recordCount(String metricName, double amount, String... tags) {
        try {
            Map<String, String> tagMap = parseTags(tags);
            Metrics.counter(metricName, tagMap, amount);
            
            if (log.isDebugEnabled()) {
                log.debug("记录计数指标(增量): {} += {}, tags: {}", metricName, amount, String.join(",", tags));
            }
        } catch (Exception e) {
            log.warn("记录计数指标(增量)失败: {}", e.getMessage());
        }
    }

    /**
     * 记录成功/失败次数
     *
     * @param metricName 指标名称
     * @param success    是否成功
     * @param tags       标签数组
     */
    public void recordSuccessRate(String metricName, boolean success, String... tags) {
        try {
            Map<String, String> tagMap = parseTags(tags);
            Map<String, String> successTags = Maps.newHashMap(tagMap);
            successTags.put("result", success ? "success" : "failure");
            Metrics.counter(metricName + ".success_rate", successTags);
            
            if (log.isDebugEnabled()) {
                log.debug("记录成功率指标: {} = {}, tags: {}", metricName, success, String.join(",", tags));
            }
        } catch (Exception e) {
            log.warn("记录成功率指标失败: {}", e.getMessage());
        }
    }

    /**
     * 记录响应大小
     *
     * @param metricName   指标名称
     * @param responseSize 响应大小（字节）
     * @param tags         标签数组
     */
    public void recordResponseSize(String metricName, long responseSize, String... tags) {
        try {
            Map<String, String> tagMap = parseTags(tags);
            // 使用计数器记录响应大小范围，避免使用 DistributionSummary
            String sizeRange = getSizeRange(responseSize);
            Map<String, String> sizeTags = Maps.newHashMap(tagMap);
            sizeTags.put("size_range", sizeRange);
            Metrics.counter(metricName + ".response_size", sizeTags);
            
            if (log.isDebugEnabled()) {
                log.debug("记录响应大小指标: {} = {} bytes, tags: {}", metricName, responseSize, String.join(",", tags));
            }
        } catch (Exception e) {
            log.warn("记录响应大小指标失败: {}", e.getMessage());
        }
    }

    /**
     * 记录分布型指标（简化为计数器）
     */
    public void recordDistribution(String metricName, double value, String... tags) {
        try {
            Map<String, String> tagMap = parseTags(tags);
            // 使用计数器记录分布范围，避免使用 DistributionSummary
            String range = getValueRange(value);
            Map<String, String> rangeTags = Maps.newHashMap(tagMap);
            rangeTags.put("range", range);
            Metrics.counter(metricName + ".distribution", rangeTags);
            
            if (log.isDebugEnabled()) {
                log.debug("记录分布指标: {} = {}, tags: {}", metricName, value, String.join(",", tags));
            }
        } catch (Exception e) {
            log.warn("记录分布指标失败: {}", e.getMessage());
        }
    }

      /**
     * 将标签数组转换为 Map
     */
    private Map<String, String> parseTags(String[] tags) {
        Map<String, String> tagMap = Maps.newHashMap();
        if (tags != null) {
            for (String tag : tags) {
                String[] parts = tag.split("=", 2);
                if (parts.length == 2) {
                    tagMap.put(parts[0], parts[1]);
                }
            }
        }
        return tagMap;
    }

    /**
     * 获取大小范围
     */
    private String getSizeRange(long size) {
        if (size < 1024) return "0-1KB";
        else if (size < 10 * 1024) return "1-10KB";
        else if (size < 100 * 1024) return "10-100KB";
        else if (size < 1024 * 1024) return "100KB-1MB";
        else if (size < 10 * 1024 * 1024) return "1-10MB";
        else return "10MB+";
    }

    /**
     * 获取数值范围
     */
    private String getValueRange(double value) {
        if (value < 100) return "0-100";
        else if (value < 1000) return "100-1K";
        else if (value < 10000) return "1K-10K";
        else if (value < 100000) return "10K-100K";
        else if (value < 1000000) return "100K-1M";
        else return "1M+";
    }

    /**
     * 添加标签（保持向后兼容）
     */
    private String[] appendTag(String[] originalTags, String key, String value) {
        String[] newTags = new String[originalTags.length + 1];
        System.arraycopy(originalTags, 0, newTags, 0, originalTags.length);
        newTags[originalTags.length] = key + "=" + value;
        return newTags;
    }


}
