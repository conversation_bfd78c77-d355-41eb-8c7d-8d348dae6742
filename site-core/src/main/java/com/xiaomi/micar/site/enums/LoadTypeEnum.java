package com.xiaomi.micar.site.enums;

/**
 * 页面加载方式枚举
 * 
 * <AUTHOR>
 * @since 2025/06/29
 */
public enum LoadTypeEnum {

    /**
     * 从page直接加载
     */
    PAGE_DIRECT(1, "从page直接加载"),

    /**
     * 从module加载
     */
    MODULE_LOAD(2, "从module加载");

    private final Integer code;
    private final String desc;

    LoadTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 加载方式码
     * @return 对应的枚举，未找到返回null
     */
    public static LoadTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LoadTypeEnum loadType : values()) {
            if (loadType.getCode().equals(code)) {
                return loadType;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的加载方式码
     *
     * @param code 加载方式码
     * @return true=有效，false=无效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 检查是否为页面直接加载方式
     *
     * @param code 加载方式码
     * @return true=页面直接加载，false=其他方式或无效
     */
    public static boolean isPageDirect(Integer code) {
        return PAGE_DIRECT.getCode().equals(code);
    }

    /**
     * 检查是否为模块加载方式
     *
     * @param code 加载方式码
     * @return true=模块加载，false=其他方式或无效
     */
    public static boolean isModuleLoad(Integer code) {
        return MODULE_LOAD.getCode().equals(code);
    }
}