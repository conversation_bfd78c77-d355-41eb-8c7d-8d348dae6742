package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面模块分组配置实体类
 * 对应数据库表: site_page_module_strategy
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_page_module_strategy", keepGlobalPrefix = true)
public class SitePageModuleStrategyEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 分组策略
     */
    private String strategy;

    /**
     * 模块ID（关联 site_page_module.id）
     */
    private Long moduleId;

    /**
     * 优先级(C端模块顺序)
     */
    private Integer priority;

    /**
     * 配置内容（JSON格式，包含模块ID列表）
     */
    private String config;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer deleted;
}
