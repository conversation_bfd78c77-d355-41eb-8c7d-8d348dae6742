-- 页面缓存存储表
-- 用于存储页面缓存数据，替代Nacos存储大数据
CREATE TABLE `site_page_cache_storage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `page_id` varchar(64) NOT NULL COMMENT '页面ID',
  `combination_key` varchar(255) NOT NULL COMMENT '组合键（页面+策略+实验组合的唯一标识）',
  `config` longtext NOT NULL COMMENT '配置内容（JSON格式的PageRespV2）',
  `batch_id` varchar(64) NOT NULL COMMENT '批次ID（用于标识本次更新的批次）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_page_batch` (`page_id`, `batch_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面缓存存储表';
