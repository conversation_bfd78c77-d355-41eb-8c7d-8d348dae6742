server:
  port: 8080
  shutdown: graceful

spring:
  servlet:
    multipart:
      enabled: false
  redis:
    redisson:
      connection-pool-size: 100
  datasource:
    hikari:
      connection-timeout: 6000 # 连接超时时间，单位毫秒，不添加此参数hikari默认30秒
      idle-timeout: 60000 # 连接池中连接最大空闲时间，单位毫秒，不添加此参数hikari默认10 minutes
      max-lifetime: 300000 # 连接池中连接最大生命周期，单位毫秒，不添加此参数hikari默认30 minutes【lvs默认是360s这里少60s】
      maximum-pool-size: 50 # 连接池最大连接数
      minimum-idle: 10 # 连接池中最小空闲连接数
      pool-name: activity-hikari-pool # 连接池名字
      connection-test-query: SELECT 1 # 校验连接的SQL语句
      leak-detection-threshold: 30000 # 连接泄露检测阈值，单位毫秒，hikari默认0不检查
      validation-timeout: 750 #单位毫秒，默认5s。探测连接alive超时时间。只作用于探活，不作用于业务SQL
  task:
    execution:
      pool:
        allow-core-thread-timeout: true
        core-size: 8
        max-size: 16
        queue-capacity: 1000
        keep-alive: 60s
  jackson:
    # json 序列化排除值为 null 的属性
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    name: micar-site-admin
  profiles:
    default: staging
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

service:
  name: http:com.mi.micar-site-admin

logging:
  level:
    root: INFO
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

dubbo:
  application:
    qosEnable: false
  protocol:
    threads: 500
  provider:
    tag: ''
    filter: "-icccDubboException,-icccDubboValidation,exceptionFilter"
  consumer:
    filter: "-icccDubboException,-icccDubboValidation"
feign:
  okhttp:
    enabled: true

# BPM表单配置
bpm:
  form:
    # 页面名称字段ID
    page-name-field: input_2838a77eaa06
    # 模块配置字段ID
    module-config-field: input_54fd98c13175
    # 备注字段ID
    remark-field: textarea_5bb2fd36f811
    # 预览二维码字段ID
    qr-code-field: upload_d45ca55fed22
    # 流程模型编码
    model-code: bpmn_1116682992290459648
    # 流程实例名称
    process-name: 汽车APP官网发布审批
    # 详情页面域名
    detail-page-domain: https://admin.car.miui.com
# 前端 C 端地址配置
frontend:
  url:
    page-base-url: https://www.xiaomiev.com/shop/home/<USER>