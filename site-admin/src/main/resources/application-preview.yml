spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password@kc-sid: iccc-config-decrypt
    password: GDD/P4BNEksumNR2Ux1khDthlOv5Um+FcbUOZsJTfbQWx0lJqhD69eHRW9y1cjbQfXYYEoMGTt63s02AiA0AdgPPqin+ARgQ3mh7jY4tTWqUSphP8Mp8xBgUUjmOFBH6nKKaZk+IR0sbygXpGmEA
    url: ******************************************************************************************************************************
    username: micar_site_sv22_wn
  redis:
    host: ares.car.cache.b2c.srv
    port: 5105
    password@kc-sid: iccc-config-decrypt
    password: GDChON/3JNFs+mH+MLQW/rRoFwUhbZW4rGEcfQ4jKtG8gmMCBCmjbpDsWV82QOWDgjAYEtD/BSNb5Eegn82KkUSXkn7OARgQBxjPmtUjSwygR6quH3Bz7xgUOp5QQLiFW4kEV0dBCQ6Ss4ijJY4A
# dubbo相关配置
dubbo:
  group: preview
  registry:
    address: nacos://nacos.systech.b2c.srv:80
  mdcd:
    registry:
      address: nacos://${dubbo.mdcd.registry.url}?username=${dubbo.mdcd.registry.username}&password=${dubbo.mdcd.registry.password}&namespace=${dubbo.mdcd.registry.username}
      url: cnbj1-nacos.api.xiaomi.net:80
      username: mdcd-preview
      password@kc-sid: iccc-config-decrypt
      password: GDBhp6Zsc8DOnKHA7OMTYQ3RX9qZcR39DSagap1K+mnMzFcyAowgjJ/gZGt1W0YFULcYEimqN9nj5EEBh54tU97cUXVXARgQVjsgdRYVQSS8tmEdA5VNzxgUkQERyAjepVDmET4zPvFeREY7/LYA
      group: preview
  consumer:
    activity-site: preview

service:
  name: http:com.mi.micar-site-admin-preview

#mybatis
mybatis-plus:
  global-config:
    db-config:
      table-prefix: preview_

#nacos
nacos:
  config:
    enabled: true
    addr: nacos.systech.b2c.srv:80
    namespace: preview

iccc:
  site:
    component:
      api:
        domain: internal-preview.car.miui.srv
    router:
      community:
        domain: https://web-preview.community.car.miui.com
  fds:
    clients:
      default:
        endPoint: staging-cnbj2-fds.api.xiaomi.net
        accessKey: AKS7T6LK4S47PWXHUX
        secretKey@kc-sid: iccc-config-decrypt
        secretKey: GDAXWxjs2F7OWwxi0VaDgPLgXSyJJBlAUgJajeFcsNW0mv7cIGC9ZIYQMuelZecYIh0YEjm54ZRyG0K4gLtzRMqEprGFARgQoKhTD-H-Tnu_lVqIi4hT2hgUwU7dZoD-kK6JVmDxDgTDYxlGFPwA
        grandeeId: CI132916

oaucf:
  auth:
    appId: mdQjtjoqfq8s          # 应用的id
    appSecret: AlczYJtM0Qh1K7mrfj529i5LsoMNdCPn  # 应用的secret
  bpm:
    enabled: true
    url: https://bpm.infra.mioffice.cn/runtime    # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60
# BPM表单配置
bpm:
  form:
    # 页面名称字段ID
    page-name-field: input_959723a13211
    # 模块配置字段ID
    module-config-field: input_26d5dd7f9b04
    # 备注字段ID
    remark-field: textarea_f54a13f1132e
    # 查看详情
    check-detail-field: input_398aec600560
    # 预览二维码字段ID
    qr-code-field: upload_c311b0756820
    # 流程模型编码
    model-code: bpmn_1142839981423972352
    # 流程实例名称
    process-name: 【preview】汽车APP官网发布审批
    # 详情页面域名
    detail-page-domain: https://xiaomiev-be.pre.mi.com
    # 预览二维码链接前缀
    qr-url-prefix: https://tesla-router.pre.xiaomiev.com/mtop/carInternal/carsiteMgr
site:
  robot:
    baseUrl: https://open.f.mioffice.cn
    appId: cli_a8fdaf8618b89062
    appSecret@kc-sid: iccc-config-decrypt
    appSecret: GDAJVfwpwF4lLg6ZfqiY2SnbTUhon0ouKdaU05I/Zowanrfqn17OQO3esCXtUW6daugYEvUmvAFb2EMQhpExBADr22N5ARgQAcCGp/SoSpKLuKyF5AO0hxgUT7zlINm632ji2n1MtiGKWAY1NzQA
    chatId: oc_509c58637911e59b7646562ffc748130
    templateMap:
      DraftApproval: AAqIEzh8srhyo
      ApprovalResult: AAqIEzh8srhyo