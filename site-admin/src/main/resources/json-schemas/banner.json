{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://xiaomi.com/schemas/banner.json", "title": "Banner Template <PERSON><PERSON><PERSON>", "description": "Banner 模板配置格式定义", "type": "object", "properties": {"data": {"type": "array", "minItems": 1, "description": "Banner 数据数组", "items": {"type": "object", "properties": {"id": {"type": "string", "minLength": 1, "description": "Banner 项唯一标识"}, "name": {"type": "string", "maxLength": 100, "description": "Banner 名称"}, "type": {"enum": ["image", "video"], "description": "内容类型"}, "image": {"type": "object", "description": "主图片信息"}, "imageFold": {"type": "object", "description": "折叠图片信息"}, "video": {"type": "object", "description": "视频信息", "additionalProperties": false}, "videoFold": {"type": "object", "description": "折叠视频信息", "additionalProperties": false}, "title": {"type": "string", "maxLength": 100, "description": "Banner 标题"}, "subTitle": {"type": "string", "maxLength": 100, "description": "Banner 副标题"}, "refId": {"type": "string", "description": "引用链接"}, "refType": {"enum": ["h5", "native", "external", "miniProgram", "appInternal"], "description": "引用类型"}}, "required": ["id", "name", "type", "title", "subTitle", "refId", "refType"], "allOf": [{"if": {"properties": {"type": {"const": "image"}}}, "then": {"description": "图片类型：必须提供 image 和 imageFold", "required": ["image", "imageFold"], "properties": {"image": {"type": "object", "description": "图片信息", "properties": {"src": {"type": "string", "pattern": "^https?://.*", "description": "图片链接"}}, "required": ["src"], "additionalProperties": false}, "imageFold": {"type": "object", "description": "折叠图片信息", "properties": {"src": {"type": "string", "pattern": "^https?://.*", "description": "折叠图片链接"}}, "required": ["src"], "additionalProperties": false}}}}, {"if": {"properties": {"type": {"const": "video"}}}, "then": {"description": "视频类型：必须提供 video 和 videoFold", "required": ["video", "videoFold"], "properties": {"video": {"type": "object", "description": "视频信息", "properties": {"cover": {"type": "string", "pattern": "^https?://.*", "description": "视频封面链接"}}, "required": ["cover"], "additionalProperties": false}, "videoFold": {"type": "object", "description": "折叠视频信息", "properties": {"cover": {"type": "string", "pattern": "^https?://.*", "description": "折叠屏视频封面链接"}}, "required": ["cover"], "additionalProperties": false}}}}], "additionalProperties": false}}}, "required": ["data"], "additionalProperties": false}