spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password@kc-sid: iccc-config-decrypt
    password: GDAGSxp0NKAMNA9opEmXiN57osW_Cw6jtMLosXpGqNyDLdpR68joozqxqs8NlgpZ7osYEkxdJiNUjU75uMlCof9n1HBS_xgQyO3UYghWRaeVndcdvktuOBgUvwRcbw15twjbJcN3sItGTW2S1-0A
    url: ******************************************************************************************************************************
    username: micar_site_sv22_wn
  # 座舱通用redis
  redis:
    host: wcc.cache01.test.b2c.srv
    port: 22122
    password@kc-sid: iccc-config-decrypt
    password: GDA0KVOkNnRpWWWXU/SwvICafkaNk64/UVPDp5NuSeJBLONZ1TIe4Z+9JGjndmiiRdcYEpzjCFcVy0s4l/JAR+LwcDGk/xgQMgoMEIT0QCqrgxEt0cv6gxgUlG8oZNye3l/l6QN4koXJiu7T4wUA
# dubbo相关配置
dubbo:
  group: staging
  registry:
    address: nacos://nacos.test.b2c.srv:80
  mdcd:
    registry:
      address: nacos://${dubbo.mdcd.registry.url}?username=${dubbo.mdcd.registry.username}&password=${dubbo.mdcd.registry.password}&namespace=${dubbo.mdcd.registry.username}
      url: staging-nacos.api.xiaomi.net:80
      username: mdcd-staging
      password@kc-sid: iccc-config-decrypt
      password: GDBGSGfXL+WfcdMc5HkS2MUnRSyl0UUxVhHrALtDFdnZYhY1xP4QwbcqaiQVm03P/yUYEjgMzz0HE0MUj09x4qgpQsSR/xgQuvSpwSglR/ijNaULWZ3d5RgUYuNp5wJH9QCqZIz/DNAX7A06cfwA
      group: ''

oaucf:
  auth:
    appId: 7grEEEUKfyUT          # 应用的id
    appSecret: vpYrAcAFizYMFfvkyM9bHHCqXpFhG81J  # 应用的secret
  bpm:
    enabled: true
    url: https://bpm-infra.test.mioffice.cn/runtime    # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60
# BPM表单配置
bpm:
  form:
    # 页面名称字段ID
    page-name-field: input_2838a77eaa06
    # 模块配置字段ID
    module-config-field: input_54fd98c13175
    # 备注字段ID
    remark-field: textarea_5bb2fd36f811
    # 查看详情
    check-detail-field: link_4efa665074ff
    # 预览二维码字段ID
    qr-code-field: upload_d45ca55fed22
    # 流程模型编码
    model-code: bpmn_1116682992290459648
    # 流程实例名称
    process-name: 【staging】汽车APP官网发布审批
    # 详情页面域名
    detail-page-domain: https://admin-staging.car.miui.com
    # 预览二维码链接前缀
    qr-url-prefix: http://v-be.test.mi.com/mtop/carsiteMgr
iccc:
  fds:
    clients:
      default:
        endPoint: staging-cnbj2-fds.api.xiaomi.net
        accessKey: AKS7T6LK4S47PWXHUX
        secretKey@kc-sid: iccc-config-decrypt
        secretKey: GDAmeEvSTz29Y3nSsiiBUda5WheKzaiFRzQJQ1dDRS7DBDgI_iJjuxITahPCpASKPgIYEmuQvvSV90dSjHUMSnmlRvWW_xgQNGHmAE3VTEmcKoJgKzsunBgUYN9cuosUjUdhdvktlN9lxDsbLEoA
        grandeeId: CI132916
#nacos
nacos:
  config:
    enabled: true
    addr: nacos.test.b2c.srv:80
    namespace: staging
site:
  robot:
    baseUrl: https://open.f.mioffice.cn
    appId: cli_a8fdaf8618b89062
    appSecret@kc-sid: iccc-config-decrypt
    appSecret: GDDKA9lECsnEPz0/VYkEKKvz+kMIm/oMWDGaQVZblLpeNE3gASNUtR+hGRAyLYNit/IYEhS2H35xw0kZnSb+0eYXFsxH/xgQy34E1wNQSyaJBq/jNZiclBgU3geyegcFY3boOQ0cgNPcTiCzPKAA
    chatId: oc_509c58637911e59b7646562ffc748130
    templateMap:
      DraftApproval: AAqIEzh8srhyo
      ApprovalResult: AAqIEzh8srhyo

