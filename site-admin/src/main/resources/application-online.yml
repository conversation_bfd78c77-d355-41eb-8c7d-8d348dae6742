spring:
  application:
    name: micar-site
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password@kc-sid: iccc-config-decrypt-prod
    password: GDCsPi0OrFqRg0uDta6GEWswsF1qTREbEnLdNEZZQ3gsIqaxMkMVWSERukX9zM-bh3EYEn67EkhlOUFhi6IEi4nPGa7AARgQGkZzkmqTQm-L87MTxlsFPhgUTM7lyQlwO5X0JR4dzsQGkDv6_fYA
    url: ***********************************************************************************************************************************************
    username: micar_site_ov79_wn
  redis:
    host: ares.cn-car-micar-vip-micar-user-operation-prod.cache.srv
    port: 5105
    password@kc-sid: iccc-config-decrypt-prod
    password: GDDpXV4wdpwP4GdySkxVBrRlQiAUkRkclpnNl6zaCYw7VTaOE/1Gaf8NahqFez20+RQYEjKxyECJtU1ausq9ThEfs84oARgQOgg/m9YvRQua0FxOKgAhTBgUFx/XNYLKRDa0ZsRudMqIJIgz3ncA

dubbo:
  group: online
  registry:
    address: nacos://nacos.systech.b2c.srv:80
  # 兼容mdcd provider tag,商城服务走tag降级逻辑调度
  consumer:
    tag: product
    activity-site: prod
  mdcd:
    registry:
      address: nacos://${dubbo.mdcd.registry.url}?username=${dubbo.mdcd.registry.username}&password=${dubbo.mdcd.registry.password}&namespace=${dubbo.mdcd.registry.username}
      url: cnbj1-nacos.api.xiaomi.net:80
      username: mdcd-prod
      password@kc-sid: iccc-config-decrypt-prod
      password: GDARKZidxrC5fp4AHBbdLTR6egFFive6gp4S/bCd8muCWNrnLP2fsXbCR6Fp9bHI2Y8YEpiUPEMfoUgUibWjb5xVIsBcARgQhUcvDnMDRTiXHP5qOohTXRgUtfiSJ/KFkk1VBQJrEJowfAl4ZdcA
      group: c3
#nacos
nacos:
  config:
    enabled: true
    namespace: c3
iccc:
  site:
    component:
      api:
        domain: internal.car.miui.srv
    router:
      community:
        domain: https://web.community.car.miui.com
  fds:
    clients:
      default:
        endPoint: cnbj1-fds.api.xiaomi.net
        accessKey: AKS7T6LK4S47PWXHUX
        secretKey@kc-sid: iccc-config-decrypt-prod
        secretKey: GDCLRMkQgdsDgx7OeLGb/ehnlQAMJ4oC5EWHTYM6A53BR28ceOxH4nBukOuL0SBnp60YEgEEFXSHdEyZtad7RzyR3zNpARgQEZBnB3ZFR0C3p0zDpLxrsxgUIbQDCTwPp4eh08zklv/TWwLydeIA
        grandeeId: CI132916
oaucf:
  auth:
    appId: J0QMvB1afJOp          # 应用的id
    appSecret@kc-sid: iccc-config-decrypt-prod
    appSecret: GDCg7Z3BYQkzsyX1WImZ6XVQgdjT4Ur7aN4kY+LKnvrJIJvdUVE1ozpsxXY1UvcZkOAYEt+ALmC6ckMMuh0UIlJcRVGEARgQPDUgTwMkTW+egm8CoJWiwhgUrMQoNn1BMBEnc18EpCFRTdRaR54A
  bpm:
    enabled: true
    url: https://bpm.infra.mioffice.cn/runtime    # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60
# BPM表单配置
bpm:
  form:
    # 页面名称字段ID
    page-name-field: input_2838a77eaa06
    # 模块配置字段ID
    module-config-field: input_54fd98c13175
    # 备注字段ID
    remark-field: textarea_5bb2fd36f811
    # 查看详情
    check-detail-field: input_398aec600560
    # 预览二维码字段ID
    qr-code-field: upload_d45ca55fed22
    # 流程模型编码
    model-code: bpmn_1116682992290459648
    # 流程实例名称
    process-name: 汽车APP官网发布审批
    # 详情页面域名
    detail-page-domain: https://xiaomiev.be.mi.com
    # 预览二维码链接前缀
    qr-url-prefix: https://api.retail.xiaomiev.com/mtop/carsite/carsiteMgr

site:
  robot:
    baseUrl: https://open.f.mioffice.cn
    appId: cli_a8fdaf8618b89062
    appSecret@kc-sid: iccc-config-decrypt-prod
    appSecret: GDBY/B+WRiSXdbw/GRVvcvw/6PQl86a1L4ntftdSWkOEtdEgR7qGBYiP4svb+9wsYLgYEm9bIsFnIEosuWC6EiSohcG0ARgQ7JbUF2OSSPC9FWk6J3i0shgUJQ+BvTVEYzEKaRcLu4Kf7rRHU1YA
    chatId: oc_509c58637911e59b7646562ffc748130
    templateMap:
      DraftApproval: AAqIEzh8srhyo
      ApprovalResult: AAqIEzh8srhyo
