package com.xiaomi.micar.site.service.callback;

import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.model.bpm.BpmCallbackDTO;
import com.xiaomi.micar.site.model.bpm.BpmCallbackResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * BMP回调验证器
 * 负责回调参数和草稿状态的验证
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class CallbackValidator {

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    /**
     * 验证回调参数
     *
     * @param callback 回调数据
     * @return 验证结果，null表示验证通过
     */
    public BpmCallbackResponse validateCallback(BpmCallbackDTO callback) {
        if (callback == null) {
            log.warn("回调参数为空");
            return BpmCallbackResponse.error("回调参数为空");
        }

        if (StringUtils.isEmpty(callback.getBusinessKey())) {
            log.warn("业务键为空");
            return BpmCallbackResponse.error("业务键为空");
        }

        if (StringUtils.isEmpty(callback.getProcessInstanceStatus())) {
            log.warn("流程状态为空");
            return BpmCallbackResponse.error("流程状态为空");
        }

        return null;
    }

    /**
     * 根据业务键获取草稿
     *
     * @param businessKey 业务键
     * @return 草稿实体
     */
    public SitePageConfigDraftEntity getDraftByBusinessKey(String businessKey) {
        try {
            SitePageConfigDraftEntity draft = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getGroupKey, businessKey)
                    .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
                    .last("LIMIT 1")
                    .one();

            if (draft == null) {
                log.warn("根据groupKey未找到草稿: {}", businessKey);
                return null;
            }


            return draft;
        } catch (Exception e) {
            log.warn("解析业务键异常: {}", businessKey, e);
            return null;
        }
    }

    /**
     * 验证草稿状态是否有效
     *
     * @param draft 草稿实体
     * @return 是否有效
     */
    public boolean isValidDraftStatus(SitePageConfigDraftEntity draft) {
        if (draft == null) {
            return false;
        }

        Integer status = draft.getStatus();
        return status != null && 
               (status.equals(ConfigStatus.AUDITING.getCode()) || 
                status.equals(ConfigStatus.OFFLINE_AUDIT.getCode()));
    }
}
