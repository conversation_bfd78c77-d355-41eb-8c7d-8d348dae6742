package com.xiaomi.micar.site.api.be;

import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.api.model.TopicPageListResp;
import com.xiaomi.micar.site.api.model.TopicPageSaveReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftDetailReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftDeleteReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftListReq;
import com.xiaomi.micar.site.api.be.aop.BeLoginCheck;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.List;

/**
 * 专题页管理接口
 *
 * 提供专题页的查询和管理功能
 */
public interface BeTopicPageMgr {

    /**
     * 获取所有专题页列表
     *
     * @return 专题页列表响应
     */
    Result<TopicPageListResp> getAllTopicPages();

    /**
     * 获取有草稿的专题页草稿列表
     *
     * @param req 专题页草稿列表请求
     * @return 草稿列表响应
     */
    Result<CommonPagedData<RecordDTO>> getTopicPagesWithDrafts(TopicPageDraftListReq req);

    /**
     * 保存专题页配置
     *
     * @param req 专题页保存请求
     * @return 保存结果
     */
    Result<Void> saveTopicPage(TopicPageSaveReq req);

    /**
     * 根据草稿ID获取专题页详情
     *
     * @param req 专题页草稿详情请求
     * @return 草稿详情
     */
    Result<RecordDTO> getDraftDetail(TopicPageDraftDetailReq req);

    /**
     * 删除专题页草稿
     *
     * @param req 专题页草稿删除请求
     * @return 删除结果
     */
    Result<Void> deleteDraft(TopicPageDraftDeleteReq req);
}