package com.xiaomi.micar.site.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.car.iccc.iccccommonutil.util.IcccCommonDateUtil;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.dao.entity.SiteOpsLogEntity;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.mapper.SiteOpsLogMapper;
import com.xiaomi.micar.site.enums.OperationType;
import com.xiaomi.micar.site.model.UserInfo;
import com.xiaomi.micar.site.model.bpm.Operator;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * site ops log
 *
 * <AUTHOR>
 * @since 2025/05/17
 */
@Component
public class SiteOpsLogEngine extends ServiceImpl<SiteOpsLogMapper, SiteOpsLogEntity> {

    @Transactional(rollbackFor = Exception.class)
    public void saveOpsLog(OperationType operationType, UserInfo userInfo, List<SitePageConfigDraftEntity> draftList) {
        long now = IcccCommonDateUtil.getCurrentTimeMillis();

        List<SiteOpsLogEntity> logs = Lists.newArrayList();
        draftList.forEach(draft -> {
            SiteOpsLogEntity entity = new SiteOpsLogEntity();
            entity.setTs(now);
            entity.setOperatorId(userInfo.getAccount());
            entity.setOperatorName(userInfo.getUserName());
            entity.setOperation(operationType.getCode());
            entity.setPageId(draft.getPageId());
            entity.setModuleId(draft.getModuleId() != null ? draft.getModuleId().toString() : null);
            entity.setDraftId(String.valueOf(draft.getId()));
            entity.setReleaseId(draft.getReleaseId() == null ? null : String.valueOf(draft.getReleaseId()));
            entity.setRemark("user ops");
            entity.setContent(JsonUtil.toJSONString(draft));
            logs.add(entity);
        });
        this.saveBatch(logs);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBpmCallbackLog(OperationType operationType, Operator operator, String comment, SitePageConfigDraftEntity draft) {
        long now = IcccCommonDateUtil.getCurrentTimeMillis();
        SiteOpsLogEntity entity = new SiteOpsLogEntity();
        entity.setTs(now);
        entity.setOperatorId(operator.getUserName());
        entity.setOperatorName(operator.getDisplayName());
        entity.setOperation(operationType.getCode());
        entity.setPageId(draft.getPageId());
        entity.setModuleId(draft.getModuleId() != null ? draft.getModuleId().toString() : null);
        entity.setDraftId(String.valueOf(draft.getId()));
        entity.setReleaseId(draft.getReleaseId() == null ? null : String.valueOf(draft.getReleaseId()));
        entity.setRemark(comment);
        entity.setContent(JsonUtil.toJSONString(draft));

        this.save(entity);
    }

}
