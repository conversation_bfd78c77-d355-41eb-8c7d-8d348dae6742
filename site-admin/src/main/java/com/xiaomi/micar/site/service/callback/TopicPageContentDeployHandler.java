package com.xiaomi.micar.site.service.callback;

import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageConfigEngine;
import com.xiaomi.micar.site.dao.SitePageInfoEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageConfigEntity;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.event.DiffContext;
import com.xiaomi.micar.site.model.bpm.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 页面内容部署处理器
 * 专门处理页面内容审核通过后的部署逻辑
 * 
 * <AUTHOR>
 * @since 2025/06/28
 */
@Slf4j
@Component
public class TopicPageContentDeployHandler implements DraftDeployHandler {

    @Resource
    private SitePageInfoEngine pageInfoEngine;
    @Resource
    private SitePageConfigEngine configEngine;
    @Resource
    private SitePageConfigDraftEngine draftEngine;
    @Resource
    private DraftStatusManager statusManager;

    @Override
    public boolean supports(Integer draftType) {
        return draftType != null && draftType.equals(DraftType.TOPIC_PAGE_CONTENT.getCode());
    }

    /**
     * 页面内容部署 - 严格事务控制
     * 
     * 注意：此方法会被BmpCallbackService在事务中调用，加入当前事务
     * 任何异常都会导致整个部署事务回滚
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public DiffContext deploy(SitePageConfigDraftEntity draft, boolean isOfflineAudit, Operator operator, String comment) {
        log.info("开始页面内容部署: draftId={}, pageId={}, isOfflineAudit={}",
                draft.getId(), draft.getPageId(), isOfflineAudit);

        if (isOfflineAudit) {
            // 下线审核：将页面标记为下线状态
            handlePageOffline(draft, operator, comment);
        } else {
            // 上线审核：创建新的页面配置或更新现有页面
            handlePageOnline(draft, operator, comment);
        }

        // 更新草稿状态为已部署
        updateDraftStatus(draft);

        log.info("页面内容部署成功: draftId={}, pageId={}, isOfflineAudit={}",
                draft.getId(), draft.getPageId(), isOfflineAudit);

        // 页面内容部署不需要生成diff，返回null
        return null;
    }

    @Override
    public String getHandlerName() {
        return "PageContentDeployHandler";
    }

    /**
     * 处理页面上线
     */
    private void handlePageOnline(SitePageConfigDraftEntity draft, Operator operator, String comment) {
        String pageId = draft.getPageId();

        // 1. 检查页面信息是否存在
        SitePageInfoEntity pageInfo = getOrCreatePageInfo(pageId);

        // 2. 生成新版本号
        Integer newVersion = generateNewVersion(pageId, pageInfo);

        // 3. 更新或创建页面配置（每个页面只有一条记录）
        updateOrCreatePageConfig(draft, newVersion);

        // 4. 更新页面信息版本和上线状态
        updatePageInfoWithStatus(pageInfo, newVersion, 1); // status=1 表示上线

        log.info("页面上线成功: pageId={}, version={}, draftId={}, status=1",
                pageId, newVersion, draft.getId());
    }

    /**
     * 处理页面下线
     * 下线逻辑：从 draft 表中找到上一个最新上线的版本替代 site_page_config 里面的生效数据
     */
    private void handlePageOffline(SitePageConfigDraftEntity draft, Operator operator, String comment) {
        String pageId = draft.getPageId();
        
        log.info("开始页面下线处理: pageId={}, currentDraftId={}", pageId, draft.getId());
        
        // 1. 查找历史上已经上线的最近版本配置
        String previousConfig = findPreviousOnlineConfig(draft);
        
        // 2. 获取当前页面信息
        SitePageInfoEntity pageInfo = pageInfoEngine.lambdaQuery()
                .eq(SitePageInfoEntity::getPageId, pageId)
                .one();
                
        if (pageInfo == null) {
            throw new RuntimeException("页面信息不存在: pageId=" + pageId);
        }
        
        // 3. 获取当前生效的配置
        SitePageConfigEntity currentConfig = getCurrentPageConfig(pageId);
        
        // 4. 生成新版本号
        Integer newVersion = generateNewVersion(pageId, pageInfo);
        
        if (previousConfig != null) {
            // 有历史版本：更新当前配置为历史版本的内容
            log.info("找到历史上线版本，回退配置: pageId={}", pageId);

            // 5. 更新当前配置为历史版本的内容
            currentConfig.setConfig(previousConfig);
            currentConfig.setVersion(newVersion);
            currentConfig.setUpdateTime(new Date());

            if (!configEngine.updateById(currentConfig)) {
                throw new RuntimeException("更新页面配置失败: pageId=" + pageId);
            }

            // 6. 更新页面信息版本，保持上线状态（回退到历史版本，页面仍然可用）
            updatePageInfoWithStatus(pageInfo, newVersion, 1); // status=1 表示仍然上线

            log.info("页面下线成功，回退到历史版本: pageId={}, newVersion={}, status=1", pageId, newVersion);
        } else {
            // 没有历史版本：将配置设为空，表示页面彻底下线
            log.info("没有找到历史上线版本，页面彻底下线: pageId={}", pageId);

            // 5. 使用 LambdaUpdateWrapper 强制更新 config 为 null
            boolean updateResult = configEngine.lambdaUpdate()
                    .eq(SitePageConfigEntity::getId, currentConfig.getId())
                    .set(SitePageConfigEntity::getConfig, null) // 强制设置为 null
                    .set(SitePageConfigEntity::getVersion, newVersion)
                    .set(SitePageConfigEntity::getUpdateTime, new Date())
                    .update();

            if (!updateResult) {
                throw new RuntimeException("更新页面配置失败: pageId=" + pageId);
            }

            // 6. 更新页面信息版本和下线状态（彻底下线）
            updatePageInfoWithStatus(pageInfo, newVersion, 0); // status=0 表示彻底下线

            log.info("页面彻底下线成功，设为空配置: pageId={}, newVersion={}, status=0", pageId, newVersion);
        }
    }

    /**
     * 获取当前生效的页面配置（必须存在）
     */
    private SitePageConfigEntity getCurrentPageConfig(String pageId) {
        SitePageConfigEntity currentConfig = configEngine.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, pageId)
                .one();
        
        if (currentConfig == null) {
            throw new RuntimeException("当前页面配置不存在: pageId=" + pageId);
        }
        
        return currentConfig;
    }

    /**
     * 检查是否是新增的Topic页面
     */
    private boolean isNewTopicPage(String pageId) {
        // 检查页面信息表中是否已存在该pageId
        SitePageInfoEntity existingPageInfo = pageInfoEngine.lambdaQuery()
                .eq(SitePageInfoEntity::getPageId, pageId)
                .one();

        return existingPageInfo == null;
    }

    /**
     * 获取或创建页面信息
     */
    private SitePageInfoEntity getOrCreatePageInfo(String pageId) {
        return pageInfoEngine.getOrCreatePageInfo(pageId);
    }

    /**
     * 生成新版本号
     */
    private Integer generateNewVersion(String pageId, SitePageInfoEntity pageInfo) {
        Integer maxVersion = configEngine.selectMaxVersion(pageId);
        Integer newVersion = (maxVersion != null) ? maxVersion + 1 : pageInfo.getVersion() + 1;

        log.info("生成新版本号: pageId={}, currentVersion={}, maxVersion={}, newVersion={}",
                pageId, pageInfo.getVersion(), maxVersion, newVersion);

        return newVersion;
    }

    /**
     * 更新或创建页面配置
     * site_page_config 表每个页面只有一条记录，存储当前线上生效的配置
     */
    private void updateOrCreatePageConfig(SitePageConfigDraftEntity draft, Integer version) {
        String pageId = draft.getPageId();

        // 查找现有的页面配置
        SitePageConfigEntity existingConfig = configEngine.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, pageId)
                .one();

        if (existingConfig != null) {
            // 更新现有配置
            existingConfig.setConfig(draft.getConfig());
            existingConfig.setVersion(version);
            existingConfig.setUpdateTime(new Date());

            if (!configEngine.updateById(existingConfig)) {
                throw new RuntimeException("更新页面配置失败: pageId=" + pageId + ", version=" + version);
            }

            log.info("更新页面配置成功: pageId={}, version={}, configId={}",
                    pageId, version, existingConfig.getId());
        } else {
            // 创建新配置（首次上线）
            SitePageConfigEntity newConfig = new SitePageConfigEntity();
            newConfig.setPageId(pageId);
            newConfig.setPageName(draft.getName() != null ? draft.getName() : pageId);
            newConfig.setConfig(draft.getConfig());
            newConfig.setVersion(version);
            newConfig.setCreateTime(new Date());
            newConfig.setUpdateTime(new Date());

            if (!configEngine.save(newConfig)) {
                throw new RuntimeException("创建页面配置失败: pageId=" + pageId + ", version=" + version);
            }

            log.info("创建页面配置成功: pageId={}, version={}, configId={}",
                    pageId, version, newConfig.getId());
        }
    }

    /**
     * 更新页面信息（包含状态）
     */
    private void updatePageInfoWithStatus(SitePageInfoEntity pageInfo, Integer newVersion, Integer status) {
        pageInfo.setVersion(newVersion);
        pageInfo.setStatus(status);
        if (!pageInfoEngine.updateById(pageInfo)) {
            throw new RuntimeException("更新页面信息失败: pageId=" + pageInfo.getPageId()
                    + ", version=" + newVersion + ", status=" + status);
        }

        log.info("更新页面信息成功: pageId={}, version={}, status={}",
                pageInfo.getPageId(), newVersion, status);
    }


    /**
     * 查找历史上已经上线的最近版本配置
     * <p>
     * 下线逻辑：
     * 1. 查找同一页面历史中已上线的版本（状态为 DEPLOYED）
     * 2. 如果找到，恢复到该上线版本的配置
     * 3. 如果找不到，返回 null，表示页面彻底下线
     */
    private String findPreviousOnlineConfig(SitePageConfigDraftEntity currentDraft) {
        try {
            SitePageConfigDraftEntity previousOnlineVersion = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getPageId, currentDraft.getPageId())
                    .eq(SitePageConfigDraftEntity::getType, DraftType.TOPIC_PAGE_CONTENT.getCode())
                    .eq(SitePageConfigDraftEntity::getStatus, ConfigStatus.DEPLOYED.getCode()) // 查找已上线的版本
                    .ne(SitePageConfigDraftEntity::getId, currentDraft.getId()) // 排除当前要下线的草稿
                    .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
                    .last("LIMIT 1")
                    .one();

            if (previousOnlineVersion != null) {
                log.info("找到历史上线版本: pageId={}, draftId={}, updateTime={}",
                        currentDraft.getPageId(), previousOnlineVersion.getId(), previousOnlineVersion.getUpdateTime());
                return previousOnlineVersion.getConfig();
            } else {
                log.info("未找到历史上线版本，页面将彻底下线: pageId={}",
                        currentDraft.getPageId());
                return null; // 返回 null 表示页面彻底下线
            }

        } catch (Exception e) {
            log.warn("查找历史上线版本失败: pageId={}, error={}", 
                    currentDraft.getPageId(), e.getMessage());
            return null; // 异常情况也返回 null，表示页面下线
        }
    }

    /**
     * 更新草稿状态
     */
    private void updateDraftStatus(SitePageConfigDraftEntity draft) {
        // 获取最新创建的配置ID作为releaseId
        SitePageConfigEntity latestConfig = configEngine.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, draft.getPageId())
                .orderByDesc(SitePageConfigEntity::getVersion)
                .last("limit 1")
                .one();

        Long releaseId = latestConfig != null ? latestConfig.getId() : null;

        // 使用直接转移方法，从当前状态（AUDITING/OFFLINE_AUDIT）直接转到DEPLOYED
        boolean updated = statusManager.executeDirectApprovalTransitionWithReleaseId(
                draft.getId(), draft.getStatus(), releaseId);

        if (!updated) {
            throw new RuntimeException("部署完成状态转移失败: draftId=" + draft.getId());
        }

        log.info("更新草稿状态成功: draftId={}, releaseId={}", draft.getId(), releaseId);
    }
}