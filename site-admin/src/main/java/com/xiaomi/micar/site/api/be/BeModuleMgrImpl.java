package com.xiaomi.micar.site.api.be;

import com.xiaomi.micar.site.api.be.aop.BeLoginCheck;
import com.xiaomi.micar.site.api.dto.*;
import com.xiaomi.micar.site.api.model.*;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageModuleEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleEntity;
import com.xiaomi.micar.site.enums.BpmOperationType;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.GroupCodeType;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.micar.site.service.BpmInternalService;
import com.xiaomi.micar.site.service.ConfigDraftService;
import com.xiaomi.micar.site.convert.TransformUtil;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 后台：页面模块管理
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Slf4j
@ApiModule(value = "后台管理#页面模块管理接口", apiInterface = BeModuleMgr.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class BeModuleMgrImpl implements BeModuleMgr {

    @Resource
    private SitePageConfigDraftEngine draftEngine;
    @Resource
    private ConfigDraftService draftService;
    @Resource
    private SitePageModuleEngine moduleEngine;
    @Resource
    private BpmInternalService bpmInternalService;

    @ApiDoc(value = "获取模块配置")
    @BeLoginCheck
    @Override
    public Result<RecordDTO> getDraftConfig(GetDraftReq req) {
        SitePageConfigDraftEntity entity = draftEngine.getById(req.getId(), req.getPageId(), req.getModuleId());
        if (entity == null) {
            return Result.fail(GeneralCodes.ParamError, "获取模块配置失败");
        }
        return Result.success(TransformUtil.draftToDTO(entity));
    }

    @ApiDoc(value = "保存模块配置")
    @Override
    @BeLoginCheck
    public Result<Void> saveDraftConfig(DraftSaveReq req) {
        draftService.saveOrUpdateModule(req.getUserInfo(), req);
        return Result.success(null);
    }

    @ApiDoc(value = "获取模块配置历史")
    @Override
    @BeLoginCheck
    public Result<CommonPagedData<RecordDTO>> draftList(DraftListReq req) {
        CommonPagedData<RecordDTO> result = draftService.moduleDraftList(req.getPageId(), req.getModuleId(), req.getStatus(), req.getPage(), req.getSize());
        return Result.success(result);
    }

    // ========== 新增的模块管理接口实现 ==========

    @ApiDoc(value = "新增模块配置")
    @Override
    @BeLoginCheck
    public Result<ModuleAddResp> addModule(ModuleAddReq req) {
        try {
            log.info("新增模块配置: pageId={}, moduleName={}, template={}, dataProvider={}",
                    req.getPageId(), req.getModuleName(), req.getTemplate(), req.getDataProvider());

            // 创建模块实体
            SitePageModuleEntity moduleEntity = new SitePageModuleEntity();
            moduleEntity.setPageId(req.getPageId());
            moduleEntity.setModuleName(req.getModuleName());
            moduleEntity.setTemplate(req.getTemplate());
            moduleEntity.setComponent(getComponentByTemplate(req.getTemplate()));

            // 设置数据提供者（从请求参数获取）
            moduleEntity.setDataProvider(req.getDataProvider());
            moduleEntity.setDataProviderParams(req.getDataProviderParams() != null ? req.getDataProviderParams() : "{}");

            // 设置时间字段
            Date now = new Date();
            moduleEntity.setCreateTime(now);
            moduleEntity.setUpdateTime(now);

            // 保存模块
            boolean saved = moduleEngine.save(moduleEntity);
            if (!saved) {
                return Result.fail(GeneralCodes.InternalError, "保存模块失败");
            }

            log.info("模块创建成功: moduleId={}", moduleEntity.getId());
            return Result.success(new ModuleAddResp(moduleEntity.getId()));

        } catch (Exception e) {
            log.error("新增模块配置异常: {}", e.getMessage(), e);
            return Result.fail(GeneralCodes.InternalError, "新增模块配置失败: " + e.getMessage());
        }
    }

    @ApiDoc(value = "查看模块列表")
    @Override
    @BeLoginCheck
    public Result<ModuleListResp> getModuleList(ModuleListReq req) {
        try {
            log.info("查询模块列表: pageId={}", req.getPageId());

            List<SitePageModuleEntity> modules = moduleEngine.lambdaQuery()
                    .eq(SitePageModuleEntity::getPageId, req.getPageId())
                    .orderByAsc(SitePageModuleEntity::getId)  // 按 ID 排序作为默认排序
                    .list();

            List<ModuleListResp.ModuleItem> moduleItems = modules.stream()
                    .map(module -> new ModuleListResp.ModuleItem(
                            module.getId(),
                            module.getModuleName(),
                            module.getTemplate()))
                    .collect(Collectors.toList());

            ModuleListResp response = new ModuleListResp();
            response.setPageId(req.getPageId());
            response.setModuleList(moduleItems);

            return Result.success(response);

        } catch (Exception e) {
            log.error("查询模块列表异常: {}", e.getMessage(), e);
            return Result.fail(GeneralCodes.InternalError, "查询模块列表失败: " + e.getMessage());
        }
    }



    @ApiDoc(value = "模块顺序管理（提交审核）")
    @Override
    @BeLoginCheck
    public Result<ModuleOrderResp> manageModuleOrder(ModuleOrderReq req) {
        try {
            log.info("提交模块顺序调整审核: pageId={}, strategyCode={}, modules={}",
                    req.getPageId(), req.getStrategyCode(), req.getModules().size());

            // 验证策略代码
            if (!GroupCodeType.isValid(req.getStrategyCode())) {
                return Result.fail(GeneralCodes.ParamError, "无效的策略代码: " + req.getStrategyCode());
            }

            // 构建页面配置数据
            ModuleOrderConfig orderConfig = buildModuleOrderConfig(req);
            String configJson = JsonUtil.toJSONString(orderConfig);

            // 获取登录用户信息
            String operatorId = req.getUserInfo().getAccount();
            String operatorName = req.getUserInfo().getUserName();

            // 创建草稿记录
            SitePageConfigDraftEntity draftEntity = new SitePageConfigDraftEntity();
            draftEntity.setType(4); // type=4 代表模块排序调整
            draftEntity.setPageId(req.getPageId());
            draftEntity.setName("模块顺序调整-" + req.getStrategyCode());
            draftEntity.setConfig(configJson);
            draftEntity.setRemark("调整" + req.getStrategyCode() + "策略下的模块顺序");
            draftEntity.setStatus(ConfigStatus.DRAFT.getCode());
            draftEntity.setOperatorId(operatorId);
            draftEntity.setOperatorName(operatorName);
            draftEntity.setGroupKey(req.getStrategyCode());

            // 保存草稿
            boolean saved = draftEngine.save(draftEntity);
            if (!saved) {
                return Result.fail(GeneralCodes.InternalError, "保存草稿失败");
            }

            log.info("模块顺序调整草稿创建成功: draftId={}, pageId={}, strategyCode={}",
                    draftEntity.getId(), req.getPageId(), req.getStrategyCode());

            // 自动提交BPM审核
            String remark = "模块顺序调整：" + req.getStrategyCode() + "策略";
            Result<Void> bpmResult = bpmInternalService.submitBpmApproval(
                    draftEntity.getId(),
                    BpmOperationType.PAGE_ADJUST.getCode(),
                    remark,
                    req.getUserInfo());

            if (bpmResult.getCode() != 0) {
                log.error("提交BPM审核失败: {}", bpmResult.getMessage());
                return Result.fail(GeneralCodes.InternalError, "提交审核失败: " + bpmResult.getMessage());
            }

            log.info("模块顺序调整已提交BPM审核: draftId={}", draftEntity.getId());

            // 构建响应
            List<ModuleOrderResp.ModuleOrderItem> moduleOrders = req.getModules().stream()
                    .map(item -> new ModuleOrderResp.ModuleOrderItem(
                            String.valueOf(item.getModuleId()),
                            item.getPriority()))
                    .collect(Collectors.toList());

            ModuleOrderResp response = new ModuleOrderResp();
            response.setPageId(req.getPageId());
            response.setStrategy(req.getStrategyCode());
            response.setModuleOrders(moduleOrders);

            return Result.success(response);

        } catch (Exception e) {
            log.error("提交模块顺序调整审核异常: {}", e.getMessage(), e);
            return Result.fail(GeneralCodes.InternalError, "提交审核失败: " + e.getMessage());
        }
    }

    @ApiDoc(value = "查看人群策略列表")
    @Override
    @BeLoginCheck
    public Result<List<StrategyItemDTO>> getStrategyList(StrategyListReq req) {
        try {
            log.info("查询人群策略列表: pageId={}", req.getPageId());

            // 返回所有支持的策略
            List<StrategyItemDTO> strategies = Arrays.stream(GroupCodeType.values())
                    .map(type -> new StrategyItemDTO(
                            type.getCode(),
                            type.getDescription().trim(),
                            type.getDescription().trim() + "用户分组"))
                    .collect(Collectors.toList());

            return Result.success(strategies);

        } catch (Exception e) {
            log.error("查询人群策略列表异常: {}", e.getMessage(), e);
            return Result.fail(GeneralCodes.InternalError, "查询人群策略列表失败: " + e.getMessage());
        }
    }



    // ========== 辅助方法 ==========



    /**
     * 根据模板获取组件类型
     */
    private String getComponentByTemplate(String template) {
        // 根据模板映射到组件类型，这里可以根据实际业务需求调整
        switch (template.toLowerCase()) {
            case "banner":
                return "Banner";
            case "news":
            case "fullwidthcard":
                return "News";
            case "navigation":
            case "verticalcard":
                return "Navigation";
            case "tabnews":
            case "tabscrolllist":
                return "TabNews";
            case "tabs":
                return "Tabs";
            case "tabfeeds":
                return "TabFeeds";
            case "activitylist":
                return "ActivityList";
            default:
                return "News"; // 默认组件类型
        }
    }



    // getNextPriority 方法已删除，因为 priority 字段已从 site_page_module 表中移除



    /**
     * 构建模块顺序配置
     */
    private ModuleOrderConfig buildModuleOrderConfig(ModuleOrderReq req) {
        ModuleOrderConfig config = new ModuleOrderConfig();
        config.setPageId(req.getPageId());
        config.setStrategy(req.getStrategyCode());
        config.setModules(req.getModules().stream()
                .map(item -> {
                    ModuleOrderConfig.ModuleItem moduleItem = new ModuleOrderConfig.ModuleItem();
                    moduleItem.setModuleId(String.valueOf(item.getModuleId()));
                    moduleItem.setPriority(item.getPriority());
                    return moduleItem;
                })
                .collect(Collectors.toList()));
        return config;
    }

    /**
     * 模块顺序配置数据类
     */
    public static class ModuleOrderConfig {
        private String pageId;
        private String strategy;
        private List<ModuleItem> modules;

        // getters and setters
        public String getPageId() { return pageId; }
        public void setPageId(String pageId) { this.pageId = pageId; }
        public String getStrategy() { return strategy; }
        public void setStrategy(String strategy) { this.strategy = strategy; }
        public List<ModuleItem> getModules() { return modules; }
        public void setModules(List<ModuleItem> modules) { this.modules = modules; }

        public static class ModuleItem {
            private String moduleId;
            private Integer priority;

            // getters and setters
            public String getModuleId() { return moduleId; }
            public void setModuleId(String moduleId) { this.moduleId = moduleId; }
            public Integer getPriority() { return priority; }
            public void setPriority(Integer priority) { this.priority = priority; }
        }
    }
}
