package com.xiaomi.micar.site.api.admin;

import com.xiaomi.micar.site.service.QRCodeGenerator;
import com.xiaomi.micar.site.service.PreviewUrlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/basic")
public class BasicController {

    @Resource
    private PreviewUrlService previewUrlService;

    @GetMapping(value = "/page/preview/qrcode", produces = MediaType.IMAGE_JPEG_VALUE)
    public void previewQrCode(@RequestParam String ct, @RequestParam String cv, HttpServletResponse response) throws Exception {
        // 参数校验
        if (StringUtils.isBlank(ct) || StringUtils.isBlank(cv)) {
            log.warn("参数不能为空: ct={}, cv={}", ct, cv);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "参数 ct 和 cv 不能为空");
            return;
        }

        try {
            // 校验 cv 是否为有效的数字
            Long draftId = Long.valueOf(cv);
            
            // 使用PreviewUrlService生成预览URL
            String content = previewUrlService.generatePreviewUrl(draftId, ct);

            log.info("生成预览二维码: cv={}, ct={}, url={}", cv, ct, content);

            QRCodeGenerator.generate(content, response.getOutputStream());
        } catch (NumberFormatException e) {
            log.warn("cv 参数格式错误: cv={}", cv, e);
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "cv 参数必须为有效的数字");
        } catch (IOException e) {
            log.error("响应写入失败: cv={}, ct={}", cv, ct, e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "生成二维码失败");
        }
    }


}
