package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 查看模块列表请求
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ModuleListReq extends BaseMgrReq {

    /**
     * 页面ID
     */
    @NotBlank(message = "页面ID不能为空")
    private String pageId;
}
