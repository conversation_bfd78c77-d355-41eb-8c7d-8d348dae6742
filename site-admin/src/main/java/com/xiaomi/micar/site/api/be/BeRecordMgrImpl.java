package com.xiaomi.micar.site.api.be;

import com.xiaomi.micar.site.api.be.aop.BeLoginCheck;
import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.api.model.DraftDeleteReq;
import com.xiaomi.micar.site.api.model.DraftListReq;
import com.xiaomi.micar.site.api.model.DraftBatchSaveReq;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.micar.site.service.ConfigDraftService;
import com.xiaomi.micar.site.service.RecordService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 后台：数据记录管理
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Slf4j
@ApiModule(value = "后台管理#数据记录管理接口", apiInterface = BeRecordMgr.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class BeRecordMgrImpl implements BeRecordMgr {

    @Resource
    private RecordService recordService;

    @Resource
    private ConfigDraftService configDraftService;

    @ApiDoc(value = "获取有效数据集列表")
    @BeLoginCheck
    @Override
    public Result<CommonPagedData<RecordDTO>> recordList(DraftListReq req) {
        List<RecordDTO> list = recordService.list(req.getPageId(), req.getModuleId());
        CommonPagedData<RecordDTO> data = new CommonPagedData<>();
        data.setRecords(list);
        data.setTotal((long) list.size());
        return Result.success(data);
    }

    @ApiDoc(value = "获取数据集草稿列表")
//    @BeLoginCheck
    @Override
    public Result<CommonPagedData<RecordDTO>> draftList(DraftListReq req) {
        CommonPagedData<RecordDTO> data = configDraftService.recordDraftList(req.getPageId(), req.getModuleId(), req.getStatus(), req.getPage(), req.getSize());
        return Result.success(data);
    }

    @ApiDoc(value = "保存数据集草稿列表")
    @BeLoginCheck
    @Override
    public Result<Void> saveBatch(DraftBatchSaveReq req) {
        configDraftService.saveRecords(req.getUserInfo(), req.getRecords());
        return Result.success(null);
    }

    @Override
    @ApiDoc(value = "删除数据集草稿列表")
    @BeLoginCheck
    public Result<Void> deleteBatch(DraftDeleteReq req) {
        configDraftService.deleteRecords(req.getUserInfo(), req.getIds());
        return Result.success(null);
    }
}
