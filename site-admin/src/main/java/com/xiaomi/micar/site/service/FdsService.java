package com.xiaomi.micar.site.service;


import com.mi.car.iccc.starter.fds.annotation.FdsClient;
import com.mi.car.iccc.starter.fds.model.DownloadResult;
import com.mi.car.iccc.starter.fds.service.IcccFdsClient;
import com.xiaomi.infra.galaxy.fds.client.exception.GalaxyFDSClientException;
import com.xiaomi.infra.galaxy.fds.result.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class FdsService {

    @FdsClient
    private IcccFdsClient client;


    public PutObjectResult uploadFile(InputStream in, String bucketName, String objectName) throws GalaxyFDSClientException {
        PutObjectResult putObjectResult = client.uploadFile(in, bucketName, objectName);
        log.info("上传结果={}", putObjectResult);
        return putObjectResult;
    }

    public PutObjectResult uploadFile(InputStream in, String bucketName, String objectName, String contentType) throws GalaxyFDSClientException, IOException {
        PutObjectResult putObjectResult = client.uploadFile(in, bucketName, objectName, contentType);
        log.info("上传结果带contentType,结果={}", putObjectResult);
        return putObjectResult;
    }


    private String getDateFormat() {
        LocalDate localDate = LocalDate.now();
        return localDate.format(DateTimeFormatter.ofPattern("YYYYMMdd"));
    }


    public void download(String bucketName, String objectName, String localDir) throws GalaxyFDSClientException, IOException {
        DownloadResult download = client.download(bucketName, objectName, localDir);
        log.info("download file success, localDir: {}", download);
    }

    public URI getDownloadUrl(String bucketName,
                              String objectName,
                              long expireTimeInMillSecond,
                              boolean allowOutsideAccess) throws GalaxyFDSClientException, URISyntaxException {
        return client.getPresignedDownloadUrl(bucketName, objectName, expireTimeInMillSecond, allowOutsideAccess);
    }
}
