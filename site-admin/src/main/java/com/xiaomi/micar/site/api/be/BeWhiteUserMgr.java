package com.xiaomi.micar.site.api.be;


import com.xiaomi.micar.site.api.model.WhiteUserDeleteReq;
import com.xiaomi.micar.site.api.model.WhiteUserListReq;
import com.xiaomi.micar.site.api.model.WhiteUserSaveReq;
import com.xiaomi.micar.site.dao.entity.SiteWhiteUserEntity;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 后端接口：白名单管理
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
public interface BeWhiteUserMgr {

    Result<CommonPagedData<SiteWhiteUserEntity>> list(WhiteUserListReq req);

    Result<Void> save(WhiteUserSaveReq req);

    Result<Void> delete(WhiteUserDeleteReq req);
}
