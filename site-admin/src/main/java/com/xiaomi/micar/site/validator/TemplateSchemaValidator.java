package com.xiaomi.micar.site.validator;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;

/**
 * 基于 JSON Schema 标准的模板验证器
 * 使用成熟的 JSON Schema 库进行配置验证
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Component
public class TemplateSchemaValidator {

    private static final Logger log = LoggerFactory.getLogger(TemplateSchemaValidator.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Map<String, JsonSchema> templateSchemas = new HashMap<>();
    private final ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
    private final JsonSchemaFactory schemaFactory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012);

    @PostConstruct
    public void loadSchemas() {
        try {
            log.info("开始扫描 JSON Schema 文件...");
            
            // 自动扫描 json-schemas 目录下的所有 JSON Schema 文件
            Resource[] resources = resourceResolver.getResources("classpath*:json-schemas/*.json");

            log.info("扫描到 {} 个 JSON Schema 文件", resources.length);

            for (Resource resource : resources) {
                log.info("文件名: {}, 存在: {}", resource.getFilename(), resource.exists());
                loadTemplateSchema(resource);
            }

            log.info("已加载 {} 个模板 JSON Schema", templateSchemas.size());
            log.info("已加载的模板: {}", templateSchemas.keySet());

        } catch (IOException e) {
            log.error("扫描 JSON Schema 目录失败", e);
        }
    }

    /**
     * 加载单个模板 JSON Schema
     */
    private void loadTemplateSchema(Resource resource) {
        try {
            // 从文件名提取模板名称（去掉 .json 后缀）
            String filename = resource.getFilename();
            if (filename == null || !filename.endsWith(".json")) {
                log.debug("跳过非JSON文件: {}", filename);
                return;
            }

            String templateName = filename.substring(0, filename.length() - 5); // 去掉 .json

            try (InputStream inputStream = resource.getInputStream()) {
                JsonNode schemaNode = objectMapper.readTree(inputStream);
                JsonSchema jsonSchema = schemaFactory.getSchema(schemaNode);
                templateSchemas.put(templateName, jsonSchema);
                log.debug("加载模板 JSON Schema: {} -> {}", templateName, resource.getURI());
            }

        } catch (Exception e) {
            log.error("加载模板 JSON Schema 失败: {}", resource.getFilename(), e);
        }
    }

    /**
     * 验证配置并返回详细错误信息
     *
     * @param template 模板名称
     * @param config 配置JSON字符串
     * @return 验证结果，包含是否通过和详细错误信息
     */
    public ValidationResult validateConfigWithDetails(String template, String config) {
        if (!StringUtils.hasText(template)) {
            log.warn("模板名称为空，跳过配置校验");
            return ValidationResult.success();
        }

        if (!StringUtils.hasText(config)) {
            log.error("配置内容为空");
            return ValidationResult.failure("root", "配置内容不能为空");
        }

        try {
            // 基础JSON格式校验
            JsonNode configNode = objectMapper.readTree(config);

            // 根据模板进行 JSON Schema 校验
            return validateByTemplateWithDetails(template, configNode);

        } catch (Exception e) {
            log.error("配置JSON格式错误: template={}, error={}", template, e.getMessage());
            return ValidationResult.failure("root", "JSON格式错误: " + e.getMessage());
        }
    }

    /**
     * 根据模板进行 JSON Schema 校验，返回详细错误信息
     *
     * @param template 模板名称
     * @param config 配置JsonNode
     * @return 验证结果，包含是否通过和错误详情
     */
    public ValidationResult validateByTemplateWithDetails(String template, JsonNode config) {
        JsonSchema schema = templateSchemas.get(template);

        if (schema == null) {
            log.debug("未找到模板 {} 的 JSON Schema 定义，仅校验JSON格式", template);
            return ValidationResult.success();
        }

        log.debug("使用 JSON Schema {} 进行配置校验", template);

        // 使用 JSON Schema 进行验证
        Set<ValidationMessage> validationMessages = schema.validate(config);

        if (validationMessages.isEmpty()) {
            log.debug("JSON Schema 验证通过: template={}", template);
            return ValidationResult.success();
        } else {
            // 收集所有验证错误
            List<ValidationError> errors = new ArrayList<>();
            for (ValidationMessage message : validationMessages) {
                errors.add(new ValidationError(
                    message.getPath(),
                    message.getMessage()
                ));
            }

            // 记录验证错误
            for (ValidationMessage message : validationMessages) {
                log.error("JSON Schema 验证失败: template={}, path={}, message={}",
                    template, message.getPath(), message.getMessage());
            }

            return ValidationResult.failure(errors);
        }
    }

}
