package com.xiaomi.micar.site.service.callback;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaomi.micar.site.dao.SitePageRecordEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageRecordEntity;
import com.xiaomi.micar.site.event.DiffContext;
import com.xiaomi.micar.site.model.bpm.Operator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 记录部署处理器
 * 专门处理记录类型的草稿部署
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class RecordDeployHandler implements DraftDeployHandler {

    @Resource
    private SitePageRecordEngine recordEngine;

    @Resource
    private DraftStatusManager statusManager;

    @Override
    public boolean supports(Integer draftType) {
        // 不再支持 RECORD 类型，统一走 ModuleDeployHandler 处理
        // 保留代码以备将来可能的恢复需求
        return false;
    }

    /**
     * 记录部署 - 严格事务控制
     *
     * 注意：此方法会被BmpCallbackService在事务中调用，加入当前事务
     * 任何异常都会导致整个部署事务回滚
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public DiffContext deploy(SitePageConfigDraftEntity draft, boolean isOfflineAudit, Operator operator, String comment) {
        // 如果是新记录（没有releaseId）
        if (draft.getReleaseId() == null || draft.getReleaseId() <= 0) {
            createNewRecord(draft);
        } else {
            // 处理已有记录的修改或删除
            SitePageRecordEntity existingRecord = getExistingRecord(draft);
            if (existingRecord == null) {
                throw new RuntimeException("未找到对应的记录，部署已终止");
            }
            if (isOfflineAudit) {
                handleRecordOffline(draft, existingRecord);
            } else {
                handleRecordUpdate(draft, existingRecord);
            }
        }

        log.info("记录部署成功: draftId={}, isOfflineAudit={}, operationDesc={}",
                draft.getId(), isOfflineAudit);
        // 记录部署不需要生成diff，返回null
        return null;
    }

    @Override
    public String getHandlerName() {
        return "RecordDeployHandler";
    }

    /**
     * 创建新记录
     */
    private void createNewRecord(SitePageConfigDraftEntity draft) {


        SitePageRecordEntity newRecord = buildNewRecord(draft);

        if (!recordEngine.save(newRecord)) {
            throw new RuntimeException("保存新记录失败，部署已终止");
        }

        // 使用直接转移方法，从当前状态（AUDITING/OFFLINE_AUDIT）直接转到DEPLOYED
        boolean updated = statusManager.executeDirectApprovalTransitionWithReleaseId(
                draft.getId(), draft.getStatus(), newRecord.getId());

        if (!updated) {
            throw new RuntimeException("部署完成状态转移失败，部署已完成但状态更新失败");
        }


    }

    /**
     * 构建新记录实体
     */
    private SitePageRecordEntity buildNewRecord(SitePageConfigDraftEntity draft) {
        SitePageRecordEntity newRecord = new SitePageRecordEntity();
        newRecord.setPageId(draft.getPageId());
        newRecord.setModuleId(draft.getModuleId() != null ? draft.getModuleId().toString() : null);
        newRecord.setName(draft.getName());
        newRecord.setConfig(draft.getConfig());
        newRecord.setPriority(draft.getPriority());
        newRecord.setExposeFrom(draft.getExposeFrom());
        newRecord.setExposeTo(draft.getExposeTo());
        newRecord.setRemark(draft.getRemark());
        newRecord.setOperatorId(draft.getOperatorId());
        newRecord.setOperatorName(draft.getOperatorName());
        newRecord.setRecordGroup(draft.getGroupKey());
        return newRecord;
    }

    /**
     * 获取已有记录
     */
    private SitePageRecordEntity getExistingRecord(SitePageConfigDraftEntity draft) {
        SitePageRecordEntity existingRecord = recordEngine.getOne(
                new LambdaQueryWrapper<SitePageRecordEntity>()
                        .eq(SitePageRecordEntity::getPageId, draft.getPageId())
                        .eq(SitePageRecordEntity::getId, draft.getReleaseId())
                        .eq(SitePageRecordEntity::getModuleId, draft.getModuleId())
        );

        if (existingRecord == null) {
            log.error("未找到记录: draftId={}, releaseId={}", draft.getId(), draft.getReleaseId());
        }

        return existingRecord;
    }

    /**
     * 处理记录下线
     */
    private void handleRecordOffline(SitePageConfigDraftEntity draft, SitePageRecordEntity existingRecord) {


        // 检查记录是否已经被逻辑删除
        if (existingRecord.getDeleted() != null && existingRecord.getDeleted() == 1) {
            throw new RuntimeException("记录已经被下线，不能重复下线");
        }

        // 逻辑删除记录
        if (!recordEngine.removeById(existingRecord.getId())) {
            throw new RuntimeException("删除记录失败，部署已终止");
        }

        // 使用直接转移方法，从当前状态（OFFLINE_AUDIT）直接转到DEPLOYED  
        boolean updated = statusManager.executeDirectApprovalTransitionWithReleaseId(
                draft.getId(), draft.getStatus(), 0L);

        if (!updated) {
            throw new RuntimeException("下线完成状态转移失败，部署已完成但状态更新失败");
        }


    }

    /**
     * 处理记录更新
     */
    private void handleRecordUpdate(SitePageConfigDraftEntity draft, SitePageRecordEntity existingRecord) {


        // 更新记录字段
        updateRecordFields(draft, existingRecord);

        if (!recordEngine.updateById(existingRecord)) {
            throw new RuntimeException("更新记录失败，部署已终止");
        }

        // 更新草稿状态
        boolean updated = statusManager.executeDeploymentCompleteTransition(draft.getId(), false);

        if (!updated) {
            throw new RuntimeException("部署完成状态转移失败，部署已完成但状态更新失败");
        }


    }

    /**
     * 更新记录字段
     */
    private void updateRecordFields(SitePageConfigDraftEntity draft, SitePageRecordEntity existingRecord) {
        existingRecord.setConfig(draft.getConfig());
        existingRecord.setPriority(draft.getPriority());
        existingRecord.setRemark(draft.getRemark());
        existingRecord.setExposeFrom(draft.getExposeFrom());
        existingRecord.setExposeTo(draft.getExposeTo());
        existingRecord.setOperatorId(draft.getOperatorId());
        existingRecord.setOperatorName(draft.getOperatorName());
    }
}
