package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 获取专题页草稿列表请求
 *
 * <AUTHOR>
 * @since 2025/06/29
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TopicPageDraftListReq extends BaseMgrReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页面ID（可选，如果不传则查询所有专题页的草稿）
     */
    private String pageId;

    /**
     * 草稿状态（可选，如果不传则查询所有状态的草稿）
     */
    private Integer status;

    /**
     * 专题页名称搜索关键词（可选，支持模糊搜索）
     */
    private String name;

    /**
     * 页码，默认第1页
     */
    private Long page = 1L;

    /**
     * 每页大小，默认20条
     */
    private Long size = 20L;
}
