package com.xiaomi.micar.site.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.dao.SitePageRecordEngine;
import com.xiaomi.micar.site.dao.entity.SitePageRecordEntity;
import com.xiaomi.micar.site.convert.TransformUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集管理
 *
 * <AUTHOR>
 * @since 2025/04/17
 */
@Service
public class RecordService {


    @Resource
    private SitePageRecordEngine recordEngine;

    /**
     * 记录列表（分页）
     *
     * @param pageId
     * @param moduleId
     * @return
     */
    public List<RecordDTO> list(String pageId, Integer moduleId) {
        return recordEngine.list(new LambdaQueryWrapper<>(SitePageRecordEntity.class)
                        .eq(SitePageRecordEntity::getPageId, pageId)
                        .eq(SitePageRecordEntity::getModuleId, moduleId.toString())
                        .orderByAsc(SitePageRecordEntity::getPriority)
                        .orderByDesc(SitePageRecordEntity::getUpdateTime) // 优先级相同时，按更新时间倒序（最近的在前）
                )
                .stream()
                .map(TransformUtil::entityToDTO)
                .collect(Collectors.toList());
    }

}
