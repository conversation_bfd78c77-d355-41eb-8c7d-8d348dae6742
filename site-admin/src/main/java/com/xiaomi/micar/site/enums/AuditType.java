package com.xiaomi.micar.site.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核类型枚举
 * 用于明确区分上线审核和下线审核，避免布尔值判断的混淆
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@AllArgsConstructor
@Getter
public enum AuditType {
    
    /**
     * 上线审核
     * 将草稿配置部署到线上
     */
    ONLINE("上线审核", "将草稿配置部署到线上"),
    
    /**
     * 下线审核  
     * 将线上配置下线或回滚
     */
    OFFLINE("下线审核", "将线上配置下线或回滚");
    
    private final String name;
    private final String description;
    
    /**
     * 根据草稿状态判断审核类型
     *
     * @param draftStatus 草稿状态
     * @return 审核类型
     */
    public static AuditType fromDraftStatus(Integer draftStatus) {
        if (draftStatus == null) {
            throw new IllegalArgumentException("草稿状态不能为空");
        }
        
        if (draftStatus.equals(ConfigStatus.AUDITING.getCode())) {
            return ONLINE;
        } else if (draftStatus.equals(ConfigStatus.OFFLINE_AUDIT.getCode())) {
            return OFFLINE;
        } else {
            throw new IllegalArgumentException("无效的审核状态: " + draftStatus + 
                    ", 期望状态: " + ConfigStatus.AUDITING.getCode() + "(上线审核) 或 " + 
                    ConfigStatus.OFFLINE_AUDIT.getCode() + "(下线审核)");
        }
    }
    
    /**
     * 是否为上线审核
     */
    public boolean isOnline() {
        return this == ONLINE;
    }
    
    /**
     * 是否为下线审核
     */
    public boolean isOffline() {
        return this == OFFLINE;
    }
    
    /**
     * 获取部署完成后的目标状态
     */
    public ConfigStatus getTargetStatus() {
        return this == ONLINE ? ConfigStatus.DEPLOYED : ConfigStatus.DRAFT;
    }
}
