package com.xiaomi.micar.site.api.be;


import com.xiaomi.micar.site.api.dto.*;
import com.xiaomi.micar.site.api.model.*;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.List;

/**
 * 后端接口：页面模块管理
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
public interface BeModuleMgr {

    Result<RecordDTO> getDraftConfig(GetDraftReq req);

    Result<Void> saveDraftConfig(DraftSaveReq req);

    Result<CommonPagedData<RecordDTO>> draftList(DraftListReq req);

    // ========== 新增的模块管理接口 ==========

    /**
     * 新增模块配置
     */
    Result<ModuleAddResp> addModule(ModuleAddReq req);

    /**
     * 查看模块列表
     */
    Result<ModuleListResp> getModuleList(ModuleListReq req);

    /**
     * 模块顺序管理（提交审核）
     */
    Result<ModuleOrderResp> manageModuleOrder(ModuleOrderReq req);

    /**
     * 查看人群策略列表
     */
    Result<List<StrategyItemDTO>> getStrategyList(StrategyListReq req);

}
