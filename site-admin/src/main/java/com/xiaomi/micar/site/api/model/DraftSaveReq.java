package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.model.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 获取站点模块配置
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DraftSaveReq extends RecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private UserInfo userInfo;

}