package com.xiaomi.micar.site.service.callback;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.dao.SitePageConfigEngine;
import com.xiaomi.micar.site.dao.SitePageInfoEngine;
import com.xiaomi.micar.site.dao.SitePageModuleStrategyEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageConfigEntity;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleStrategyEntity;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.event.DiffContext;
import com.xiaomi.micar.site.model.bpm.Operator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模块排序部署处理器
 * 专门处理模块排序审核类型的草稿部署
 * 主要用于页面内模块顺序的调整
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class ModuleReOrderDeployHandler implements DraftDeployHandler {

    @Resource
    private SitePageModuleStrategyEngine moduleStrategyEngine;
    @Resource
    private SitePageInfoEngine pageInfoEngine;
    @Resource
    private SitePageConfigEngine configEngine;
    @Resource
    private DraftStatusManager statusManager;

    @Override
    public boolean supports(Integer draftType) {
        return draftType != null && draftType.equals(DraftType.PAGE_STRATEGY_MODIFY.getCode());
    }

    /**
     * 页面部署 - 严格事务控制
     *
     * 注意：此方法会被BmpCallbackService在事务中调用，加入当前事务
     * 任何异常都会导致整个部署事务回滚
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public DiffContext deploy(SitePageConfigDraftEntity draft, boolean isOfflineAudit, Operator operator, String comment) {

        // 1. 解析草稿配置并保存模块顺序
        ModuleOrderConfig orderConfig = parseOrderConfig(draft);
        deleteExistingStrategy(orderConfig);
        List<SitePageModuleStrategyEntity> entities = createStrategyEntities(orderConfig);
        saveStrategyEntities(entities);

        // 2. 重新生成页面配置（关键：模块顺序调整后需要更新页面配置）
        SitePageInfoEntity pageInfo = getPageInfo(draft);
        SitePageConfigEntity currentConfig = getCurrentConfig(draft, pageInfo);
        Integer newVersion = generateNewVersion(draft, pageInfo);
        String reorderedConfig = reorderPageConfig(currentConfig.getConfig(), entities);
        SitePageConfigEntity newConfig = createNewConfig(draft, newVersion, reorderedConfig);
        saveNewConfig(newConfig);

        // 3. 更新页面信息版本
        updatePageInfo(pageInfo, newVersion);

        // 4. 更新草稿状态为已部署
        updateDraftStatus(draft, newConfig);

        log.info("模块排序部署成功: draftId={}, pageId={}, strategy={}, 配置数量={}, newVersion={}",
                draft.getId(), orderConfig.getPageId(), orderConfig.getStrategy(), entities.size(), newVersion);

        // 模块重排序不需要生成diff，返回null
        return null;
    }

    @Override
    public String getHandlerName() {
        return "ModuleOrderDeployHandler";
    }

    /**
     * 解析模块顺序配置
     */
    private ModuleOrderConfig parseOrderConfig(SitePageConfigDraftEntity draft) {
        try {
            ModuleOrderConfig orderConfig = JsonUtil.parseObject(draft.getConfig(), ModuleOrderConfig.class);
            if (orderConfig == null) {
                log.error("解析模块顺序配置失败: draftId={}, config={}", draft.getId(), draft.getConfig());
                throw new RuntimeException("无效的模块顺序配置：配置为空");
            }
            
            if (orderConfig.getModules() == null || orderConfig.getModules().isEmpty()) {
                log.error("模块列表为空: draftId={}, orderConfig={}", draft.getId(), orderConfig);
                throw new RuntimeException("无效的模块顺序配置：模块列表为空");
            }


            
            return orderConfig;
        } catch (Exception e) {
            log.error("解析模块顺序配置异常: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            throw new RuntimeException("解析模块顺序配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除现有的策略配置
     */
    private void deleteExistingStrategy(ModuleOrderConfig orderConfig) {

        
        boolean deleted = moduleStrategyEngine.deleteByPageIdAndStrategy(
                orderConfig.getPageId(), orderConfig.getStrategy());
        
        if (!deleted) {
            log.warn("删除现有策略配置失败或无数据: pageId={}, strategy={}", 
                    orderConfig.getPageId(), orderConfig.getStrategy());
        }
    }

    /**
     * 创建策略实体列表
     */
    private List<SitePageModuleStrategyEntity> createStrategyEntities(ModuleOrderConfig orderConfig) {
        // 构建 config 字段：将模块ID列表转换为JSON数组格式
        List<String> moduleIds = orderConfig.getModules().stream()
                .map(ModuleOrderConfig.ModuleItem::getModuleId)
                .collect(Collectors.toList());
        String configJson = JsonUtil.toJSONString(moduleIds);

        // 为每个策略创建一个实体，config 字段包含所有模块ID
        SitePageModuleStrategyEntity entity = new SitePageModuleStrategyEntity();
        entity.setPageId(orderConfig.getPageId());
        entity.setStrategy(orderConfig.getStrategy());
        entity.setPriority(1); // 策略级别的优先级
        entity.setConfig(configJson); // 设置包含模块ID列表的config字段

        return Collections.singletonList(entity);
    }

    /**
     * 保存策略实体
     */
    private void saveStrategyEntities(List<SitePageModuleStrategyEntity> entities) {

        
        boolean saved = moduleStrategyEngine.saveBatch(entities);
        if (!saved) {
            log.error("保存模块顺序配置失败: entities={}", entities);
            throw new RuntimeException("保存模块顺序配置失败");
        }
        

    }

    /**
     * 获取页面信息
     */
    private SitePageInfoEntity getPageInfo(SitePageConfigDraftEntity draft) {
        SitePageInfoEntity pageInfo = pageInfoEngine.lambdaQuery()
                .eq(SitePageInfoEntity::getPageId, draft.getPageId())
                .one();

        if (pageInfo == null) {
            log.error("页面信息不存在: pageId={}", draft.getPageId());
            throw new RuntimeException("页面信息不存在，部署已终止");
        }

        return pageInfo;
    }

    /**
     * 获取当前配置
     */
    private SitePageConfigEntity getCurrentConfig(SitePageConfigDraftEntity draft, SitePageInfoEntity pageInfo) {
        SitePageConfigEntity currentConfig = configEngine.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, draft.getPageId())
                .eq(SitePageConfigEntity::getVersion, pageInfo.getVersion())
                .one();

        if (currentConfig == null) {
            log.error("当前页面配置不存在: pageId={}, version={}",
                    draft.getPageId(), pageInfo.getVersion());
            throw new RuntimeException("当前页面配置不存在，部署已终止");
        }

        return currentConfig;
    }

    /**
     * 生成新版本号
     */
    private Integer generateNewVersion(SitePageConfigDraftEntity draft, SitePageInfoEntity pageInfo) {
        Integer maxVersion = configEngine.selectMaxVersion(draft.getPageId());
        Integer newVersion = (maxVersion != null) ? maxVersion + 1 : pageInfo.getVersion() + 1;

        log.info("生成新版本号: pageId={}, currentVersion={}, maxVersion={}, newVersion={}",
                draft.getPageId(), pageInfo.getVersion(), maxVersion, newVersion);

        return newVersion;
    }

    /**
     * 根据新的模块顺序重排序页面配置
     */
    private String reorderPageConfig(String currentConfig, List<SitePageModuleStrategyEntity> newOrder) {
        try {
            ObjectNode configNode = (ObjectNode) JsonUtil.readTree(currentConfig);
            JsonNode componentsNode = configNode.get("components");

            if (componentsNode == null || !componentsNode.isArray()) {
                log.warn("当前配置中没有 components 数组，返回原配置");
                return currentConfig;
            }

            ArrayNode componentsArray = (ArrayNode) componentsNode;
            ArrayNode reorderedArray = (ArrayNode) JsonUtil.readTree("[]");

            // 按照新的顺序重新排列组件
            Map<String, JsonNode> componentMap = new java.util.HashMap<>();
            for (JsonNode component : componentsArray) {
                if (component.has("id")) {
                    componentMap.put(component.get("id").asText(), component);
                }
            }

            // 按照新的优先级顺序添加组件
            newOrder.stream()
                    .sorted((a, b) -> Integer.compare(a.getPriority(), b.getPriority()))
                    .forEach(entity -> {
                        JsonNode component = componentMap.get(entity.getModuleId().toString());
                        if (component != null) {
                            reorderedArray.add(component);
                        }
                    });

            // 添加不在新顺序中的组件（保持原有组件不丢失）
            for (JsonNode component : componentsArray) {
                if (component.has("id")) {
                    String moduleId = component.get("id").asText();
                    boolean found = newOrder.stream()
                            .anyMatch(entity -> entity.getModuleId().toString().equals(moduleId));
                    if (!found) {
                        reorderedArray.add(component);
                    }
                }
            }

            configNode.set("components", reorderedArray);
            return JsonUtil.toJSONLogString(configNode);

        } catch (Exception e) {
            log.error("重排序页面配置失败: error={}", e.getMessage(), e);
            throw new RuntimeException("重排序页面配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建新配置
     */
    private SitePageConfigEntity createNewConfig(SitePageConfigDraftEntity draft,
                                               Integer newVersion, String reorderedConfig) {
        SitePageConfigEntity newConfig = new SitePageConfigEntity();
        newConfig.setPageId(draft.getPageId());
        newConfig.setPageName(draft.getPageId());
        newConfig.setConfig(reorderedConfig);
        newConfig.setVersion(newVersion);
        newConfig.setCreateTime(new Date());
        newConfig.setUpdateTime(new Date());
        return newConfig;
    }

    /**
     * 保存新配置
     */
    private void saveNewConfig(SitePageConfigEntity newConfig) {
        if (!configEngine.save(newConfig)) {
            log.error("保存新配置失败: pageId={}, version={}",
                    newConfig.getPageId(), newConfig.getVersion());
            throw new RuntimeException("保存新配置失败，部署已终止");
        }

        log.info("保存新配置成功: pageId={}, version={}",
                newConfig.getPageId(), newConfig.getVersion());
    }

    /**
     * 更新页面信息
     */
    private void updatePageInfo(SitePageInfoEntity pageInfo, Integer newVersion) {
        pageInfo.setVersion(newVersion);
        pageInfo.setUpdateTime(new Date());

        if (!pageInfoEngine.updateById(pageInfo)) {
            log.error("更新页面信息失败: pageId={}, version={}",
                    pageInfo.getPageId(), newVersion);
            throw new RuntimeException("更新页面信息失败，部署已终止");
        }

        log.info("更新页面信息成功: pageId={}, version={}",
                pageInfo.getPageId(), newVersion);
    }

    /**
     * 更新草稿状态
     */
    private void updateDraftStatus(SitePageConfigDraftEntity draft, SitePageConfigEntity newConfig) {
        // 使用直接转移方法，从当前状态（AUDITING/OFFLINE_AUDIT）直接转到DEPLOYED
        boolean updated = statusManager.executeDirectApprovalTransitionWithReleaseId(
                draft.getId(), draft.getStatus(), newConfig.getId());

        if (!updated) {
            log.error("部署完成状态转移失败: draftId={}", draft.getId());
            throw new RuntimeException("部署完成状态转移失败，部署已完成但状态更新失败");
        }

        log.info("更新草稿状态成功: draftId={}, releaseId={}",
                draft.getId(), newConfig.getId());
    }

    /**
     * 模块顺序配置类
     */
    public static class ModuleOrderConfig {
        private String pageId;
        private String strategy;
        private List<ModuleItem> modules;

        // getters and setters
        public String getPageId() { return pageId; }
        public void setPageId(String pageId) { this.pageId = pageId; }
        public String getStrategy() { return strategy; }
        public void setStrategy(String strategy) { this.strategy = strategy; }
        public List<ModuleItem> getModules() { return modules; }
        public void setModules(List<ModuleItem> modules) { this.modules = modules; }

        public static class ModuleItem {
            private String moduleId;
            private Integer priority;

            // getters and setters
            public String getModuleId() { return moduleId; }
            public void setModuleId(String moduleId) { this.moduleId = moduleId; }
            public Integer getPriority() { return priority; }
            public void setPriority(Integer priority) { this.priority = priority; }
        }
    }
}
