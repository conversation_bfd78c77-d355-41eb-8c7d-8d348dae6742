package com.xiaomi.micar.site.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模块顺序调整响应
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModuleOrderResp {

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 策略
     */
    private String strategy;

    /**
     * 模块顺序列表
     */
    private List<ModuleOrderItem> moduleOrders;

    /**
     * 模块顺序项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ModuleOrderItem {
        /**
         * 模块ID
         */
        private String moduleId;

        /**
         * 优先级
         */
        private Integer priority;
    }
}
