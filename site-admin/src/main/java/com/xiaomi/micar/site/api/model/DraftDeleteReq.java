package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 删除数据req
 *
 * <AUTHOR>
 * @since 2025/04/12
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DraftDeleteReq extends BaseMgrReq implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<Long> ids;
}