package com.xiaomi.micar.site.validator;

import lombok.Data;
import java.util.List;
import java.util.ArrayList;

/**
 * 模板验证结果
 *
 * <AUTHOR>
 * @since 2025/01/21
 */
@Data
public class ValidationResult {
    
    /**
     * 验证是否通过
     */
    private boolean valid;
    
    /**
     * 错误信息列表
     */
    private List<ValidationError> errors;
    
    /**
     * 错误摘要信息
     */
    private String errorSummary;
    
    public ValidationResult() {
        this.errors = new ArrayList<>();
    }
    
    public ValidationResult(boolean valid, List<ValidationError> errors) {
        this.valid = valid;
        this.errors = errors != null ? errors : new ArrayList<>();
        this.errorSummary = generateErrorSummary();
    }
    
    /**
     * 创建成功的验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult(true, null);
    }
    
    /**
     * 创建失败的验证结果
     */
    public static ValidationResult failure(List<ValidationError> errors) {
        return new ValidationResult(false, errors);
    }
    
    /**
     * 创建失败的验证结果（单个错误）
     */
    public static ValidationResult failure(String path, String message) {
        List<ValidationError> errors = new ArrayList<>();
        errors.add(new ValidationError(path, message));
        return new ValidationResult(false, errors);
    }
    
    /**
     * 生成错误摘要信息
     */
    private String generateErrorSummary() {
        if (valid || errors.isEmpty()) {
            return null;
        }
        
        if (errors.size() == 1) {
            ValidationError error = errors.get(0);
            return String.format("字段 '%s' 验证失败: %s", error.getPath(), error.getMessage());
        } else {
            return String.format("发现 %d 个验证错误，请检查配置格式", errors.size());
        }
    }
    
    /**
     * 获取格式化的错误信息（用于前端显示）
     */
    public String getFormattedErrorMessage() {
        if (valid || errors.isEmpty()) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("配置验证失败:\n");
        
        for (int i = 0; i < errors.size(); i++) {
            ValidationError error = errors.get(i);
            sb.append(String.format("%d. 字段 '%s': %s", 
                i + 1, error.getPath(), error.getMessage()));
            if (i < errors.size() - 1) {
                sb.append("\n");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 添加错误信息
     */
    public void addError(ValidationError error) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(error);
        this.valid = false;
        this.errorSummary = generateErrorSummary();
    }
    
    /**
     * 添加错误信息
     */
    public void addError(String path, String message) {
        addError(new ValidationError(path, message));
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return !valid && errors != null && !errors.isEmpty();
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
}