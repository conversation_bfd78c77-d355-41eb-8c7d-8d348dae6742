package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;

import java.util.List;

/**
 * 专题页列表响应
 */
public class TopicPageListResp {

    private List<SitePageInfoEntity> topicPages;

    private Integer total;

    public TopicPageListResp() {
    }

    public TopicPageListResp(List<SitePageInfoEntity> topicPages) {
        this.topicPages = topicPages;
        this.total = topicPages != null ? topicPages.size() : 0;
    }

    public List<SitePageInfoEntity> getTopicPages() {
        return topicPages;
    }

    public void setTopicPages(List<SitePageInfoEntity> topicPages) {
        this.topicPages = topicPages;
        this.total = topicPages != null ? topicPages.size() : 0;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}