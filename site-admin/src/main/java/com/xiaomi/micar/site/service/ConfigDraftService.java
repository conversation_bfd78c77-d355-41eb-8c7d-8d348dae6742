package com.xiaomi.micar.site.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.car.iccc.misc.idcenter.IdCenter;
import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.dao.SiteOpsLogEngine;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageRecordEngine;
import com.xiaomi.micar.site.dao.SitePageModuleEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleEntity;
import com.xiaomi.micar.site.validator.TemplateSchemaValidator;
import com.xiaomi.micar.site.validator.ValidationResult;
import com.xiaomi.micar.site.enums.BpmOperationType;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.enums.OperationType;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.micar.site.model.UserInfo;
import com.xiaomi.micar.site.convert.TransformUtil;
import com.xiaomi.micar.site.filter.ResultUtils;
import com.xiaomi.youpin.infra.rpc.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 草稿管理
 *
 * <AUTHOR>
 * @since 2025/04/17
 */
@Slf4j
@Service
public class ConfigDraftService {
    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Resource
    private SitePageRecordEngine recordEngine;

    @Resource
    private SiteOpsLogEngine opsLogEngine;

    @Resource
    private BpmInternalService bpmInternalService;

    @Resource
    private SitePageModuleEngine moduleEngine;

    @Resource
    private TemplateSchemaValidator templateSchemaValidator;

    /**
     * 记录列表（分页）
     *
     * @param pageId
     * @param moduleId
     * @param status
     * @param page
     * @param size
     * @return
     */
    public CommonPagedData<RecordDTO> recordDraftList(String pageId, Integer moduleId, Integer status, Long page, Long size) {
        Page<SitePageConfigDraftEntity> pg = Page.of(page, size);
        pg = draftEngine.page(pg, new LambdaQueryWrapper<>(SitePageConfigDraftEntity.class)
                .eq(SitePageConfigDraftEntity::getPageId, pageId)
                .eq(SitePageConfigDraftEntity::getModuleId, moduleId)
//                .eq(SitePageConfigDraftEntity::getType, DraftType.RECORD_CONTENT.getCode())
                .eq(status != null, SitePageConfigDraftEntity::getStatus, status)
                .orderByAsc(SitePageConfigDraftEntity::getPriority)
                .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
        );
        CommonPagedData<RecordDTO> result = new CommonPagedData<>();
        result.setTotal(pg.getTotal());
        result.setRecords(pg.getRecords().stream().map(TransformUtil::draftToDTO).collect(Collectors.toList()));
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateModule(UserInfo userInfo, RecordDTO record) {
        // validate
        Assert.isTrue(StringUtils.isNotEmpty(record.getPageId()), "配置有误");
        Assert.isTrue(record.getModuleId() != null, "配置有误");
        Assert.isTrue(StringUtils.isNotEmpty(record.getConfig()), "配置有误");

        // 校验配置结构
        validateConfig(record.getPageId(), record.getModuleId(), record.getConfig());

        SitePageConfigDraftEntity entity;
        boolean isModifyingDeployedDraft = false; // 标记是否在修改已上线的草稿

        if (record.getId() != null) {
            entity = draftEngine.getById(record.getId(), record.getPageId(), record.getModuleId());
            Assert.notNull(entity, "配置不存在");
            Assert.isTrue(ConfigStatus.getByCode(entity.getStatus()).isEditable(), "当前状态不允许编辑");
            Assert.isTrue(entity.getType() == DraftType.MODULE_CONTENT.getCode(), "配置无效");

            // 检查原始状态是否为已上线
            if (entity.getStatus() == ConfigStatus.DEPLOYED.getCode()) {
                isModifyingDeployedDraft = true;
                log.info("检测到修改已上线草稿，将自动提交审核: draftId={}, pageId={}, moduleId={}",
                        entity.getId(), entity.getPageId(), entity.getModuleId());
            }

            entity = TransformUtil.dtoToDraft(record, entity);
            entity.setOperatorId(userInfo.getAccount());
            entity.setOperatorName(userInfo.getUserName());
            entity.setStatus(ConfigStatus.DRAFT.getCode());
        } else {
            entity = TransformUtil.dtoToDraft(record, null);
            entity.setType(DraftType.MODULE_CONTENT.getCode());
            entity.setOperatorId(userInfo.getAccount());
            entity.setOperatorName(userInfo.getUserName());
            entity.setStatus(ConfigStatus.DRAFT.getCode());
        }

        // 保存草稿
        draftEngine.saveOrUpdate(entity);

        // 如果是修改已上线的草稿，自动提交审核
        if (isModifyingDeployedDraft && entity.getId() != null) {
            autoSubmitBpmApproval(entity.getId(), userInfo);
        }
    }

    /**
     * 自动提交BPM审核
     * 当修改已上线的草稿时自动调用
     * 注意：此方法会抛出异常，确保事务回滚
     */
    private void autoSubmitBpmApproval(Long draftId, UserInfo userInfo) {
        log.info("开始自动提交BPM审核: draftId={}, user={}", draftId, userInfo.getAccount());

        // 调用BPM服务提交修改审核
        Result<Void> result = bpmInternalService.submitBpmApproval(
                draftId,
                BpmOperationType.MODIFY.getCode(), // 修改审核
                "系统自动提交：修改已上线配置", // 自动生成的备注
                userInfo
        );

        if (ResultUtils.isSuccess(result)) {
            log.info("自动提交BPM审核成功: draftId={}, user={}", draftId, userInfo.getAccount());
        } else {
            String errorMsg = String.format("自动提交BPM审核失败: draftId=%d, user=%s, error=%s",
                    draftId, userInfo.getAccount(), result.getMessage());
            log.error(errorMsg);
            // 抛出异常，触发事务回滚
            throw new RuntimeException(errorMsg);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRecords(UserInfo userInfo, List<RecordDTO> list) {
        // validate
        list.forEach(record -> {
            Assert.isTrue(StringUtils.isNotEmpty(record.getPageId()), "配置有误");
            Assert.isTrue(record.getModuleId() != null, "配置有误");
            Assert.isTrue(StringUtils.isNotEmpty(record.getConfig()), "配置有误");
        });

        List<SitePageConfigDraftEntity> insertList = list.stream()
                .filter(record -> record.getId() == null)
                .map(record -> {
                    SitePageConfigDraftEntity entity = TransformUtil.dtoToDraft(record, null);
                    entity.setType(DraftType.RECORD_CONTENT.getCode());
                    entity.setOperatorId(userInfo.getAccount());
                    entity.setOperatorName(userInfo.getUserName());
                    entity.setStatus(ConfigStatus.DRAFT.getCode());
                    return entity;
                })
                .collect(Collectors.toList());

        List<SitePageConfigDraftEntity> updateList = list.stream()
                .filter(record -> record.getId() != null)
                .map(record -> {
                    SitePageConfigDraftEntity entity = draftEngine.getById(record.getId());
                    Assert.notNull(entity, "配置不存在");
                    Assert.isTrue(ConfigStatus.getByCode(entity.getStatus()).isEditable(), "当前状态不允许编辑");
                    Assert.isTrue(entity.getType() == DraftType.RECORD_CONTENT.getCode(), "配置无效");

                    entity = TransformUtil.dtoToDraft(record, entity);

                    entity.setOperatorId(userInfo.getAccount());
                    entity.setOperatorName(userInfo.getUserName());
                    entity.setStatus(ConfigStatus.DRAFT.getCode());
                    entity.setCreateTime(null);
                    entity.setUpdateTime(null);
                    return entity;
                })
                .collect(Collectors.toList());

        draftEngine.saveBatch(insertList);
        draftEngine.updateBatchById(updateList);
        // 写入审计操作日志
        opsLogEngine.saveOpsLog(OperationType.SAVE_DRAFT, userInfo, insertList);
        opsLogEngine.saveOpsLog(OperationType.SAVE_DRAFT, userInfo, updateList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRecords(UserInfo userInfo, List<Long> ids) {
        List<SitePageConfigDraftEntity> list = draftEngine.listByIds(ids);
        // 只有record表也没有才允许删除
        Assert.isTrue(CollectionUtils.isEmpty(recordEngine.findByDraftIds(ids)), "当前草稿已关联在线记录，不能删除");
        Assert.isTrue(list.stream().allMatch(record -> ConfigStatus.getByCode(record.getStatus()).isDeletable()), "当前状态不允许删除");
        Assert.isTrue(list.stream().allMatch(record -> record.getReleaseId() == null || record.getReleaseId() == 0), "已关联线上数据，不能删除");
        draftEngine.removeBatchByIds(ids);
        // 写入审计操作日志
        opsLogEngine.saveOpsLog(OperationType.DELETE_DRAFT, userInfo, list);
    }

    public CommonPagedData<RecordDTO> moduleDraftList(String pageId, Integer moduleId, Integer status, Long page, Long size) {
        Page<SitePageConfigDraftEntity> pg = Page.of(page, size);
        pg = draftEngine.page(pg, new LambdaQueryWrapper<>(SitePageConfigDraftEntity.class)
                .eq(SitePageConfigDraftEntity::getPageId, pageId)
                .eq(SitePageConfigDraftEntity::getModuleId, moduleId)
                .eq(SitePageConfigDraftEntity::getType, DraftType.MODULE_CONTENT.getCode())
                .eq(status != null, SitePageConfigDraftEntity::getStatus, status)
                .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
        );

        CommonPagedData<RecordDTO> result = new CommonPagedData<>();
        result.setTotal(pg.getTotal());
        result.setPage(pg.getCurrent());
        result.setSize(pg.getSize());
        result.setRecords(pg.getRecords().stream().map(TransformUtil::draftToDTO).collect(Collectors.toList()));
        return result;
    }

    /**
     * 校验模块配置结构
     * 基于 template 进行验证，每个模板有自己的 Schema 定义
     *
     * @param pageId 页面ID
     * @param moduleId 模块ID
     * @param config 配置JSON字符串
     */
    private void validateConfig(String pageId, Integer moduleId, String config) {
        try {
            // 查询模块信息，获取模板类型
            SitePageModuleEntity moduleEntity = moduleEngine.getById(moduleId);
            if (moduleEntity == null) {
                log.warn("模块不存在，跳过配置校验: moduleId={}", moduleId);
                return;
            }

            String template = moduleEntity.getTemplate();
            if (StringUtils.isEmpty(template)) {
                log.warn("模块模板为空，跳过配置校验: moduleId={}, pageId={}", moduleId, pageId);
                return;
            }

            // 执行基于模板 Schema 的配置校验，获取详细错误信息
            ValidationResult validationResult = templateSchemaValidator.validateConfigWithDetails(template, config);
            if (!validationResult.isValid()) {
                String errorMsg = validationResult.getFormattedErrorMessage();
                if (errorMsg == null || errorMsg.trim().isEmpty()) {
                    errorMsg = "配置校验失败，请检查配置格式是否符合模板要求";
                }
                log.error("模块配置校验失败: pageId={}, moduleId={}, template={}, errors={}", 
                    pageId, moduleId, template, validationResult.getErrors());
                throw new IllegalArgumentException(errorMsg);
            }

            log.debug("模块配置校验通过: pageId={}, moduleId={}, template={}", pageId, moduleId, template);

        } catch (IllegalArgumentException e) {
            // 重新抛出校验失败异常
            throw e;
        } catch (Exception e) {
            log.error("配置校验过程中发生异常: pageId={}, moduleId={}", pageId, moduleId, e);
            throw new RuntimeException("配置校验异常: " + e.getMessage(), e);
        }
    }

}
