package com.xiaomi.micar.site.model.robot;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * 卡片模板消息
 *
 * <AUTHOR>
 * @since 2024/3/23
 */
@Data
public class TplMessage {
    private String type = "template";

    private TplData data;


    /**
     * struct
     */
    @Data
    public static class TplData {
        @JsonProperty("template_id")
        private String id;
        @JsonProperty("template_variable")
        private Map<String, String> variable;
    }

    public static TplMessage build(String templateId, Map<String, String> variable) {
        TplMessage message = new TplMessage();
        message.setType("template");
        TplData data = new TplData();
        data.setId(templateId);
        data.setVariable(variable);
        message.setData(data);
        return message;
    }
}
