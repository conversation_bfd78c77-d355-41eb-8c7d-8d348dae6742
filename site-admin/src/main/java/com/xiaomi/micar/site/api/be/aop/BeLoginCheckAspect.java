package com.xiaomi.micar.site.api.be.aop;

import com.xiaomi.micar.site.model.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;


/**
 * 用户检查切面
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Component
@Aspect
@Slf4j
public class BeLoginCheckAspect {

    @Around(value = "@annotation(loginCheck)")
    public Object doAround(final ProceedingJoinPoint joinPoint, BeLoginCheck loginCheck) throws Throwable {
        Object[] args = joinPoint.getArgs();

        // 获取用户账号信息
        String acct = RpcContext.getContext().getAttachment("$upc_account");

        // 如果需要登录验证
        if (loginCheck.required()) {
            Assert.isTrue(StringUtils.isNotBlank(acct), "用户未登录");
        }

        // 如果方法有参数，则尝试设置用户信息
        if (args.length > 0) {
            Object argument = args[0];

            Field field = ReflectionUtils.findField(argument.getClass(), loginCheck.field());
            if (field == null) {
                throw new RuntimeException("[BeLoginCheck]unknow field : " + loginCheck.field());
            }

            // 如果用户已登录，设置用户信息
            if (StringUtils.isNotBlank(acct)) {
                String name = RpcContext.getContext().getAttachment("$upc_userName");
                String mid = RpcContext.getContext().getAttachment("$upc_miID");

                UserInfo userInfo = new UserInfo();
                userInfo.setAccount(acct);
                userInfo.setUserName(name);
                userInfo.setMid(mid);

                field.setAccessible(true);
                field.set(argument, userInfo);
            }
        }

        return joinPoint.proceed();
    }
}
