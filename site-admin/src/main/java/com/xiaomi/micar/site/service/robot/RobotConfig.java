package com.xiaomi.micar.site.service.robot;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;


/**
 * 机器人配置
 *
 * <AUTHOR>
 * @since 2025/01/26
 */
@ConfigurationProperties(prefix = "site.robot")
@Data
public class RobotConfig {

    private String baseUrl;
    private String appId;
    private String appSecret;
    private String chatId;

    /**
     * 事件模板
     * key为事件名称，value为模板ID
     */
    private Map<String, String> templateMap;
}
