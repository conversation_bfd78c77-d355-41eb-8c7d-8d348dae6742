package com.xiaomi.micar.site.service.robot;


import com.google.common.collect.Maps;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.lark.oapi.service.im.v1.model.ListChatReq;
import com.lark.oapi.service.im.v1.model.ListChatResp;
import com.lark.oapi.service.im.v1.model.CreateImageReq;
import com.lark.oapi.service.im.v1.model.CreateImageReqBody;
import com.lark.oapi.service.im.v1.model.CreateImageResp;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.model.robot.TplMessage;
import com.xiaomi.micar.site.service.PreviewUrlService;
import com.xiaomi.micar.site.service.QRCodeGenerator;

import static com.xiaomi.micar.site.event.ApprovalCompletedEvent.*;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;



/**
 * 机器人通知业务
 *
 * <AUTHOR>
 * @since 2025/01/25
 */
@Component
@Slf4j
public class RobotNotifyService {

    @Resource
    private RobotConfig robotConfig;

    @Resource
    private PreviewUrlService previewUrlService;

    private volatile Client client;

    private void initRobotClient() {
        if (client == null) {
            synchronized (this) {
                if (client == null) {
                    this.client = Client.newBuilder(robotConfig.getAppId(), robotConfig.getAppSecret())
                            .openBaseUrl(robotConfig.getBaseUrl())
                            .build();
                }
            }
        }
    }

    public String getChats() throws Exception {
        initRobotClient();
         ListChatReq req = ListChatReq.newBuilder()
                 .pageSize(20)
                 .build();

         ListChatResp resp = client.im().v1().chat().list(req);

         if (!resp.success()) {
             log.info("获取群聊列表失败: code={}, msg={}, reqId={}, resp={}",
                     resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
             return null;
         }

         return Jsons.DEFAULT.toJson(resp.getData());
    }

    /**
     * 发送审批结果通知
     *
     * @param draft 草稿实体
     * @param operator 操作人
     * @param resultType 执行结果类型
     * @param resultMessage 结果描述
     * @param comment 审批意见
     * @param pageName 页面名称
     * @param moduleName 模块名称
     * @param operationType 操作类型
     * @return 是否发送成功
     */
    public boolean sendApprovalResultNotify(SitePageConfigDraftEntity draft, String operator,
                                          Object resultType, String resultMessage, String comment,
                                          String pageName, String moduleName, String operationType) {
        try {
            initRobotClient();

            String templateId = robotConfig.getTemplateMap().get("ApprovalResult");
            if (templateId == null) {
                log.warn("robot ignore send for no template found. event: ApprovalResult");
                return false;
            }

            // 构建模板变量 - 审批结果通知
            Map<String, String> variable = Maps.newHashMap();
            variable.put(FIELD_OPERATOR, operator); // 操作人员
            variable.put("pageName", pageName != null ? pageName : "未知页面"); // 页面名称
            variable.put("moduleName", moduleName != null ? moduleName : "未知模块"); // 模块名称
            variable.put("operationType", operationType != null ? operationType : "未知操作"); // 操作类型
            variable.put(FIELD_RESULT_TYPE, resultType != null ? resultType.toString() : "未知"); // 审批结果
            variable.put(FIELD_COMMENT, comment != null ? comment : ""); // 审批备注
            variable.put("operatorTime", java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-M-d H:mm:ss"))); // 操作时间

            // 生成预览二维码并上传到飞书
            String imageKey = generatePreviewQrCode(draft);
            variable.put("image_key", imageKey); // 飞书图片key

            TplMessage message = TplMessage.build(templateId, variable);

            // 创建请求对象
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType("chat_id")
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .receiveId(robotConfig.getChatId())
                            .msgType("interactive")
                            .content(JsonUtil.toJSONString(message))
                            .uuid(draft.getId() + "_" + System.currentTimeMillis())
                            .build())
                    .build();

            CreateMessageResp resp = client.im().v1().message().create(req);
            log.info("robot approval result notify send success. request={}, response={}", req, resp);

            if (resp == null || !resp.success()) {
                log.error("robot approval result notify send failed. request={}, response={}",
                         JsonUtil.toJSONString(req), JsonUtil.toJSONString(resp));
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("robot approval result notify send exception. draftId: {}, resultType: {}, pageName: {}, moduleName: {}, operationType: {}",
                     draft.getId(), resultType, pageName, moduleName, operationType, e);
            return false;
        }
    }

    /**
     * 生成预览二维码并上传到飞书
     *
     * @param draft 草稿实体
     * @return 飞书图片key
     */
    private String generatePreviewQrCode(SitePageConfigDraftEntity draft) {
        try {
            // 1. 生成预览URL
            String previewUrl = previewUrlService.generatePreviewUrl(draft);
            log.info("生成预览URL: draftId={}, previewUrl={}", draft.getId(), previewUrl);

            // 2. 生成二维码图片并上传到飞书
            String imageKey = generateAndUploadQrCode(previewUrl);
            log.info("二维码上传成功: draftId={}, imageKey={}", draft.getId(), imageKey);

            return imageKey;
        } catch (Exception e) {
            log.error("生成预览二维码失败: draftId={}", draft.getId(), e);
            return ""; // 返回空字符串，不影响其他功能
        }
    }

    /**
     * 生成二维码图片并上传到飞书
     *
     * @param content 二维码内容
     * @return 飞书图片key
     */
    public String generateAndUploadQrCode(String content) {
        File tempFile = null;
        try {
            initRobotClient();

            // 1. 生成二维码图片到临时文件
            tempFile = File.createTempFile("qrcode_", ".jpg");
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                QRCodeGenerator.generate(content, fos);
            }

            // 2. 上传到飞书
            CreateImageReq req = CreateImageReq.newBuilder()
                    .createImageReqBody(CreateImageReqBody.newBuilder()
                            .imageType("message")
                            .image(tempFile)
                            .build())
                    .build();

            CreateImageResp resp = client.im().v1().image().create(req);

            if (!resp.success()) {
                log.error("上传二维码到飞书失败: code={}, msg={}, reqId={}",
                        resp.getCode(), resp.getMsg(), resp.getRequestId());
                return "";
            }

            String imageKey = resp.getData().getImageKey();
            log.info("二维码上传飞书成功: imageKey={}", imageKey);
            return imageKey;

        } catch (Exception e) {
            log.error("生成并上传二维码失败: content={}", content, e);
            return "";
        } finally {
            // 3. 确保临时文件被删除
            if (tempFile != null) {
                tempFile.delete();
            }
        }
    }




}
