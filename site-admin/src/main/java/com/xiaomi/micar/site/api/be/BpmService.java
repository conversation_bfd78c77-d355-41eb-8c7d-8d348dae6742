package com.xiaomi.micar.site.api.be;

import com.xiaomi.micar.site.model.bpm.BpmCallbackDTO;
import com.xiaomi.micar.site.model.bpm.BpmCallbackResponse;
import com.xiaomi.micar.site.model.bpm.BpmSubmitRequest;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.Map;

/**
 * BPM服务接口
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
public interface BpmService {
    /**
     * 提交站点配置审批
     *
     * @param request 提交请求，包含草稿ID和操作类型
     * @return 提交结果
     */
    Result<Void> submit(BpmSubmitRequest request);

    /**
     * 处理站点配置审批回调
     *
     * @param callback 回调数据
     * @return 回调处理结果
     */
    BpmCallbackResponse handleCallback(BpmCallbackDTO callback);
}
