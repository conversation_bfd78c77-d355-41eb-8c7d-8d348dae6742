package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 新增模块配置请求
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ModuleAddReq extends BaseMgrReq {

    /**
     * 页面ID
     */
    @NotBlank(message = "页面ID不能为空")
    private String pageId;

    /**
     * 楼层名称
     */
    @NotBlank(message = "楼层名称不能为空")
    private String moduleName;

    /**
     * 模板ID
     */
    @NotBlank(message = "模板ID不能为空")
    private String template;

    /**
     * 数据提供者
     */
    private String dataProvider;

    /**
     * 数据提供者参数（JSON格式）
     */
    private String dataProviderParams;
}
