package com.xiaomi.micar.site.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查看模块列表响应
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModuleListResp {

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 草稿ID（仅当draft=true才返回）
     */
    private String draftId;

    /**
     * 模块列表
     */
    private List<ModuleItem> moduleList;

    /**
     * 模块项
     */
    @Data
    @NoArgsConstructor
    public static class ModuleItem {
        /**
         * 模块ID
         */
        private Long moduleId;

        /**
         * 模块名称
         */
        private String moduleName;

        /**
         * 模板ID
         */
        private String template;

        public ModuleItem(Long moduleId, String moduleName, String template) {
            this.moduleId = moduleId;
            this.moduleName = moduleName;
            this.template = template;
        }
    }
}
