package com.xiaomi.micar.site.api.be;

import com.xiaomi.micar.site.api.be.aop.BeLoginCheck;
import com.xiaomi.micar.site.api.model.WhiteUserDeleteReq;
import com.xiaomi.micar.site.api.model.WhiteUserListReq;
import com.xiaomi.micar.site.api.model.WhiteUserSaveReq;
import com.xiaomi.micar.site.dao.SiteWhiteUserEngine;
import com.xiaomi.micar.site.dao.entity.SiteWhiteUserEntity;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 后台：白名单用户
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Slf4j
@ApiModule(value = "后台管理#白名单用户管理接口", apiInterface = BeRecordMgr.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class BeWhiteUserMgrImpl implements BeWhiteUserMgr {

    @Resource
    private SiteWhiteUserEngine whiteUserEngine;


    @ApiDoc(value = "获取白名单用户列表")
    @BeLoginCheck
    @Override
    public Result<CommonPagedData<SiteWhiteUserEntity>> list(WhiteUserListReq req) {
        CommonPagedData<SiteWhiteUserEntity> data = whiteUserEngine.pagedData(req.getPage(), req.getSize());
        return Result.success(data);
    }

    @Override
    public Result<Void> save(WhiteUserSaveReq req) {
        whiteUserEngine.saveOrUpdateUser(req);
        return Result.success(null);
    }

    @Override
    public Result<Void> delete(WhiteUserDeleteReq req) {
        whiteUserEngine.removeById(req.getId());
        return Result.success(null);
    }
}
