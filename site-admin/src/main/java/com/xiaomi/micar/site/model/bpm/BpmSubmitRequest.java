package com.xiaomi.micar.site.model.bpm;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * BPM提交审批请求对象
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BpmSubmitRequest extends BaseMgrReq {
    /**
     * 草稿ID
     */
    private Long draftId;

    /**
     * 操作类型
     * 1: 修改审核
     * 2: 下线审核
     */
    private Integer operationType;

    /**
     * 备注信息
     */
    private String remark;
}
