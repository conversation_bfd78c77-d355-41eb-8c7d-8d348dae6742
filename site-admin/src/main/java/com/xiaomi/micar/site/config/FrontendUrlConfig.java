package com.xiaomi.micar.site.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 前端 C 端地址配置
 * 用于生成页面预览链接
 *
 * <AUTHOR>
 * @since 2025/06/29
 */
@Data
@Component
@ConfigurationProperties(prefix = "frontend.url")
public class FrontendUrlConfig {

    /**
     * C 端页面基础地址
     * 例如：http://test-api.retail.xiaomiev.com/mtop/carsite/page/v2
     *
     *
     * https://www.xiaomiev.com/shop/home/<USER>
     */
    private String pageBaseUrl = "https://www.xiaomiev.com/shop/home/<USER>";

    /**
     * 生成页面 C 端访问链接
     *
     * @param topicId 主题ID
     * @return 完整的 C 端访问链接
     */
    public String generatePageUrl(String topicId) {
        if (topicId == null || topicId.trim().isEmpty()) {
            return null;
        }
        return pageBaseUrl.replace("{topicId}", topicId);
    }
}
