package com.xiaomi.micar.site.enums;

import lombok.Getter;

/**
 * 用户操作类型
 *
 * <AUTHOR>
 * @since 2025/05/27
 */
@Getter
public enum OperationType {

    SAVE_DRAFT("save_draft", "保存草稿"),
    DELETE_DRAFT("delete_draft", "删除草稿"),
    ONLINE_SUBMIT("online_submit", "上线审核"),
    ONLINE_REJECT("online_reject", "上线审核驳回"),
    ONLINE_AUDIT("online_audit", "上线审核通过"),
    ONLINE_DEPLOY("online_deploy", "上线部署完成"),
    OFFLINE_SUBMIT("offline_submit", "下线审核"),
    OFFLINE_REJECT("offline_reject", "下线审核驳回"),
    OFFLINE_AUDIT("offline_audit", "下线审核通过");


    private final String code;
    private final String desc;

    OperationType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static OperationType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
