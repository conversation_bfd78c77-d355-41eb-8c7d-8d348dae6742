package com.xiaomi.micar.site.api.be;


import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.api.model.DraftDeleteReq;
import com.xiaomi.micar.site.api.model.DraftListReq;
import com.xiaomi.micar.site.api.model.DraftBatchSaveReq;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 后端接口：页面模块管理
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
public interface BeRecordMgr {

    Result<CommonPagedData<RecordDTO>> recordList(DraftListReq req);


    Result<CommonPagedData<RecordDTO>> draftList(DraftListReq req);

    Result<Void> saveBatch(DraftBatchSaveReq req);

    Result<Void> deleteBatch(DraftDeleteReq req);
}
