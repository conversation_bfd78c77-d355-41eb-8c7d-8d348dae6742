package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 白名单列表req
 *
 * <AUTHOR>
 * @since 2025/04/12
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WhiteUserListReq extends BaseMgrReq implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long page = 1L;
    private Long size = 20L;
}