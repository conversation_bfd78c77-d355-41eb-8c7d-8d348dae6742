package com.xiaomi.micar.site.config;

import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.NetUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RegisterConfig {

    @Value("${nacos.config.addr}")
    private String nacosAddr;

    @Value("${server.port}")
    private int port;

    @Value("${service.name}")
    private String serviceName;


    Instance getInstance() {
        Instance instance = new Instance();
        instance.setPort(port);
        String ip = Optional.ofNullable(System.getenv("host.ip")).orElse(NetUtils.getLocalHost());
        log.info("host ip is {},serviceName={}", ip, serviceName);
        // String ip = InetAddress.getLocalHost().getHostAddress();
        instance.setIp(ip);
        instance.setServiceName(serviceName);
        return instance;
    }

    @Bean
    NamingService getNamingService() throws NacosException {
        NamingService namingService = NamingFactory.createNamingService(nacosAddr);
        log.info("namedd={}", namingService);
        Instance instance = getInstance();
        namingService.registerInstance(serviceName, instance);
        return namingService;
    }
}
