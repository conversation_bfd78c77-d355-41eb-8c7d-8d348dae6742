package com.xiaomi.micar.site.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.oa.infra.oaucf.bpm.rep.CreateProcInstResp;
import com.mi.oa.infra.oaucf.bpm.req.CreateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.xiaomi.micar.site.config.BpmFormConfig;
import com.xiaomi.micar.site.dao.SiteOpsLogEngine;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.enums.BpmOperationType;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.enums.OperationType;
import com.xiaomi.micar.site.filter.ResultUtils;
import com.xiaomi.micar.site.model.UserInfo;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * BPM内部服务 - 处理BPM提交的核心逻辑
 * 避免Dubbo服务之间的互相调用
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class BpmInternalService {

    @Resource
    private ProcessInstanceService processInstanceService;

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Resource
    private BpmFormConfig bpmFormConfig;

    @Resource
    private SiteOpsLogEngine opsLogEngine;

    /**
     * 提交BPM审核的核心逻辑
     *
     * @param draftId       草稿ID
     * @param operationType 操作类型
     * @param remark        备注
     * @param userInfo      用户信息
     * @return 提交结果
     */
    public Result<Void> submitBpmApproval(Long draftId, Integer operationType, String remark, UserInfo userInfo) {
        log.info("开始处理BPM提交: draftId={}, operationType={}, remark={}, user={}",
                draftId, operationType, remark, userInfo.getAccount());

        // 参数校验
        if (draftId == null) {
            return Result.fail(GeneralCodes.ParamError, "草稿ID不能为空");
        }

        if (operationType == null) {
            return Result.fail(GeneralCodes.ParamError, "操作类型不能为空");
        }

        BpmOperationType bpmOperationType = BpmOperationType.getByCode(operationType);
        if (bpmOperationType == null) {
            return Result.fail(GeneralCodes.ParamError, "无效的操作类型: " + operationType);
        }

        // 根据draftId获取草稿信息
        SitePageConfigDraftEntity draft = draftEngine.getById(draftId);
        if (draft == null) {
            return Result.fail(GeneralCodes.ParamError, "草稿不存在");
        }

        // 根据操作类型检查草稿状态
        if (BpmOperationType.OFFLINE.equals(bpmOperationType)) {
            // 下线审核：草稿必须是已部署状态
            if (draft.getStatus() != ConfigStatus.DEPLOYED.getCode()) {
                return Result.fail(GeneralCodes.ParamError, "草稿状态不是上线状态，当前状态: " + draft.getStatus());
            }

            // 检查是否为当前生效的版本
            Result<Void> versionCheckResult = checkCurrentActiveVersion(draft);
            if (!ResultUtils.isSuccess(versionCheckResult)) {
                return versionCheckResult;
            }
        } else {
            // 修改审核：草稿必须是草稿状态
            if (draft.getStatus() != ConfigStatus.DRAFT.getCode()) {
                return Result.fail(GeneralCodes.ParamError, "草稿状态不是草稿状态，当前状态: " + draft.getStatus());
            }
        }

        // 页面级别审核类型的特殊校验：同一时间只能有一个页面级别的审核
        if (DraftType.isPageLevel(draft.getType())) {
            // 将 int[] 转换为 List<Integer> 以确保 MyBatis-Plus .in() 方法的兼容性
            List<Integer> pageLevelCodes = Arrays.stream(DraftType.getPageLevelCodes())
                    .boxed()
                    .collect(Collectors.toList());

            long count = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getPageId, draft.getPageId())
                    .in(SitePageConfigDraftEntity::getType, pageLevelCodes)
                    .in(SitePageConfigDraftEntity::getStatus, ConfigStatus.AUDITING.getCode(), ConfigStatus.OFFLINE_AUDIT.getCode())
                    .ne(SitePageConfigDraftEntity::getId, draft.getId()) // 排除当前草稿
                    .count();

            if (count > 0) {
                log.warn("同一个页面已有处于审核中的页面级别审核草稿, pageId={}, draftId={}, operationType={}",
                        draft.getPageId(), draft.getId(), bpmOperationType.getCode());
                return Result.fail(GeneralCodes.ParamError, "同一个页面已有处于审核中的页面级别审核，请等待当前审核完成后再提交");
            }
        } else {
            // 组件类型的校验：同一个页面和模块ID只能有一个处于审核中
            long count = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getPageId, draft.getPageId())
                    .eq(SitePageConfigDraftEntity::getModuleId, draft.getModuleId())
                    .in(SitePageConfigDraftEntity::getStatus, ConfigStatus.AUDITING.getCode(), ConfigStatus.OFFLINE_AUDIT.getCode())
                    .ne(SitePageConfigDraftEntity::getId, draft.getId()) // 排除当前草稿
                    .count();

            if (count > 0) {
                log.warn("同一个页面和模块已有处于审核中的草稿, pageId={}, moduleId={}, draftId={}, operationType={}",
                        draft.getPageId(), draft.getModuleId(), draft.getId(), bpmOperationType.getCode());
                return Result.fail(GeneralCodes.ParamError, "同一个页面和模块已有处于审核中的草稿，请等待当前审核完成后再提交");
            }
        }

        try {
            // 构建表单数据
            Map<String, Object> formData = buildFormData(draft, bpmOperationType, remark);

            // 生成业务键
            String groupKey = generateGroupKey();
            draft.setGroupKey(groupKey);
            draftEngine.updateById(draft);

            // 创建流程实例
            String processName = bpmFormConfig.getProcessName() + "-" + bpmOperationType.getDesc();
            CreateProcInstReq createProcInstReq = CreateProcInstReq.builder()
                    .modelCode(bpmFormConfig.getModelCode())
                    .processInstanceName(processName)
                    .businessKey(groupKey)
                    .startUserId(userInfo.getAccount())
                    .formData(formData)
                    .build();

            // 调用BPM服务
            BaseResp<CreateProcInstResp> resp = processInstanceService.create(createProcInstReq);
            log.info("提交BPM审批结果: {}", resp);

            if (resp == null || resp.getCode() != 0) {
                if (resp != null && resp.getCode() == 3000009) {
                    log.warn("流程实例已存在, draftId={}", draftId);
                    return Result.success(null);
                }
                return Result.fail(GeneralCodes.InternalError, "提交审批失败: " + (resp != null ? resp.getMessage() : "未知错误"));
            }

            // 更新草稿状态
            boolean updated = updateDraftStatus(draft, bpmOperationType, userInfo);
            if (!updated) {
                return Result.fail(GeneralCodes.InternalError, "BPM提交成功，但更新草稿状态失败，可能是并发修改导致");
            }

            // 记录审计日志
            OperationType opsType = BpmOperationType.OFFLINE.equals(bpmOperationType) ?
                    OperationType.OFFLINE_SUBMIT : OperationType.ONLINE_SUBMIT;
            opsLogEngine.saveOpsLog(opsType, userInfo, Lists.newArrayList(draft));

            log.info("BPM提交处理成功, draftId={}, pageId={}, 新状态={}, 操作类型={}, 操作人={}/{}",
                    draftId, draft.getPageId(), draft.getStatus(), bpmOperationType.getDesc(),
                    userInfo.getAccount(), userInfo.getUserName());
            return Result.success(null);

        } catch (Exception e) {
            log.error("BPM提交处理异常: draftId={}, error={}", draftId, e.getMessage(), e);
            return Result.fail(GeneralCodes.InternalError, "提交审核失败: " + e.getMessage());
        }
    }

    /**
     * 构建表单数据
     */
    private Map<String, Object> buildFormData(SitePageConfigDraftEntity draft, BpmOperationType operationType, String remark) {
        Map<String, Object> formData = new LinkedHashMap<>();

        // 页面名称
        formData.put(bpmFormConfig.getPageNameField(), draft.getPageId());

        // 模块配置
        formData.put(bpmFormConfig.getModuleConfigField(), draft.getModuleId() + "#" + draft.getName());

        // 操作类型
        String operationTypeDesc = operationType.getDesc();

        // 备注信息
        String finalRemark = StringUtils.isNotEmpty(remark) ? remark :
                (StringUtils.isNotEmpty(draft.getRemark()) ? draft.getRemark() : "无");
        formData.put(bpmFormConfig.getRemarkField(), operationTypeDesc + ": " + finalRemark);

        // 扫码预览
        ;
        String qrUrl = bpmFormConfig.getQrUrlPrefix() + "/basic/page/preview/qrcode?ct=" + DraftType.getByCode(draft.getType()).getContentType() + "&cv=" + draft.getId();
        Map<String, Object> qrCodeMap = Maps.newHashMap();
        qrCodeMap.put("fileName", "预览二维码.png");
        qrCodeMap.put("uri", qrUrl);
        qrCodeMap.put("originFileName", "预览二维码.png");
        formData.put(bpmFormConfig.getQrCodeField(), Lists.newArrayList(qrCodeMap));

        return formData;
    }

    /**
     * 生成业务键
     */
    private String generateGroupKey() {
        long timestamp = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        return timestamp + "_" + uuid;
    }

    /**
     * 更新草稿状态
     */
    private boolean updateDraftStatus(SitePageConfigDraftEntity draft, BpmOperationType operationType, UserInfo userInfo) {
        Integer expectedStatus = draft.getStatus();
        Integer newStatus;

        if (BpmOperationType.OFFLINE.equals(operationType)) {
            newStatus = ConfigStatus.OFFLINE_AUDIT.getCode();
        } else {
            newStatus = ConfigStatus.AUDITING.getCode();
        }

        // 使用CAS方式更新
        boolean updated = draftEngine.lambdaUpdate()
                .eq(SitePageConfigDraftEntity::getId, draft.getId())
                .eq(SitePageConfigDraftEntity::getStatus, expectedStatus)
                .set(SitePageConfigDraftEntity::getStatus, newStatus)
                .set(SitePageConfigDraftEntity::getOperatorId, userInfo.getAccount())
                .set(SitePageConfigDraftEntity::getOperatorName, userInfo.getUserName())
                .update();

        if (updated) {
            // 更新内存中的对象状态
            draft.setStatus(newStatus);
            draft.setOperatorId(userInfo.getAccount());
            draft.setOperatorName(userInfo.getUserName());
        }

        return updated;
    }

    /**
     * 检查是否为当前生效的版本
     * 当下线时，必须确保下线的是当前生效的最新版本
     *
     * @param draft 要下线的草稿
     * @return 检查结果
     */
    private Result<Void> checkCurrentActiveVersion(SitePageConfigDraftEntity draft) {
        log.info("检查当前生效版本: draftId={}, pageId={}, moduleId={}, type={}",
                draft.getId(), draft.getPageId(), draft.getModuleId(), draft.getType());

        try {
            // 查找同一页面和模块的最新上线版本
            SitePageConfigDraftEntity latestDeployedDraft;

            if (DraftType.isPageLevel(draft.getType())) {
                // 页面级别类型：查找同一页面的最新上线版本
                // 将 int[] 转换为 List<Integer> 以确保 MyBatis-Plus .in() 方法的兼容性
                List<Integer> pageLevelCodes = Arrays.stream(DraftType.getPageLevelCodes())
                        .boxed()
                        .collect(Collectors.toList());

                latestDeployedDraft = draftEngine.lambdaQuery()
                        .eq(SitePageConfigDraftEntity::getPageId, draft.getPageId())
                        .in(SitePageConfigDraftEntity::getType, pageLevelCodes)
                        .eq(SitePageConfigDraftEntity::getStatus, ConfigStatus.DEPLOYED.getCode())
                        .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
                        .last("LIMIT 1")
                        .one();
            } else {
                // 模块类型：查找同一页面和模块的最新上线版本
                // 将模块级别的类型码转换为 List<Integer> 以确保 MyBatis-Plus .in() 方法的兼容性
                List<Integer> moduleLevelCodes = Arrays.stream(DraftType.getModuleLevelCodes())
                        .boxed()
                        .collect(Collectors.toList());

                latestDeployedDraft = draftEngine.lambdaQuery()
                        .eq(SitePageConfigDraftEntity::getPageId, draft.getPageId())
                        .eq(SitePageConfigDraftEntity::getModuleId, draft.getModuleId())
                        .in(SitePageConfigDraftEntity::getType, moduleLevelCodes)
                        .eq(SitePageConfigDraftEntity::getStatus, ConfigStatus.DEPLOYED.getCode())
                        .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
                        .last("LIMIT 1")
                        .one();
            }

            if (latestDeployedDraft == null) {
                log.warn("未找到当前生效的版本: pageId={}, moduleId={}, type={}",
                        draft.getPageId(), draft.getModuleId(), draft.getType());
                return Result.fail(GeneralCodes.ParamError, "未找到当前生效的版本");
            }

            // 检查要下线的是否为最新版本
            if (!draft.getId().equals(latestDeployedDraft.getId())) {
                String errorMsg;
                if (draft.getType() == 1) {
                    errorMsg = String.format("当前要下线的不是最新生效版本，请先下线版本ID: %d (更新时间: %s)",
                            latestDeployedDraft.getId(), latestDeployedDraft.getUpdateTime());
                } else {
                    errorMsg = String.format("当前要下线的不是最新生效版本，请先下线版本ID: %d (页面: %s, 模块: %d, 更新时间: %s)",
                            latestDeployedDraft.getId(), latestDeployedDraft.getPageId(),
                            latestDeployedDraft.getModuleId(), latestDeployedDraft.getUpdateTime());
                }

                log.warn("版本检查失败: 当前草稿ID={}, 最新版本ID={}, pageId={}, moduleId={}",
                        draft.getId(), latestDeployedDraft.getId(), draft.getPageId(), draft.getModuleId());
                return Result.fail(GeneralCodes.ParamError, errorMsg);
            }

            log.info("版本检查通过: 当前草稿是最新生效版本, draftId={}, pageId={}, moduleId={}",
                    draft.getId(), draft.getPageId(), draft.getModuleId());
            return Result.success(null);

        } catch (Exception e) {
            log.error("检查当前生效版本异常: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            return Result.fail(GeneralCodes.InternalError, "检查当前生效版本失败: " + e.getMessage());
        }
    }
}
