package com.xiaomi.micar.site.service.callback;

import com.xiaomi.micar.site.enums.AuditType;
import com.xiaomi.micar.site.enums.ConfigStatus;
import lombok.Getter;

/**
 * 草稿状态转移枚举
 * 清晰定义所有可能的状态转移路径
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Getter
public enum DraftStatusTransition {
    
    // 审批驳回转移
    AUDITING_TO_DRAFT("上线审核驳回",
            ConfigStatus.AUDITING, ConfigStatus.DRAFT, "上线审核被驳回，回到草稿状态"),

    OFFLINE_AUDIT_TO_DEPLOYED("下线审核驳回",
            ConfigStatus.OFFLINE_AUDIT, ConfigStatus.DEPLOYED, "下线审核被驳回，保持已部署状态"),

    // 审批通过转移
    AUDITING_TO_DEPLOYING("上线审核通过",
            ConfigStatus.AUDITING, ConfigStatus.DEPLOYING, "上线审核通过，开始部署"),

    OFFLINE_AUDIT_TO_DEPLOYING("下线审核通过",
            ConfigStatus.OFFLINE_AUDIT, ConfigStatus.DEPLOYING, "下线审核通过，开始下线部署"),

    // 部署完成转移
    DEPLOYING_TO_DEPLOYED("部署完成",
            ConfigStatus.DEPLOYING, ConfigStatus.DEPLOYED, "部署完成，变为已部署状态"),

    DEPLOYING_TO_DRAFT("下线完成",
            ConfigStatus.DEPLOYING, ConfigStatus.DRAFT, "下线部署完成，变为草稿状态"),

    // 提交审核转移
    DRAFT_TO_AUDITING("提交上线审核",
            ConfigStatus.DRAFT, ConfigStatus.AUDITING, "草稿提交上线审核"),

    DEPLOYED_TO_OFFLINE_AUDIT("提交下线审核",
            ConfigStatus.DEPLOYED, ConfigStatus.OFFLINE_AUDIT, "已部署配置提交下线审核"),

    // 直接转移（优化后的审批通过转移，跳过中间状态）
    AUDITING_TO_DEPLOYED_DIRECT("上线审核通过直接部署",
            ConfigStatus.AUDITING, ConfigStatus.DEPLOYED, "上线审核通过，直接变为已部署状态"),

    OFFLINE_AUDIT_TO_DRAFT_DIRECT("下线审核通过直接完成",
            ConfigStatus.OFFLINE_AUDIT, ConfigStatus.DRAFT, "下线审核通过，直接变为草稿状态");

    private final String name;
    private final ConfigStatus fromStatus;
    private final ConfigStatus toStatus;
    private final String description;

    DraftStatusTransition(String name, ConfigStatus fromStatus, ConfigStatus toStatus, String description) {
        this.name = name;
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.description = description;
    }

    /**
     * 根据当前状态和目标状态查找转移
     */
    public static DraftStatusTransition findTransition(ConfigStatus from, ConfigStatus to) {
        for (DraftStatusTransition transition : values()) {
            if (transition.fromStatus == from && transition.toStatus == to) {
                return transition;
            }
        }
        return null;
    }

    /**
     * 根据当前状态查找审批驳回后的转移
     */
    public static DraftStatusTransition findRejectionTransition(ConfigStatus currentStatus) {
        if (currentStatus == ConfigStatus.AUDITING) {
            return AUDITING_TO_DRAFT;
        } else if (currentStatus == ConfigStatus.OFFLINE_AUDIT) {
            return OFFLINE_AUDIT_TO_DEPLOYED;
        }
        throw new IllegalArgumentException("当前状态不支持驳回操作: " + currentStatus.getDesc());
    }

    /**
     * 根据当前状态查找审批通过后的转移
     */
    public static DraftStatusTransition findApprovalTransition(ConfigStatus currentStatus) {
        if (currentStatus == ConfigStatus.AUDITING) {
            return AUDITING_TO_DEPLOYING;
        } else if (currentStatus == ConfigStatus.OFFLINE_AUDIT) {
            return OFFLINE_AUDIT_TO_DEPLOYING;
        }
        throw new IllegalArgumentException("当前状态不支持审批通过操作: " + currentStatus.getDesc());
    }

    /**
     * 根据部署类型查找部署完成后的转移
     */
    public static DraftStatusTransition findDeploymentCompleteTransition(boolean isOfflineAudit) {
        return isOfflineAudit ? DEPLOYING_TO_DRAFT : DEPLOYING_TO_DEPLOYED;
    }

    /**
     * 根据审核类型查找部署完成后的转移
     * 使用枚举替代布尔值，提高代码可读性
     */
    public static DraftStatusTransition findDeploymentCompleteTransition(AuditType auditType) {
        return auditType.isOffline() ? DEPLOYING_TO_DRAFT : DEPLOYING_TO_DEPLOYED;
    }

    /**
     * 根据当前状态查找审批通过后的直接转移（跳过中间状态）
     * 优化版本：直接从审核状态转移到最终状态
     */
    public static DraftStatusTransition findDirectApprovalTransition(ConfigStatus currentStatus) {
        if (currentStatus == ConfigStatus.AUDITING) {
            return AUDITING_TO_DEPLOYED_DIRECT;
        } else if (currentStatus == ConfigStatus.OFFLINE_AUDIT) {
            return OFFLINE_AUDIT_TO_DRAFT_DIRECT;
        }
        throw new IllegalArgumentException("当前状态不支持直接审批通过操作: " + currentStatus.getDesc());
    }

}
