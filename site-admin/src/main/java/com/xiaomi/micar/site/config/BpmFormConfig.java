package com.xiaomi.micar.site.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * BPM表单配置
 *
 * <AUTHOR>
 * @since 2023/04/15
 */
@Configuration
@ConfigurationProperties(prefix = "bpm.form")
@Data
public class BpmFormConfig {

    /**
     * 页面名称字段ID
     */
    private String pageNameField;

    /**
     * 模块配置字段ID
     */
    private String moduleConfigField;

    /**
     * 备注字段ID
     */
    private String remarkField;

    /**
     * 查看详情
     */
    private String checkDetailField;

    /**
     * 预览二维码字段ID
     */
    private String qrCodeField;

    /**
     * 流程模型编码
     */
    private String modelCode;

    /**
     * 流程实例名称
     */
    private String processName;

    /**
     * 详情页面域名
     */
    private String detailPageDomain;

    /**
     * 预览二维码前缀
     */
    private String qrUrlPrefix;
}
