package com.xiaomi.micar.site.controller;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.model.bpm.BpmCallbackDTO;
import com.xiaomi.micar.site.model.bpm.BpmCallbackResponse;
import com.xiaomi.micar.site.service.BpmCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * BPM回调控制器
 *
 * <AUTHOR>
 * @since 2023/07/15
 */
@Slf4j
@RestController
@RequestMapping("/bpm")
public class BpmController {

    @Resource
    private BpmCallbackService bpmCallbackService;

    /**
     * 站点配置审批回调接口
     *
     * @param callback 回调数据
     * @return 回调响应
     */
    @PostMapping("/callback")
    public BpmCallbackResponse siteConfigCallback(@RequestBody String callback) {

        log.info("收到站点配置审批回调请求: {}", callback);
        // 处理回调请求
        return bpmCallbackService.handleSiteConfigCallback(JsonUtil.parseObject(callback, BpmCallbackDTO.class));
    }
}
