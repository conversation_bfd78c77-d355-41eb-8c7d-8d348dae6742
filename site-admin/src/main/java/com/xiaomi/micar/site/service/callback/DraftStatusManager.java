package com.xiaomi.micar.site.service.callback;

import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 草稿状态管理器 (状态机版本)
 * 使用状态转移枚举，清晰定义所有可能的状态转移路径
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class DraftStatusManager {

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    /**
     * 执行状态转移（CAS方式）
     *
     * @param draftId 草稿ID
     * @param transition 状态转移
     * @return 是否转移成功
     */
    public boolean executeTransition(Long draftId, DraftStatusTransition transition) {
        log.info("执行状态转移: draftId={}, {}: {} -> {}",
                draftId, transition.getName(),
                transition.getFromStatus().getDesc(), transition.getToStatus().getDesc());

        boolean updated = draftEngine.lambdaUpdate()
                .eq(SitePageConfigDraftEntity::getId, draftId)
                .eq(SitePageConfigDraftEntity::getStatus, transition.getFromStatus().getCode())
                .set(SitePageConfigDraftEntity::getStatus, transition.getToStatus().getCode())
                .update();

        if (updated) {
            log.info("状态转移成功: draftId={}, {}", draftId, transition.getName());
        } else {
            log.error("状态转移失败: draftId={}, {} (可能是并发修改或状态不匹配)",
                    draftId, transition.getName());
        }

        return updated;
    }

    /**
     * 执行审批驳回转移
     */
    public boolean executeRejectionTransition(Long draftId, Integer currentStatus) {
        ConfigStatus status = ConfigStatus.getByCode(currentStatus);
        DraftStatusTransition transition = DraftStatusTransition.findRejectionTransition(status);
        return executeTransition(draftId, transition);
    }

    /**
     * 执行部署完成转移
     */
    public boolean executeDeploymentCompleteTransition(Long draftId, boolean isOfflineAudit) {
        DraftStatusTransition transition = DraftStatusTransition.findDeploymentCompleteTransition(isOfflineAudit);
        return executeTransition(draftId, transition);
    }

    /**
     * 执行状态转移并更新关联ID
     *
     * @param draftId 草稿ID
     * @param transition 状态转移
     * @param releaseId 关联ID
     * @return 是否转移成功
     */
    public boolean executeTransitionWithReleaseId(Long draftId, DraftStatusTransition transition, Long releaseId) {
        log.info("执行状态转移并更新关联ID: draftId={}, {}: {} -> {}, releaseId={}",
                draftId, transition.getName(),
                transition.getFromStatus().getDesc(), transition.getToStatus().getDesc(), releaseId);

        boolean updated = draftEngine.lambdaUpdate()
                .eq(SitePageConfigDraftEntity::getId, draftId)
                .eq(SitePageConfigDraftEntity::getStatus, transition.getFromStatus().getCode())
                .set(SitePageConfigDraftEntity::getStatus, transition.getToStatus().getCode())
                .set(SitePageConfigDraftEntity::getReleaseId, releaseId)
                .update();

        if (updated) {
            log.info("状态转移和关联ID更新成功: draftId={}, {}, releaseId={}",
                    draftId, transition.getName(), releaseId);
        } else {
            log.error("状态转移和关联ID更新失败: draftId={}, {} (可能是并发修改或状态不匹配)",
                    draftId, transition.getName());
        }

        return updated;
    }

    /**
     * 执行部署完成转移并更新关联ID
     */
    public boolean executeDeploymentCompleteTransitionWithReleaseId(Long draftId, boolean isOfflineAudit, Long releaseId) {
        DraftStatusTransition transition = DraftStatusTransition.findDeploymentCompleteTransition(isOfflineAudit);
        return executeTransitionWithReleaseId(draftId, transition, releaseId);
    }

    /**
     * 执行审批通过的直接转移并更新关联ID（优化版本：跳过中间状态）
     * 直接从审核状态转移到最终状态，避免不必要的中间状态
     */
    public boolean executeDirectApprovalTransitionWithReleaseId(Long draftId, Integer currentStatus, Long releaseId) {
        ConfigStatus status = ConfigStatus.getByCode(currentStatus);
        DraftStatusTransition transition = DraftStatusTransition.findDirectApprovalTransition(status);
        return executeTransitionWithReleaseId(draftId, transition, releaseId);
    }

    /**
     * 获取状态名称
     *
     * @param status 状态码
     * @return 状态名称
     */
    public String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        try {
            ConfigStatus configStatus = ConfigStatus.getByCode(status);
            return configStatus.getDesc();
        } catch (Exception e) {
            return "未知状态(" + status + ")";
        }
    }
}
