package com.xiaomi.micar.site.model.bpm;

import lombok.Data;

/**
 * BPM回调数据传输对象
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
public class BpmCallbackDTO {
    /**
     * 业务键
     */
    private String businessKey;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 流程实例状态
     * "RUNNING", "审批中"
     * "TERMINATED", "已终止",
     * "COMPLETED", "已通过"
     * "REJECTED", "已驳回"
     */
    private String processInstanceStatus;
    /**
     * 审批意见
     */
    private String comment;
    /**
     * 审批人
     */
    private Operator operator;
}
