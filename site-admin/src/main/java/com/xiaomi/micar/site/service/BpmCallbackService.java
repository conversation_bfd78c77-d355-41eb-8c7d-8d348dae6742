package com.xiaomi.micar.site.service;

import com.xiaomi.micar.site.dao.SiteOpsLogEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.event.ApprovalCompletedEvent;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.AuditType;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.enums.OperationType;
import com.xiaomi.micar.site.enums.ProcessInstanceStatusEnum;
import com.xiaomi.micar.site.model.bpm.BpmCallbackDTO;
import com.xiaomi.micar.site.model.bpm.BpmCallbackResponse;
import com.xiaomi.micar.site.model.bpm.Operator;
import com.xiaomi.micar.site.event.DiffContext;
import com.xiaomi.micar.site.service.callback.CallbackValidator;
import com.xiaomi.micar.site.service.callback.DraftDeployHandler;
import com.xiaomi.micar.site.service.callback.DraftDeployHandlerFactory;
import com.xiaomi.micar.site.service.callback.DraftStatusManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * BPM回调服务 (重构版)
 * 采用策略模式和职责分离原则，提高代码的可维护性和扩展性
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class BpmCallbackService {

    @Resource
    private CallbackValidator callbackValidator;

    @Resource
    private DraftStatusManager statusManager;

    @Resource
    private SiteOpsLogEngine opsLogEngine;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private DraftDeployHandlerFactory deployHandlerFactory;

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 处理站点配置审批回调
     */
    public BpmCallbackResponse handleSiteConfigCallback(BpmCallbackDTO callback) {


        // 参数校验
        BpmCallbackResponse validationResult = callbackValidator.validateCallback(callback);
        if (validationResult != null) {
            return validationResult;
        }

        try {
            // 获取草稿信息
            SitePageConfigDraftEntity draft = callbackValidator.getDraftByBusinessKey(callback.getBusinessKey());
            if (draft == null) {
                return BpmCallbackResponse.error("未找到对应的草稿记录");
            }

            // 检查草稿状态
            if (!callbackValidator.isValidDraftStatus(draft)) {
                log.warn("草稿状态不是审核中或下线审核中: draftId={}, status={}",
                        draft.getId(), draft.getStatus());
                return BpmCallbackResponse.error("草稿状态不是审核中或下线审核中，当前状态: " + draft.getStatus());
            }

            // 记录处理开始日志
            log.info("开始处理BPM回调: draftId={}, type={}, status={}, processStatus={}, operator={}, comment={}",
                    draft.getId(), getDraftTypeName(draft.getType()), draft.getStatus(),
                    callback.getProcessInstanceStatus(),
                    callback.getOperator() != null ? callback.getOperator().getDisplayName() : "未知",
                    callback.getComment());

            // 处理审批结果
            BpmCallbackResponse response = processApprovalResult(draft, callback);

            // 记录处理完成日志
            if (response.getCode() == 0) {
                log.info("BPM回调处理完成: draftId={}, type={}, processStatus={}, operator={}",
                        draft.getId(), getDraftTypeName(draft.getType()), callback.getProcessInstanceStatus(),
                        callback.getOperator() != null ? callback.getOperator().getDisplayName() : "未知");
            } else {
                log.error("BPM回调处理失败: draftId={}, type={}, processStatus={}, operator={}",
                        draft.getId(), getDraftTypeName(draft.getType()), callback.getProcessInstanceStatus(),
                        callback.getOperator() != null ? callback.getOperator().getDisplayName() : "未知");
            }

            return response;

        } catch (Exception e) {
            log.error("处理站点配置审批回调异常: {}", e.getMessage(), e);
            return BpmCallbackResponse.error("处理回调异常: " + e.getMessage());
        }
    }

    /**
     * 处理审批结果
     */
    private BpmCallbackResponse processApprovalResult(SitePageConfigDraftEntity draft, BpmCallbackDTO callback) {
        try {
            ProcessInstanceStatusEnum processStatus = ProcessInstanceStatusEnum.valueOf(callback.getProcessInstanceStatus());

            switch (processStatus) {
                case REJECTED:
                    getSelf().handleApprovalRejected(draft, callback.getOperator(), callback.getComment());
                    break;
                case COMPLETED:
                    getSelf().deployConfigAsync(draft, callback.getOperator(), callback.getComment());
                    break;
                case RUNNING:
                    log.info("流程仍在运行中: draftId={}, processStatus={}", draft.getId(), processStatus);
                    break;
                case TERMINATED:
                    log.warn("流程被终止: draftId={}, processStatus={}", draft.getId(), processStatus);
                    getSelf().handleApprovalRejected(draft, callback.getOperator(), callback.getComment());
                    break;
                default:
                    log.warn("未处理的流程状态: draftId={}, processStatus={}", draft.getId(), processStatus);
                    break;
            }
            return BpmCallbackResponse.success();
        } catch (IllegalArgumentException e) {
            log.error("无效的流程状态: draftId={}, processStatus={}", 
                    draft.getId(), callback.getProcessInstanceStatus(), e);
            return BpmCallbackResponse.error("无效的流程状态: " + callback.getProcessInstanceStatus());
        } catch (Exception e) {
            log.error("处理审批结果异常: draftId={}, error={}", 
                    draft.getId(), e.getMessage(), e);
            return BpmCallbackResponse.error("处理审批结果异常: " + e.getMessage());
        }
    }

    /**
     * 处理审批驳回
     * 注意：审批驳回不发送robot通知，只记录操作日志
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handleApprovalRejected(SitePageConfigDraftEntity draft, Operator operator, String comment) {
        try {
            // 执行审批驳回状态转移
            boolean updated = statusManager.executeRejectionTransition(draft.getId(), draft.getStatus());
            if (!updated) {
                log.error("审批驳回状态转移失败: draftId={}, currentStatus={}",
                        draft.getId(), statusManager.getStatusName(draft.getStatus()));
                throw new RuntimeException("审批驳回状态转移失败，可能是并发修改导致");
            }

            // 记录操作日志
            boolean isOfflineAudit = draft.getStatus() != null &&
                    draft.getStatus().equals(ConfigStatus.OFFLINE_AUDIT.getCode());
            OperationType operationType = isOfflineAudit ? OperationType.OFFLINE_REJECT : OperationType.ONLINE_REJECT;
            opsLogEngine.saveBpmCallbackLog(operationType, operator, comment, draft);

            log.info("审批驳回处理完成: draftId={}, operator={}, comment={}",
                    draft.getId(), operator != null ? operator.getDisplayName() : "未知", comment);
        } catch (Exception e) {
            log.error("审批驳回处理异常: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            throw e instanceof RuntimeException ? (RuntimeException) e : new RuntimeException(e);
        }
    }

    /**
     * 异步部署配置
     *
     * 关键设计原则：
     * 1. 整个部署过程必须在一个事务中完成
     * 2. 任何异常都必须导致完整回滚
     * 3. 不允许出现中间状态的数据不一致
     * 4. 状态转移和业务操作必须原子性执行
     *
     */
    @Async("eventPublishExecutor")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void deployConfigAsync(SitePageConfigDraftEntity draft, Operator operator, String comment) {
        // 在状态转移前先确定审核类型
        AuditType auditType = AuditType.fromDraftStatus(draft.getStatus());

        ApprovalCompletedEvent.ResultType resultType = ApprovalCompletedEvent.ResultType.SUCCESS;
        String resultMessage = "配置部署成功";
        DiffContext diffContext = null;

        try {
            // 获取部署处理器
            DraftDeployHandler handler = deployHandlerFactory.getHandler(draft.getType());
            if (handler == null) {
                throw new RuntimeException("未找到部署处理器: " + draft.getType());
            }

            // 执行部署逻辑并获取DiffContext
            diffContext = handler.deploy(draft, auditType.isOffline(), operator, comment);

            // 记录操作日志
            OperationType opType = auditType.isOffline() ? OperationType.OFFLINE_AUDIT : OperationType.ONLINE_AUDIT;
            opsLogEngine.saveBpmCallbackLog(opType, operator, comment, draft);

        } catch (Exception e) {
            log.error("业务逻辑执行异常: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            resultType = ApprovalCompletedEvent.ResultType.FAILED;
            resultMessage = "操作失败: " + e.getMessage();
            throw e instanceof RuntimeException ? (RuntimeException) e : new RuntimeException(e);
        } finally {
            // 发布审批完成事件
            publishApprovalCompletedEvent(draft, operator, comment, resultType, resultMessage, diffContext);
        }
    }






    /**
     * 统一发送审批完成事件（支持DiffContext）
     */
    private void publishApprovalCompletedEvent(SitePageConfigDraftEntity draft,
                                               Operator operator,
                                               String comment,
                                               ApprovalCompletedEvent.ResultType resultType,
                                               String resultMessage,
                                               DiffContext diffContext) {
        try {
            String operatorName = operator != null ? operator.getDisplayName() : "未知操作人";
            ApprovalCompletedEvent event = new ApprovalCompletedEvent(
                this, draft, operatorName, resultType, resultMessage, comment, diffContext
            );
            eventPublisher.publishEvent(event);
            log.info("审批完成事件发布成功: draftId={}, operator={}, result={}, diffContext={}",
                    draft.getId(), operatorName, resultType.getDesc(),
                    diffContext != null ? "有上下文" : "无上下文");
        } catch (Exception eventException) {
            // 事件发布失败不影响主流程，只记录日志
            log.error("发布审批完成事件异常: draftId={}, resultType={}, error={}",
                    draft.getId(), resultType.getDesc(), eventException.getMessage(), eventException);
        }
    }






    /**
     * 获取草稿类型名称
     */
    private String getDraftTypeName(Integer type) {
        if (type == null) {
            return "未知";
        }
        try {
            return DraftType.getByCode(type).getDesc();
        } catch (RuntimeException e) {
            return "未知类型(" + type + ")";
        }
    }

    /**
     * 获取自我引用，避免循环依赖
     */
    private BpmCallbackService getSelf() {
        return applicationContext.getBean(BpmCallbackService.class);
    }
}
