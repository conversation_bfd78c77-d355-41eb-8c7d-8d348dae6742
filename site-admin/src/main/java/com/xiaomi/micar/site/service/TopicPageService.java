package com.xiaomi.micar.site.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageInfoEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.enums.PageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 专题页管理服务
 * 
 * 负责管理专题页面，包括：
 * 1. 根据 draft 表的 pageId 查询 site_page_info 表
 * 2. 过滤出 page_type = 1 的专题页
 * 3. 提供专题页的CRUD操作
 */
@Slf4j
@Service
public class TopicPageService {

    @Resource
    private SitePageInfoEngine pageInfoEngine;
    
    @Resource
    private SitePageConfigDraftEngine draftEngine;

    /**
     * 获取所有专题页信息
     * 
     * @return 专题页列表
     */
    public List<SitePageInfoEntity> getAllTopicPages() {
        log.info("开始查询所有专题页信息");
        
        LambdaQueryWrapper<SitePageInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SitePageInfoEntity::getPageType, PageTypeEnum.TOPIC_PAGE.getCode()); // 专题页
        
        List<SitePageInfoEntity> topicPages = pageInfoEngine.list(queryWrapper);
        
        log.info("查询到 {} 个专题页", topicPages.size());
        return topicPages;
    }

    /**
     * 根据 pageId 查询专题页信息
     * 
     * @param pageId 页面ID
     * @return 专题页信息，如果不是专题页则返回 null
     */
    public SitePageInfoEntity getTopicPageById(String pageId) {
        log.info("查询专题页信息：pageId={}", pageId);
        
        LambdaQueryWrapper<SitePageInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SitePageInfoEntity::getPageId, pageId)
                   .eq(SitePageInfoEntity::getPageType, PageTypeEnum.TOPIC_PAGE.getCode()); // 专题页
        
        SitePageInfoEntity topicPage = pageInfoEngine.getOne(queryWrapper);
        
        if (topicPage != null) {
            log.info("找到专题页：pageId={}, pageName={}", pageId, topicPage.getPageName());
        } else {
            log.info("未找到专题页或该页面不是专题页：pageId={}", pageId);
        }
        
        return topicPage;
    }

    /**
     * 检查指定页面是否为专题页
     * 
     * @param pageId 页面ID
     * @return true 如果是专题页，false 否则
     */
    public boolean isTopicPage(String pageId) {
        return getTopicPageById(pageId) != null;
    }

    /**
     * 获取专题页的草稿列表
     *
     * @param pageId 专题页ID，如果为null则查询所有专题页的草稿
     * @return 该专题页的草稿列表
     */
    public List<SitePageConfigDraftEntity> getTopicPageDrafts(String pageId) {
        return getTopicPageDrafts(pageId, null);
    }

    /**
     * 获取专题页的草稿列表（支持名称搜索）
     *
     * @param pageId 专题页ID，如果为null则查询所有专题页的草稿
     * @param name 专题页名称搜索关键词，支持模糊搜索
     * @return 该专题页的草稿列表
     */
    public List<SitePageConfigDraftEntity> getTopicPageDrafts(String pageId, String name) {
        log.info("查询专题页草稿列表：pageId={}, name={}", pageId, name);

        LambdaQueryWrapper<SitePageConfigDraftEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (pageId != null) {
            // 查询指定专题页的草稿
            if (!isTopicPage(pageId)) {
                log.warn("页面不是专题页，无法查询草稿：pageId={}", pageId);
                return Collections.emptyList();
            }
            queryWrapper.eq(SitePageConfigDraftEntity::getPageId, pageId);
        }

        // 添加名称搜索条件
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(SitePageConfigDraftEntity::getName, name);
        }

        queryWrapper.eq(SitePageConfigDraftEntity::getType, DraftType.TOPIC_PAGE_CONTENT.getCode())
                   .eq(SitePageConfigDraftEntity::getDeleted, 0)
                   .orderByDesc(SitePageConfigDraftEntity::getCreateTime);

        List<SitePageConfigDraftEntity> drafts = draftEngine.list(queryWrapper);

        log.info("查询到专题页草稿 {} 个：pageId={}, name={}", drafts.size(), pageId, name);
        return drafts;
    }

    /**
     * 分页查询专题页的草稿列表
     *
     * @param pageId 专题页ID，如果为null则查询所有专题页的草稿
     * @param name 专题页名称搜索关键词，支持模糊搜索
     * @param status 草稿状态过滤
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 分页结果
     */
    public IPage<SitePageConfigDraftEntity> getTopicPageDraftsWithPagination(String pageId, String name, Integer status, long page, long size) {
        log.info("分页查询专题页草稿列表：pageId={}, name={}, status={}, page={}, size={}", pageId, name, status, page, size);

        LambdaQueryWrapper<SitePageConfigDraftEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (pageId != null) {
            // 查询指定专题页的草稿
            if (!isTopicPage(pageId)) {
                log.warn("页面不是专题页，无法查询草稿：pageId={}", pageId);
                return new Page<>(page, size); // 返回空的分页结果
            }
            queryWrapper.eq(SitePageConfigDraftEntity::getPageId, pageId);
        }

        // 添加名称搜索条件
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(SitePageConfigDraftEntity::getName, name);
        }

        // 添加状态过滤条件
        if (status != null) {
            queryWrapper.eq(SitePageConfigDraftEntity::getStatus, status);
        }

        queryWrapper.eq(SitePageConfigDraftEntity::getType, DraftType.TOPIC_PAGE_CONTENT.getCode())
                   .eq(SitePageConfigDraftEntity::getDeleted, 0)
                   .orderByDesc(SitePageConfigDraftEntity::getUpdateTime);

        // 创建分页对象
        Page<SitePageConfigDraftEntity> pageParam = new Page<>(page, size);

        // 执行分页查询
        IPage<SitePageConfigDraftEntity> result = draftEngine.page(pageParam, queryWrapper);

        log.info("分页查询到专题页草稿：总数={}, 当前页={}, 每页大小={}, 当前页记录数={}",
                result.getTotal(), result.getCurrent(), result.getSize(), result.getRecords().size());

        return result;
    }

}