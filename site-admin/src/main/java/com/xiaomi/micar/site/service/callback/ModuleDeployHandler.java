package com.xiaomi.micar.site.service.callback;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageModuleEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.event.DiffContext;
import com.xiaomi.micar.site.model.bpm.Operator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 模块部署处理器
 * 专门处理模块类型的草稿部署
 * <p>
 * 新架构理念：
 * - site_page_module、site_page_module_strategy、site_page_record 三张表存储当前线上生效的真实数据
 * - site_page_config 作为这些数据的聚合视图，通过 PageConfigAggregationService 生成
 * - 模块部署的核心操作是更新 site_page_module 表中的 config 字段
 * - 为保持向后兼容性，仍会更新 site_page_info 和 site_page_config 表
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class ModuleDeployHandler implements DraftDeployHandler {

    @Resource
    private SitePageModuleEngine moduleEngine;

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Resource
    private DraftStatusManager statusManager;



    /**
     * 内部部署结果类
     */
    private static class InternalDeployResult {
        final Long moduleId;
        final DiffContext diffContext;

        InternalDeployResult(Long moduleId, DiffContext diffContext) {
            this.moduleId = moduleId;
            this.diffContext = diffContext;
        }
    }

    @Override
    public boolean supports(Integer draftType) {
        // 支持 MODULE_CONTENT 和 RECORD_CONTENT 类型，统一按模块整体处理
        return draftType != null &&
                (draftType.equals(DraftType.MODULE_CONTENT.getCode()) ||
                        draftType.equals(DraftType.RECORD_CONTENT.getCode()));
    }

    /**
     * 模块部署 - 严格事务控制
     * <p>
     * 新架构（统一处理 MODULE_CONTENT 和 RECORD_CONTENT 类型）：
     * 1. 核心操作：更新 site_page_module 表中对应模块的 config 字段
     * 2. RECORD_CONTENT 类型的草稿现在也按模块整体处理，不再按记录粒度处理
     * 3. banner 等模块作为整体配置，简化管理逻辑
     * <p>
     * 注意：此方法会被BpmCallbackService在事务中调用，加入当前事务
     * 任何异常都会导致整个部署事务回滚
     * 兼容性处理已移至上层 deployConfigAsync 方法中统一处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public DiffContext deploy(SitePageConfigDraftEntity draft, boolean isOfflineAudit, Operator operator, String comment) {
        InternalDeployResult result;

        if (isOfflineAudit) {
            result = executeOfflineDeployment(draft);
        } else {
            result = executeOnlineDeployment(draft);
        }

        log.info("模块部署完成: draftId={}, pageId={}, moduleId={}, isOfflineAudit={}",
                draft.getId(), draft.getPageId(), result.moduleId, isOfflineAudit);

        return result.diffContext;
    }

    @Override
    public String getHandlerName() {
        return "ModuleDeployHandler(支持MODULE和RECORD类型)";
    }


    /**
     * 执行上线部署
     * 将草稿配置应用到模块，并收集diff所需信息
     */
    private InternalDeployResult executeOnlineDeployment(SitePageConfigDraftEntity draft) {
        // 查找并验证模块
        SitePageModuleEntity module = findAndValidateModule(draft);

        // 收集diff所需的信息（在更新配置前）
        String oldConfig = module.getConfig();
        String newConfig = draft.getConfig();
        String componentType = module.getComponent();
        String moduleName = module.getModuleName();

        // 创建DiffContext
        DiffContext diffContext = DiffContext.forOnlineOperation(
            oldConfig, newConfig, componentType, moduleName, module.getId());

        // 更新模块配置为草稿配置
        updateModuleConfig(module, newConfig, "模块上线失败");

        // 更新草稿状态
        updateDraftStatus(draft, module.getId());

        // 返回结果，包含diff上下文信息
        return new InternalDeployResult(module.getId(), diffContext);
    }

    /**
     * 执行下线部署
     * 将模块配置恢复到之前的版本，并收集diff所需信息
     */
    private InternalDeployResult executeOfflineDeployment(SitePageConfigDraftEntity draft) {
        // 查找并验证模块
        SitePageModuleEntity module = findAndValidateModule(draft);

        // 收集diff所需的信息（在更新配置前）
        String currentConfig = module.getConfig();
        String previousConfig = findPreviousOnlineConfig(draft);
        String componentType = module.getComponent();
        String moduleName = module.getModuleName();

        // 创建DiffContext
        DiffContext diffContext = DiffContext.forOfflineOperation(
            currentConfig, previousConfig, componentType, moduleName, module.getId());

        // 更新模块配置为之前的配置
        updateModuleConfig(module, previousConfig, "模块下线失败");

        // 更新草稿状态
        updateDraftStatus(draft, module.getId());

        // 返回结果，包含diff上下文信息
        return new InternalDeployResult(module.getId(), diffContext);
    }

    /**
     * 查找并验证模块
     */
    private SitePageModuleEntity findAndValidateModule(SitePageConfigDraftEntity draft) {
        if (draft.getModuleId() == null) {
            throw new RuntimeException("模块ID为空");
        }

        SitePageModuleEntity module = moduleEngine.lambdaQuery()
                .eq(SitePageModuleEntity::getPageId, draft.getPageId())
                .eq(SitePageModuleEntity::getId, Long.valueOf(draft.getModuleId()))
                .one();

        if (module == null) {
            throw new RuntimeException("未找到模块: " + draft.getModuleId());
        }

        return module;
    }

    /**
     * 更新模块配置
     */
    private void updateModuleConfig(SitePageModuleEntity module, String config, String errorMessage) {
        // 获取当前时间
        Date currentTime = new Date();

        LambdaUpdateWrapper<SitePageModuleEntity> updateWrapper = new LambdaUpdateWrapper<>(SitePageModuleEntity.class)
                .eq(SitePageModuleEntity::getPageId, module.getPageId())
                .eq(SitePageModuleEntity::getId, module.getId())
                .set(SitePageModuleEntity::getConfig, config)
                .set(SitePageModuleEntity::getUpdateTime, currentTime);

        log.info("尝试更新模块配置: pageId={}, moduleId={}, config={}, updateTime={}",
                module.getPageId(), module.getId(), config, currentTime);

        boolean updated = moduleEngine.update(null, updateWrapper);

        if (!updated) {
            log.error("模块配置更新失败: pageId={}, moduleId={}, config={}, errorMessage={}",
                    module.getPageId(), module.getId(), config, errorMessage);
            throw new RuntimeException(errorMessage + " - pageId: " + module.getPageId() + ", moduleId: " + module.getId());
        }

        log.info("模块配置更新成功: pageId={}, moduleId={}, updateTime={}",
                module.getPageId(), module.getId(), currentTime);
    }

    /**
     * 更新草稿状态
     */
    private void updateDraftStatus(SitePageConfigDraftEntity draft, Long moduleId) {
        // 使用直接转移：AUDITING -> DEPLOYED 或 OFFLINE_AUDIT -> DRAFT（跳过中间状态DEPLOYING）
        boolean updated = statusManager.executeDirectApprovalTransitionWithReleaseId(
                draft.getId(), draft.getStatus(), moduleId);
        if (!updated) {
            throw new RuntimeException("状态更新失败");
        }
    }

    /**
     * 查找历史上已经上线的最近版本配置
     * <p>
     * 下线逻辑：
     * 1. 查找同一模块历史中已上线的版本（状态为 DEPLOYED）
     * 2. 如果找到，恢复到该上线版本的配置
     * 3. 如果找不到，返回 null，表示模块彻底下线
     */
    private String findPreviousOnlineConfig(SitePageConfigDraftEntity currentDraft) {
        try {
            SitePageConfigDraftEntity previousOnlineVersion = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getPageId, currentDraft.getPageId())
                    .eq(SitePageConfigDraftEntity::getModuleId, currentDraft.getModuleId())
                    .eq(SitePageConfigDraftEntity::getStatus, ConfigStatus.DEPLOYED.getCode()) // 查找已上线的版本
                    .ne(SitePageConfigDraftEntity::getId, currentDraft.getId())
                    .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
                    .last("LIMIT 1")
                    .one();

            if (previousOnlineVersion != null) {
                log.info("找到历史上线版本: draftId={}, updateTime={}",
                        previousOnlineVersion.getId(), previousOnlineVersion.getUpdateTime());
                return previousOnlineVersion.getConfig();
            } else {
                log.info("未找到历史上线版本，模块将彻底下线: pageId={}, moduleId={}",
                        currentDraft.getPageId(), currentDraft.getModuleId());
                return null; // 返回 null 表示模块彻底下线
            }

        } catch (Exception e) {
            log.warn("查找历史上线版本失败: {}", e.getMessage());
            return null; // 异常情况也返回 null，表示模块下线
        }
    }

}
