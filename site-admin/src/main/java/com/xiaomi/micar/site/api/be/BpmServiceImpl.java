package com.xiaomi.micar.site.api.be;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.api.be.aop.BeLoginCheck;
import com.xiaomi.micar.site.model.bpm.BpmCallbackDTO;
import com.xiaomi.micar.site.model.bpm.BpmCallbackResponse;
import com.xiaomi.micar.site.model.bpm.BpmSubmitRequest;
import com.xiaomi.micar.site.service.BpmCallbackService;
import com.xiaomi.micar.site.service.BpmInternalService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * BPM服务实现
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
@Slf4j
@ApiModule(value = "后台管理#bpm接口", apiInterface = BpmService.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class BpmServiceImpl implements BpmService {

    @Resource
    private BpmCallbackService bpmCallbackService;

    @Resource
    private BpmInternalService bpmInternalService;

    @Override
    @BeLoginCheck
    public Result<Void> submit(BpmSubmitRequest request) {
        log.info("开始处理BPM提交请求: {}", JsonUtil.toJSONString(request));

        // 委托给内部服务处理
        return bpmInternalService.submitBpmApproval(
                request.getDraftId(),
                request.getOperationType(),
                request.getRemark(),
                request.getUserInfo());
    }

    @Override
    public BpmCallbackResponse handleCallback(BpmCallbackDTO callback) {
        log.info("原始回调数据: {}", JsonUtil.toJSONString(callback));
        try {
            if (callback == null) {
                log.warn("解析回调对象参数为空");
                return BpmCallbackResponse.error("解析回调JSON参数失败");
            }
            // 调用BpmCallbackService处理回调
            BpmCallbackResponse response = bpmCallbackService.handleSiteConfigCallback(callback);
            log.info("处理站点配置审批回调完成, 结果: {}", response);
            return response;
        } catch (Exception e) {
            log.error("处理站点配置审批回调异常: {}", e.getMessage(), e);
            return BpmCallbackResponse.error("处理回调异常: " + e.getMessage());
        }
    }
}
