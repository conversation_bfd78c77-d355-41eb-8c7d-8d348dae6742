package com.xiaomi.micar.site;

import com.alibaba.nacos.api.annotation.NacosProperties;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.xiaomi.micar.site.service.robot.RobotConfig;
import com.xiaomi.mone.dubbo.docs.EnableDubboApiDocs;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 官网后台管理APP
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@SpringBootApplication
@EnableDubboApiDocs
@EnableNacosConfig(
        globalProperties = @NacosProperties(
                serverAddr = "${nacos.config.addr}",
                namespace = "${nacos.config.namespace}"
        )
)
@EnableDubbo
@EnableConfigurationProperties(RobotConfig.class)
@EnableTransactionManagement(proxyTargetClass = true)
@org.springframework.scheduling.annotation.EnableAsync
public class SiteAdminApp {

    public static void main(String[] args) {
        SpringApplication.run(SiteAdminApp.class, args);
    }
}
