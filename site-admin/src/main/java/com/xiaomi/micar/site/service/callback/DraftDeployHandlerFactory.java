package com.xiaomi.micar.site.service.callback;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 草稿部署处理器工厂
 * 负责根据草稿类型选择合适的部署处理器
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class DraftDeployHandlerFactory {

    @Resource
    private ModuleReOrderDeployHandler moduleReOrderDeployHandler;

    @Resource
    private RecordDeployHandler recordDeployHandler;

    @Resource
    private ModuleDeployHandler moduleDeployHandler;

    @Resource
    private TopicPageContentDeployHandler topicPageContentDeployHandler;

    private List<DraftDeployHandler> handlers;

    @PostConstruct
    public void init() {
        handlers = new ArrayList<>();
        handlers.add(moduleReOrderDeployHandler);          // 处理 MODULE_ORDER 类型
        handlers.add(recordDeployHandler);        // 当前不处理任何类型
        handlers.add(moduleDeployHandler);        // 处理 MODULE_CONTENT 和 RECORD_CONTENT 类型
        handlers.add(topicPageContentDeployHandler);   // 处理 PAGE_CONTENT 类型
        
        log.info("初始化部署处理器工厂，注册处理器数量: {}", handlers.size());
        for (DraftDeployHandler handler : handlers) {
            log.info("注册部署处理器: {}", handler.getHandlerName());
        }
    }

    /**
     * 根据草稿类型获取对应的部署处理器
     *
     * @param draftType 草稿类型
     * @return 部署处理器
     */
    public DraftDeployHandler getHandler(Integer draftType) {
        if (draftType == null) {
            throw new IllegalArgumentException("草稿类型不能为空");
        }

        for (DraftDeployHandler handler : handlers) {
            if (handler.supports(draftType)) {
                log.info("找到匹配的部署处理器: draftType={}, handler={}", 
                        draftType, handler.getHandlerName());
                return handler;
            }
        }

        log.error("未找到支持的部署处理器: draftType={}", draftType);
        throw new IllegalArgumentException("不支持的草稿类型: " + draftType);
    }

    /**
     * 获取所有注册的处理器
     *
     * @return 处理器列表
     */
    public List<DraftDeployHandler> getAllHandlers() {
        return new ArrayList<>(handlers);
    }

    /**
     * 注册新的处理器
     *
     * @param handler 处理器
     */
    public void registerHandler(DraftDeployHandler handler) {
        if (handler != null && !handlers.contains(handler)) {
            handlers.add(handler);
            log.info("注册新的部署处理器: {}", handler.getHandlerName());
        }
    }
}
