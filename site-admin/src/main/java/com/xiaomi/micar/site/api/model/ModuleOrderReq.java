package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 模块顺序管理请求
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ModuleOrderReq extends BaseMgrReq {

    /**
     * 页面ID
     */
    @NotBlank(message = "页面ID不能为空")
    private String pageId;

    /**
     * 人群策略分组code
     */
    @NotBlank(message = "策略分组code不能为空")
    private String strategyCode;

    /**
     * 模块顺序配置
     */
    @NotEmpty(message = "模块顺序配置不能为空")
    @Valid
    private List<ModuleOrderItem> modules;

    /**
     * 模块顺序项
     */
    @Data
    public static class ModuleOrderItem {
        /**
         * 模块ID
         */
        @NotNull(message = "模块ID不能为空")
        private Long moduleId;

        /**
         * 优先级
         */
        @NotNull(message = "优先级不能为空")
        private Integer priority;
    }
}
