package com.xiaomi.micar.site.model.bpm;

import lombok.Data;

/**
 * BPM回调响应
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Data
public class BpmCallbackResponse {
    /**
     * 响应码，0表示成功
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 创建成功响应
     */
    public static BpmCallbackResponse success() {
        BpmCallbackResponse response = new BpmCallbackResponse();
        response.setCode(0);
        response.setMessage("success");
        return response;
    }
    
    /**
     * 创建错误响应
     */
    public static BpmCallbackResponse error(String message) {
        BpmCallbackResponse response = new BpmCallbackResponse();
        response.setCode(1);
        response.setMessage(message);
        return response;
    }
}
