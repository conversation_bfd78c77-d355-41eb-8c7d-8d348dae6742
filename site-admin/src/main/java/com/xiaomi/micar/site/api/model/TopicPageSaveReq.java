package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 专题页保存请求
 *
 * <AUTHOR>
 * @since 2025/06/24
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TopicPageSaveReq extends BaseMgrReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 草稿ID（编辑时传入，新建时为空）
     */
    private Long id;

    /**
     * 专题页ID
     */
    private String pageId;

    /**
     * 专题页名称
     */
    private String name;

    /**
     * 专题页配置JSON
     */
    private String config;

    /**
     * 备注
     */
    private String remark;
}
