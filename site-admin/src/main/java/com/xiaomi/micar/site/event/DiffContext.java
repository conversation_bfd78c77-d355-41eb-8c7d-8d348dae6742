package com.xiaomi.micar.site.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配置差异生成上下文
 * 封装生成配置差异所需的所有信息
 *
 * <AUTHOR>
 * @since 2025/07/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DiffContext {
    
    /**
     * 旧配置JSON字符串
     */
    private String oldConfig;
    
    /**
     * 新配置JSON字符串
     */
    private String newConfig;
    
    /**
     * 操作类型：新增、修改、删除
     */
    private String operationType;
    
    /**
     * 组件类型：Navigation、News、Banner、TabNews等
     */
    private String componentType;
    
    /**
     * 楼层名称（从site_page_module.module_name获取）
     */
    private String moduleName;
    
    /**
     * 模块ID
     */
    private Long moduleId;
    
    /**
     * 是否为下线操作
     */
    private boolean isOfflineOperation;
    
    /**
     * 创建上线操作的DiffContext
     */
    public static DiffContext forOnlineOperation(String oldConfig, String newConfig, 
                                                String componentType, String moduleName, Long moduleId) {
        return new DiffContext(oldConfig, newConfig, "修改", componentType, moduleName, moduleId, false);
    }
    
    /**
     * 创建下线操作的DiffContext
     */
    public static DiffContext forOfflineOperation(String currentConfig, String previousConfig, 
                                                 String componentType, String moduleName, Long moduleId) {
        return new DiffContext(currentConfig, previousConfig, "删除", componentType, moduleName, moduleId, true);
    }
    
    /**
     * 检查是否有足够的信息生成diff
     */
    public boolean isValid() {
        return componentType != null && operationType != null;
    }
}
