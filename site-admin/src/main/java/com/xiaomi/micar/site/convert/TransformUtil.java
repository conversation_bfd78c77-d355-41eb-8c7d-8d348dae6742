package com.xiaomi.micar.site.convert;

import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.config.FrontendUrlConfig;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageRecordEntity;

public class TransformUtil {


    public static RecordDTO draftToDTO(SitePageConfigDraftEntity entity) {
        return draftToDTO(entity, null);
    }

    public static RecordDTO draftToDTO(SitePageConfigDraftEntity entity, FrontendUrlConfig urlConfig) {
        RecordDTO dto = new RecordDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setPageId(entity.getPageId());
        dto.setModuleId(entity.getModuleId());
        dto.setConfig(entity.getConfig());
        dto.setRemark(entity.getRemark());
        dto.setPriority(entity.getPriority());
        dto.setExposeFrom(entity.getExposeFrom());
        dto.setExposeTo(entity.getExposeTo());
        dto.setOperatorId(entity.getOperatorId());
        dto.setOperatorName(entity.getOperatorName());
        dto.setStatus(entity.getStatus());
        dto.setCreateTime(entity.getCreateTime().getTime());
        dto.setUpdateTime(entity.getUpdateTime().getTime());

        // 生成 C 端页面访问链接
        if (urlConfig != null && entity.getPageId() != null) {
            dto.setPageUrl(urlConfig.generatePageUrl(entity.getPageId()));
        }

        return dto;
    }

    public static SitePageConfigDraftEntity dtoToDraft(RecordDTO dto, SitePageConfigDraftEntity entity) {
        if (entity == null) {
            entity = new SitePageConfigDraftEntity();
        }
        entity.setName(dto.getName());
        entity.setPageId(dto.getPageId());
        entity.setModuleId(dto.getModuleId());
        entity.setConfig(dto.getConfig());
        entity.setRemark(dto.getRemark());
        entity.setPriority(dto.getPriority());
        entity.setExposeFrom(dto.getExposeFrom());
        entity.setExposeTo(dto.getExposeTo());
        entity.setOperatorId(dto.getOperatorId());
        entity.setOperatorName(dto.getOperatorName());
        return entity;
    }

    public static RecordDTO entityToDTO(SitePageRecordEntity entity) {
        RecordDTO dto = new RecordDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setPageId(entity.getPageId());
        // 将String类型的moduleId转换为Integer类型
        dto.setModuleId(entity.getModuleId() != null ? Integer.valueOf(entity.getModuleId()) : null);
        dto.setConfig(entity.getConfig());
        dto.setRemark(entity.getRemark());
        dto.setPriority(entity.getPriority());
        dto.setOperatorId(entity.getOperatorId());
        dto.setOperatorName(entity.getOperatorName());
        dto.setCreateTime(entity.getCreateTime().getTime());
        dto.setUpdateTime(entity.getUpdateTime().getTime());
        return dto;
    }

}
