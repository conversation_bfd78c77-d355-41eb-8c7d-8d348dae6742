package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 专题页草稿详情请求
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TopicPageDraftDetailReq extends BaseMgrReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 草稿ID
     */
    private Long id;

    /**
     * 页面ID
     */
    private String pageId;
}
