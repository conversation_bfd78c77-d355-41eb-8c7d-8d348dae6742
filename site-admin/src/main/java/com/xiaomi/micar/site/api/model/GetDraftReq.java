package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.model.BaseMgrReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 获取站点模块配置请求
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetDraftReq extends BaseMgrReq implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String pageId;

    private Integer moduleId;


}