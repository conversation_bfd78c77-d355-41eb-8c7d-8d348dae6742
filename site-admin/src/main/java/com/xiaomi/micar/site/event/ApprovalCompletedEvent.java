package com.xiaomi.micar.site.event;

import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 审批完成事件
 * 当审批流程最终完成时发布此事件，用于上报最终执行结果
 *
 * <AUTHOR>
 * @since 2025/07/17
 */
@Getter
public class ApprovalCompletedEvent extends ApplicationEvent {
    
    /**
     * 字段名常量定义
     */
    public static final String FIELD_DRAFT_ID = "draftId";
    public static final String FIELD_DRAFT_TYPE = "draftType";
    public static final String FIELD_PAGE_ID = "pageId";
    public static final String FIELD_OPERATOR = "operator";
    public static final String FIELD_RESULT_TYPE = "resultType";
    public static final String FIELD_RESULT_MESSAGE = "resultMessage";
    public static final String FIELD_COMMENT = "comment";
    public static final String FIELD_EVENT_TIMESTAMP = "eventTimestamp";
    
    /**
     * 草稿实体
     */
    private final SitePageConfigDraftEntity draft;
    
    /**
     * 操作人
     */
    private final String operator;
    
    /**
     * 执行结果类型
     */
    private final ResultType resultType;
    
    /**
     * 结果描述
     */
    private final String resultMessage;
    
    /**
     * 审批意见
     */
    private final String comment;

    /**
     * 配置差异生成所需的信息
     */
    private final DiffContext diffContext;

    /**
     * 事件时间戳
     */
    private final Long eventTimestamp;
    
    /**
     * 执行结果类型枚举
     */
    public enum ResultType {
        SUCCESS("执行成功"),
        REJECTED("审批驳回"), 
        FAILED("执行失败"),
        TERMINATED("流程终止");
        
        private final String desc;
        
        ResultType(String desc) {
            this.desc = desc;
        }
        
        public String getDesc() {
            return desc;
        }
    }
    
    /**
     * 构造函数
     *
     * @param source 事件源
     * @param draft 草稿实体
     * @param operator 操作人
     * @param resultType 执行结果类型
     * @param resultMessage 结果描述
     * @param comment 审批意见
     * @param diffContext 配置差异生成上下文
     */
    public ApprovalCompletedEvent(Object source, SitePageConfigDraftEntity draft,
                                String operator, ResultType resultType,
                                String resultMessage, String comment, DiffContext diffContext) {
        super(source);
        this.draft = draft;
        this.operator = operator;
        this.resultType = resultType;
        this.resultMessage = resultMessage;
        this.comment = comment;
        this.diffContext = diffContext;
        this.eventTimestamp = System.currentTimeMillis();
    }

    
    /**
     * 获取草稿ID
     */
    public Long getDraftId() {
        return draft != null ? draft.getId() : null;
    }
    
    /**
     * 获取草稿类型
     */
    public Integer getDraftType() {
        return draft != null ? draft.getType() : null;
    }
    
    /**
     * 获取页面ID
     */
    public String getPageId() {
        return draft != null ? draft.getPageId() : null;
    }
    
    /**
     * 是否执行成功
     */
    public boolean isSuccess() {
        return ResultType.SUCCESS.equals(resultType);
    }
    
    /**
     * 是否被驳回
     */
    public boolean isRejected() {
        return ResultType.REJECTED.equals(resultType);
    }
    
    /**
     * 是否执行失败
     */
    public boolean isFailed() {
        return ResultType.FAILED.equals(resultType);
    }
}
