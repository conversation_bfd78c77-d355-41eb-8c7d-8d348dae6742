package com.xiaomi.micar.site.service;

import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.enums.DraftType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 预览URL生成服务
 * 抽象预览链接生成逻辑，供多个地方复用
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@Service
public class PreviewUrlService {

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    /**
     * 生成预览URL
     *
     * @param draftId 草稿ID
     * @param ct 内容类型（可选）
     * @return 预览URL
     */
    public String generatePreviewUrl(Long draftId, String ct) {
        try {
            // 获取草稿信息
            DraftInfo draftInfo = getDraftInfo(String.valueOf(draftId));
            
            // 如果没有传入ct，从草稿类型推导
            if (ct == null && draftInfo.getDraft() != null) {
                ct = DraftType.getByCode(draftInfo.getDraft().getType()).getContentType();
            }

            String previewUrl;
            if (draftInfo.isTopicPage()) {
                // 专题页使用专题页URL，themeId 使用 pageId
                previewUrl = "https://www.xiaomiev.com/shop/home/<USER>" + draftInfo.getPageId() 
                        + "&_rt=rn&scene=admin_preview&ct=" + ct + "&cv=" + draftId;
                log.info("生成专题页预览URL: draftId={}, pageId={}, url={}", draftId, draftInfo.getPageId(), previewUrl);
            } else {
                // 普通页面使用首页URL
                previewUrl = "https://www.xiaomiev.com/shop/home/<USER>" + ct + "&cv=" + draftId;
                log.info("生成普通页面预览URL: draftId={}, url={}", draftId, previewUrl);
            }

            return previewUrl;
        } catch (Exception e) {
            log.error("生成预览URL失败: draftId={}", draftId, e);
            return "";
        }
    }

    /**
     * 生成预览URL（从草稿实体）
     *
     * @param draft 草稿实体
     * @return 预览URL
     */
    public String generatePreviewUrl(SitePageConfigDraftEntity draft) {
        if (draft == null || draft.getId() == null) {
            log.warn("草稿实体为空或ID为空");
            return "";
        }

        try {
            String ct = DraftType.getByCode(draft.getType()).getContentType();
            return generatePreviewUrl(draft.getId(), ct);
        } catch (Exception e) {
            log.error("生成预览URL失败: draftId={}", draft.getId(), e);
            return "";
        }
    }

    /**
     * 生成预览二维码URL
     *
     * @param draftId 草稿ID
     * @param ct 内容类型（可选）
     * @return 二维码URL
     */
    public String generatePreviewQrCodeUrl(Long draftId, String ct) {
        try {
            // 获取草稿信息
            DraftInfo draftInfo = getDraftInfo(String.valueOf(draftId));
            
            // 如果没有传入ct，从草稿类型推导
            if (ct == null && draftInfo.getDraft() != null) {
                ct = DraftType.getByCode(draftInfo.getDraft().getType()).getContentType();
            }

            // 构建二维码接口URL
            String qrCodeUrl = "/basic/page/preview/qrcode?ct=" + ct + "&cv=" + draftId;
            log.info("生成预览二维码URL: draftId={}, qrCodeUrl={}", draftId, qrCodeUrl);
            
            return qrCodeUrl;
        } catch (Exception e) {
            log.error("生成预览二维码URL失败: draftId={}", draftId, e);
            return "";
        }
    }

    /**
     * 生成预览二维码URL（从草稿实体）
     *
     * @param draft 草稿实体
     * @return 二维码URL
     */
    public String generatePreviewQrCodeUrl(SitePageConfigDraftEntity draft) {
        if (draft == null || draft.getId() == null) {
            log.warn("草稿实体为空或ID为空");
            return "";
        }

        try {
            String ct = DraftType.getByCode(draft.getType()).getContentType();
            return generatePreviewQrCodeUrl(draft.getId(), ct);
        } catch (Exception e) {
            log.error("生成预览二维码URL失败: draftId={}", draft.getId(), e);
            return "";
        }
    }

    /**
     * 获取草稿信息
     */
    private DraftInfo getDraftInfo(String cv) {
        try {
            SitePageConfigDraftEntity draft = draftEngine.getById(cv);
            if (draft == null) {
                log.warn("未找到草稿: cv={}", cv);
                return new DraftInfo(false, null, null);
            }

            // 判断草稿类型是否为专题页内容类型
            boolean isTopicPage = draft.getType() != null && draft.getType().equals(DraftType.TOPIC_PAGE_CONTENT.getCode());
            log.debug("草稿类型判断: cv={}, type={}, pageId={}, isTopicPage={}", cv, draft.getType(), draft.getPageId(), isTopicPage);

            return new DraftInfo(isTopicPage, draft.getPageId(), draft);
        } catch (NumberFormatException e) {
            log.error("无效的草稿ID格式: {}", cv, e);
            return new DraftInfo(false, null, null);
        } catch (Exception e) {
            log.error("查询草稿失败: cv={}, error={}", cv, e.getMessage(), e);
            return new DraftInfo(false, null, null);
        }
    }

    /**
     * 草稿信息封装类
     */
    private static class DraftInfo {
        private final boolean isTopicPage;
        private final String pageId;
        private final SitePageConfigDraftEntity draft;

        public DraftInfo(boolean isTopicPage, String pageId, SitePageConfigDraftEntity draft) {
            this.isTopicPage = isTopicPage;
            this.pageId = pageId;
            this.draft = draft;
        }

        public boolean isTopicPage() {
            return isTopicPage;
        }

        public String getPageId() {
            return pageId;
        }

        public SitePageConfigDraftEntity getDraft() {
            return draft;
        }
    }
}
