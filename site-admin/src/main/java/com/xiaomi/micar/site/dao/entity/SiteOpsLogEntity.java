package com.xiaomi.micar.site.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户操作审计
 *
 * <AUTHOR>
 * @since 2025/05/27
 */


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "site_ops_log", keepGlobalPrefix = true)
public class SiteOpsLogEntity {

    /**
     * 操作时间戳
     */
    private Long ts;

    /**
     * 操作
     * {@link com.xiaomi.micar.site.enums.OperationType}
     */
    private String operation;

    private String pageId;

    private String moduleId;

    private String draftId;

    private String releaseId;

    private String remark;

    private String content;

    private String operatorId;

    private String operatorName;


}
