package com.xiaomi.micar.site.listener;


import com.xiaomi.micar.site.event.ApprovalCompletedEvent;
import com.xiaomi.micar.site.service.robot.RobotNotifyService;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 审批完成事件监听器
 * 异步处理审批完成后的通知，上报最终执行结果
 *
 * <AUTHOR>
 * @since 2025/07/17
 */
@Component
@Slf4j
public class ApprovalCompletedEventListener {

    @Resource
    private RobotNotifyService robotNotifyService;

    /**
     * 审批完成事件监听，发送飞书通知上报最终执行结果
     */
    @EventListener(ApprovalCompletedEvent.class)
    @Async("robotNotifyExecutor")
    public void handleApprovalCompletedEvent(ApprovalCompletedEvent event) {
        try {
            log.info("收到审批完成事件: draftId={}, operator={}, resultType={}, resultMessage={}, timestamp={}",
                    event.getDraftId(), 
                    event.getOperator() != null ? event.getOperator() : "未知",
                    event.getResultType() != null ? event.getResultType().getDesc() : "未知",
                    event.getResultMessage() != null ? event.getResultMessage() : "",
                    event.getEventTimestamp());
            // 获取页面名称和模块名称
            String pageName = getPageName(event);
            String moduleName = getModuleName(event);
            String operationType = getOperationType(event);

            log.info("审批通知信息: draftId={}, pageName={}, moduleName={}, operationType={}",
                    event.getDraftId(), pageName, moduleName, operationType);

            // 发送最终执行结果通知
            boolean notifyResult = robotNotifyService.sendApprovalResultNotify(
                event.getDraft(),
                event.getOperator(),
                event.getResultType().getDesc(),
                event.getResultMessage(),
                event.getComment(),
                pageName,
                moduleName,
                operationType
            );

            if (notifyResult) {
                log.info("审批结果通知发送成功: draftId={}, operator={}, result={}",
                        event.getDraftId(), 
                        event.getOperator() != null ? event.getOperator() : "未知",
                        event.getResultType() != null ? event.getResultType().getDesc() : "未知");
            } else {
                log.warn("审批结果通知发送失败: draftId={}, operator={}, result={}",
                        event.getDraftId(), 
                        event.getOperator() != null ? event.getOperator() : "未知",
                        event.getResultType() != null ? event.getResultType().getDesc() : "未知");
            }

        } catch (Exception e) {
            log.error("处理审批完成事件异常: event={}", JsonUtil.toJSONString(event), e);
        }
    }
    /**
     * 获取页面名称
     */
    private String getPageName(ApprovalCompletedEvent event) {
        try {
            if (event.getDraft() != null && event.getDraft().getPageId() != null) {
                // 这里可以根据pageId查询页面名称，暂时返回pageId
                return event.getDraft().getPageId();
            }
            return "未知页面";
        } catch (Exception e) {
            log.warn("获取页面名称失败: draftId={}", event.getDraftId(), e);
            return "未知页面";
        }
    }

    /**
     * 获取模块名称
     */
    private String getModuleName(ApprovalCompletedEvent event) {
        try {
            if (event.getDiffContext() != null && event.getDiffContext().getModuleName() != null) {
                return event.getDiffContext().getModuleName();
            }
            return "未知模块";
        } catch (Exception e) {
            log.warn("获取模块名称失败: draftId={}", event.getDraftId(), e);
            return "未知模块";
        }
    }

    /**
     * 获取操作类型
     */
    private String getOperationType(ApprovalCompletedEvent event) {
        try {
            if (event.getDiffContext() != null && event.getDiffContext().getOperationType() != null) {
                return event.getDiffContext().getOperationType();
            }
            return "未知操作";
        } catch (Exception e) {
            log.warn("获取操作类型失败: draftId={}", event.getDraftId(), e);
            return "未知操作";
        }
    }

}
