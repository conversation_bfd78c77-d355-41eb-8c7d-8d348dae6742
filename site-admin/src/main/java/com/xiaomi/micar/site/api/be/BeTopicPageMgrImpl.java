package com.xiaomi.micar.site.api.be;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiaomi.micar.site.api.be.aop.BeLoginCheck;
import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.api.model.TopicPageListResp;
import com.xiaomi.micar.site.api.model.TopicPageSaveReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftDetailReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftDeleteReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftListReq;
import com.xiaomi.micar.site.config.FrontendUrlConfig;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.micar.site.model.UserInfo;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageInfoEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.enums.BpmOperationType;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.LoadTypeEnum;
import com.xiaomi.micar.site.enums.PageTypeEnum;
import com.xiaomi.micar.site.enums.StrategyStateEnum;


import com.xiaomi.micar.site.service.TopicPageService;
import com.xiaomi.micar.site.service.BpmInternalService;
import com.xiaomi.micar.site.convert.TransformUtil;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.xiaomi.micar.site.enums.DraftType.TOPIC_PAGE_CONTENT;

/**
 * 专题页管理接口实现
 */
@Slf4j
@ApiModule(value = "后台管理#专题页管理接口", apiInterface = BeTopicPageMgr.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class BeTopicPageMgrImpl implements BeTopicPageMgr {

    @Resource
    private TopicPageService topicPageService;

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Resource
    private SitePageInfoEngine pageInfoEngine;

    @Resource
    private FrontendUrlConfig frontendUrlConfig;

    @Resource
    private BpmInternalService bmpInternalService;

    @ApiDoc(value = "获取所有专题页列表")
//    @BeLoginCheck
    @Override
    public Result<TopicPageListResp> getAllTopicPages() {
        log.info("接收到获取所有专题页列表的请求");

        List<SitePageInfoEntity> topicPages = topicPageService.getAllTopicPages();
        TopicPageListResp response = new TopicPageListResp(topicPages);

        log.info("返回专题页列表，共 {} 个专题页", response.getTotal());
        return Result.success(response);
    }

    @ApiDoc(value = "获取有草稿的专题页草稿列表")
    @BeLoginCheck
    @Override
    public Result<CommonPagedData<RecordDTO>> getTopicPagesWithDrafts(TopicPageDraftListReq req) {
        log.info("接收到获取有草稿的专题页草稿列表的请求: pageId={}, status={}, name={}, page={}, size={}",
                req.getPageId(), req.getStatus(), req.getName(), req.getPage(), req.getSize());

        // 使用数据库分页查询专题页的草稿列表
        IPage<SitePageConfigDraftEntity> pageResult = topicPageService.getTopicPageDraftsWithPagination(
                req.getPageId(),
                req.getName(),
                req.getStatus(),
                req.getPage(),
                req.getSize()
        );

        // 转换为DTO，包含 C 端页面链接
        List<RecordDTO> draftDTOs = pageResult.getRecords().stream()
                .map(draft -> TransformUtil.draftToDTO(draft, frontendUrlConfig))
                .collect(Collectors.toList());

        // 包装为 CommonPagedData 格式
        CommonPagedData<RecordDTO> pagedData = new CommonPagedData<>();
        pagedData.setRecords(draftDTOs);
        pagedData.setTotal(pageResult.getTotal());

        log.info("返回有草稿的专题页草稿列表，共 {} 个草稿，当前页 {} 条",
                pageResult.getTotal(), draftDTOs.size());
        return Result.success(pagedData);
    }

    @ApiDoc(value = "保存专题页配置")
    @BeLoginCheck
    @Override
    public Result<Void> saveTopicPage(TopicPageSaveReq req) {
        try {
            log.info("保存专题页配置: pageId={}, name={}", req.getPageId(), req.getName());

            // 参数校验
            if (StringUtils.isBlank(req.getConfig())) {
                return Result.fail(GeneralCodes.ParamError, "配置内容不能为空");
            }

            // 获取操作人信息
            String operatorId = req.getUserInfo().getAccount();
            String operatorName = req.getUserInfo().getUserName();

            SitePageConfigDraftEntity draftEntity;
            boolean isModifyingDeployedPage = false;

            if (req.getId() != null) {
                // 编辑现有草稿
                draftEntity = draftEngine.getById(req.getId());
                if (draftEntity == null) {
                    return Result.fail(GeneralCodes.ParamError, "要编辑的草稿不存在");
                }

                // 检查是否是修改已上线的草稿
                if (ConfigStatus.DEPLOYED.getCode() == draftEntity.getStatus()) {
                    isModifyingDeployedPage = true;
                }

                // 更新草稿内容
                draftEntity.setConfig(req.getConfig());
                draftEntity.setName(StringUtils.isNotBlank(req.getName()) ? req.getName() : draftEntity.getName());
                draftEntity.setRemark(StringUtils.isNotBlank(req.getRemark()) ? req.getRemark() : draftEntity.getRemark());
                draftEntity.setStatus(ConfigStatus.DRAFT.getCode());
                draftEntity.setOperatorId(operatorId);
                draftEntity.setOperatorName(operatorName);
            } else {
                // 新建草稿
                draftEntity = new SitePageConfigDraftEntity();
                draftEntity.setType(TOPIC_PAGE_CONTENT.getCode());
                draftEntity.setModuleId(0);
                draftEntity.setConfig(req.getConfig());
                draftEntity.setName(StringUtils.isNotBlank(req.getName()) ? req.getName() : "新建专题页");
                draftEntity.setRemark(StringUtils.isNotBlank(req.getRemark()) ? req.getRemark() : "专题页配置保存");
                draftEntity.setStatus(ConfigStatus.DRAFT.getCode());
                draftEntity.setOperatorId(operatorId);
                draftEntity.setOperatorName(operatorName);
                draftEntity.setPageId("temp"); // 临时占位，保存后会更新
            }

            // 保存草稿
            draftEngine.saveOrUpdate(draftEntity);


            // 新建时需要生成 pageId
            if (req.getId() == null) {
                String pageId = generatePageIdAndCreatePageInfo(req.getName());
                draftEntity.setPageId(pageId);
                draftEntity.setName(StringUtils.isNotBlank(req.getName()) ? req.getName() : "专题页配置-" + pageId);
                draftEngine.updateById(draftEntity);
                log.info("新建专题页配置保存成功: pageId={}, draftId={}", pageId, draftEntity.getId());
            } else {
                log.info("编辑专题页配置保存成功: draftId={}, pageId={}, isModifyingDeployed={}",
                        draftEntity.getId(), draftEntity.getPageId(), isModifyingDeployedPage);
            }

            // 如果是修改已上线的页面，自动提交审核
            if (isModifyingDeployedPage && draftEntity.getId() != null) {
                autoSubmitBmpApproval(draftEntity.getId(), req.getUserInfo());
            }

            return Result.success(null);

        } catch (Exception e) {
            log.error("保存专题页配置异常: pageId={}", req.getPageId(), e);
            return Result.fail(GeneralCodes.InternalError, "保存专题页配置失败: " + e.getMessage());
        }
    }

    /**
     * 自动提交BMP审核
     * 当修改已上线的专题页时自动调用
     * 注意：此方法会抛出异常，确保事务回滚
     */
    private void autoSubmitBmpApproval(Long draftId, UserInfo userInfo) {
        log.info("开始自动提交BMP审核: draftId={}, user={}", draftId, userInfo.getAccount());

        // 调用BMP服务提交修改审核
        Result<Void> result = bmpInternalService.submitBpmApproval(
                draftId,
                BpmOperationType.MODIFY.getCode(), // 修改审核
                "系统自动提交：修改已上线专题页配置", // 自动生成的备注
                userInfo
        );

        if (result.getCode() == 0) {
            log.info("自动提交BMP审核成功: draftId={}, user={}", draftId, userInfo.getAccount());
        } else {
            String errorMsg = String.format("自动提交BMP审核失败: draftId=%d, user=%s, error=%s",
                    draftId, userInfo.getAccount(), result.getMessage());
            log.error(errorMsg);
            // 抛出异常，触发事务回滚
            throw new RuntimeException(errorMsg);
        }
    }


    @ApiDoc(value = "根据草稿ID获取专题页详情")
    @BeLoginCheck
    @Override
    public Result<RecordDTO> getDraftDetail(TopicPageDraftDetailReq req) {
        try {
            log.info("获取专题页草稿详情: id={}, pageId={}", req.getId(), req.getPageId());

            // 参数校验
            if (req.getId() == null) {
                return Result.fail(GeneralCodes.ParamError, "草稿ID不能为空");
            }
//            if (StringUtils.isBlank(req.getPageId())) {
//                return Result.fail(GeneralCodes.ParamError, "页面ID不能为空");
//            }

            // 查询草稿详情
            SitePageConfigDraftEntity draftEntity = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getId, req.getId())
//                    .eq(SitePageConfigDraftEntity::getPageId, req.getPageId())
                    .eq(SitePageConfigDraftEntity::getType, 1)
                    .one();

            if (draftEntity == null) {
                return Result.fail(GeneralCodes.NotFound, "未找到指定的专题页草稿");
            }

            // 转换为DTO
            RecordDTO recordDTO = TransformUtil.draftToDTO(draftEntity);

            log.info("专题页草稿详情获取成功: id={}, pageId={}, name={}",
                    req.getId(), req.getPageId(), recordDTO.getName());
            return Result.success(recordDTO);

        } catch (Exception e) {
            log.error("获取专题页草稿详情异常: id={}, pageId={}", req.getId(), req.getPageId(), e);
            return Result.fail(GeneralCodes.InternalError, "获取专题页草稿详情失败: " + e.getMessage());
        }
    }

    @ApiDoc(value = "删除专题页草稿")
    @BeLoginCheck
    @Override
    public Result<Void> deleteDraft(TopicPageDraftDeleteReq req) {
        try {
            log.info("删除专题页草稿: id={}, pageId={}, deleteRemark={}",
                    req.getId(), req.getPageId(), req.getDeleteRemark());

            // 参数校验
            if (req.getId() == null) {
                return Result.fail(GeneralCodes.ParamError, "草稿ID不能为空");
            }
            if (StringUtils.isBlank(req.getPageId())) {
                return Result.fail(GeneralCodes.ParamError, "页面ID不能为空");
            }

            // 检查草稿是否存在
            SitePageConfigDraftEntity existingDraft = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getId, req.getId())
                    .eq(SitePageConfigDraftEntity::getPageId, req.getPageId())
                    .eq(SitePageConfigDraftEntity::getType, 1)
                    .one();

            if (existingDraft == null) {
                return Result.fail(GeneralCodes.NotFound, "未找到指定的专题页草稿");
            }

            // 检查草稿状态，只能删除草稿状态的记录
            if (existingDraft.getStatus() == null || !existingDraft.getStatus().equals(ConfigStatus.DRAFT.getCode())) {
                return Result.fail(GeneralCodes.ParamError, "只能删除草稿状态的记录");
            }

            // 执行删除
            boolean deleted = draftEngine.removeById(req.getId());
            if (!deleted) {
                return Result.fail(GeneralCodes.InternalError, "删除草稿失败");
            }

            log.info("专题页草稿删除成功: id={}, pageId={}, name={}",
                    req.getId(), req.getPageId(), existingDraft.getName());
            return Result.success(null);

        } catch (Exception e) {
            log.error("删除专题页草稿异常: id={}, pageId={}", req.getId(), req.getPageId(), e);
            return Result.fail(GeneralCodes.InternalError, "删除专题页草稿失败: " + e.getMessage());
        }
    }

    /**
     * 生成 pageId 并创建 page_info 记录
     *
     * @param pageName 页面名称
     * @return 生成的 pageId
     */
    private String generatePageIdAndCreatePageInfo(String pageName) {
        try {
            // 创建 page_info 记录
            SitePageInfoEntity pageInfo = new SitePageInfoEntity();
            pageInfo.setPageName(StringUtils.isNotBlank(pageName) ? pageName : "专题页");
            pageInfo.setPageType(PageTypeEnum.TOPIC_PAGE.getCode()); // 专题页
            pageInfo.setLoadType(LoadTypeEnum.PAGE_DIRECT.getCode()); // 从page直接加载
            pageInfo.setStrategyState(StrategyStateEnum.DISABLED.getCode()); // 专题页默认关闭人群策略
            pageInfo.setStatus(0); // 0=未上线
            pageInfo.setVersion(1); // 默认版本
            pageInfo.setPageId("temp_" + System.currentTimeMillis()); // 临时 pageId，避免 NOT NULL 约束
            pageInfo.setCreateTime(new Date());
            pageInfo.setUpdateTime(new Date());
            // 保存到数据库，获取主键ID
            pageInfoEngine.save(pageInfo);

            // 根据主键ID生成正式的 pageId
            String pageId = "topic_" + pageInfo.getId();

            // 更新为正式的 pageId
            pageInfo.setPageId(pageId);
            pageInfoEngine.updateById(pageInfo);

            log.info("创建专题页 page_info 记录成功: pageId={}, id={}, status=0", pageId, pageInfo.getId());
            return pageId;

        } catch (Exception e) {
            log.error("生成 pageId 并创建 page_info 记录失败", e);
            throw new RuntimeException("生成 pageId 失败: " + e.getMessage());
        }
    }
}