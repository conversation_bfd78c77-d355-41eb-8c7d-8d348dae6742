package com.xiaomi.micar.site.validator;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 验证错误详情
 * 用于封装具体的验证错误信息
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValidationError {
    
    /**
     * 错误字段路径
     */
    private String path;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 错误类型
     */
    private String type;
    
    /**
     * 获取友好的字段名称
     */
    public String getFriendlyFieldName() {
        if (path == null || path.isEmpty()) {
            return "根节点";
        }
        
        // 移除开头的 $ 符号
        String cleanPath = path.startsWith("$.") ? path.substring(2) : path;
        
        // 将路径转换为更友好的中文描述
        return cleanPath.replace(".", " -> ");
    }
    
    /**
     * 获取友好的错误消息
     */
    public String getFriendlyMessage() {
        if (message == null) {
            return "未知错误";
        }
        
        // 将常见的英文错误消息转换为中文
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("is missing but it is required")) {
            return "此字段为必填项，不能为空";
        }
        
        if (lowerMessage.contains("does not match")) {
            return "格式不正确";
        }
        
        if (lowerMessage.contains("is not a valid")) {
            return "值无效";
        }
        
        if (lowerMessage.contains("minimum")) {
            return "值过小";
        }
        
        if (lowerMessage.contains("maximum")) {
            return "值过大";
        }
        
        if (lowerMessage.contains("minlength")) {
            return "长度过短";
        }
        
        if (lowerMessage.contains("maxlength")) {
            return "长度过长";
        }
        
        if (lowerMessage.contains("type")) {
            if (lowerMessage.contains("string")) {
                return "应为文本类型";
            }
            if (lowerMessage.contains("number") || lowerMessage.contains("integer")) {
                return "应为数字类型";
            }
            if (lowerMessage.contains("boolean")) {
                return "应为布尔类型";
            }
            if (lowerMessage.contains("array")) {
                return "应为数组类型";
            }
            if (lowerMessage.contains("object")) {
                return "应为对象类型";
            }
            return "数据类型不正确";
        }
        
        if (lowerMessage.contains("enum")) {
            return "值不在允许的选项中";
        }
        
        if (lowerMessage.contains("format")) {
            return "格式不正确";
        }
        
        if (lowerMessage.contains("additional properties")) {
            return "包含不允许的额外字段";
        }
        
        // 如果没有匹配的模式，返回原始消息
        return message;
    }
    
    /**
     * 获取完整的友好描述
     */
    public String getFullFriendlyDescription() {
        return String.format("字段 '%s': %s", getFriendlyFieldName(), getFriendlyMessage());
    }
    
    /**
     * 构造函数，自动处理空的type
     */
    public ValidationError(String path, String message) {
        this.path = path;
        this.message = message;
        this.type = "validation";
    }
    
    // 手动添加getter方法以确保编译通过
    public String getPath() {
        return path;
    }
    
    public String getMessage() {
        return message;
    }
    
    public String getType() {
        return type;
    }
}