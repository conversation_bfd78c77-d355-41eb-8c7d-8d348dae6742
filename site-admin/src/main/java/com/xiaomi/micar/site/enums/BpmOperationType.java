package com.xiaomi.micar.site.enums;

import lombok.Getter;

/**
 * BPM操作类型枚举
 *
 * <AUTHOR>
 * @since 2025/04/15
 */
@Getter
public enum BpmOperationType {
    /**
     * 修改审核
     */
    MODIFY(1, "修改审核"),

    /**
     * 下线审核
     */
    OFFLINE(2, "下线审核"),

    /**
     * 页面调整审核
     */
    PAGE_ADJUST(3, "页面调整审核");

    private final Integer code;
    private final String desc;

    BpmOperationType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static BpmOperationType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BpmOperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
