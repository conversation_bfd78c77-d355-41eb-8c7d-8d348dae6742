package com.xiaomi.micar.site.service.callback;

import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.event.DiffContext;
import com.xiaomi.micar.site.model.bpm.Operator;

/**
 * 草稿部署处理器接口
 * 定义不同类型草稿的部署处理标准
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
public interface DraftDeployHandler {

    /**
     * 判断是否支持该类型的草稿
     *
     * @param draftType 草稿类型
     * @return 是否支持
     */
    boolean supports(Integer draftType);

    /**
     * 部署草稿
     *
     * @param draft 草稿实体
     * @param isOfflineAudit 是否为下线审核
     * @param operator 操作人
     * @param comment 审批意见
     * @return 配置差异上下文（null表示不需要生成diff）
     */
    DiffContext deploy(SitePageConfigDraftEntity draft, boolean isOfflineAudit, Operator operator, String comment);

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getHandlerName();
}
