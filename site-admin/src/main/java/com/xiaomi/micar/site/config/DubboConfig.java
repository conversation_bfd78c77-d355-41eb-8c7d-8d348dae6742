package com.xiaomi.micar.site.config;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.RegistryConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class DubboConfig {

    @Value("${dubbo.registry.address}")
    private String carlifeRegAddress;
    @Value("${dubbo.mdcd.registry.address}")
    private String mdcdRegAddress;
    @Value("${dubbo.mdcd.registry.group:}")
    private String mdcdGroup;


    @Bean
    public RegistryConfig carlifeRegistry() {
        RegistryConfig config = new RegistryConfig();
        config.setAddress(carlifeRegAddress);
        return config;
    }

    @Bean
    public RegistryConfig mdcdRegistry() {
        RegistryConfig config = new RegistryConfig();
        config.setAddress(mdcdRegAddress);
        // Registry group
        if (StringUtils.isNotEmpty(mdcdGroup)) {
            config.setGroup(mdcdGroup);
        }
        return config;
    }
}
