package com.xiaomi.micar.site.service;

import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * TopicPageService 测试类
 */
@Slf4j
@SpringBootTest
public class TopicPageServiceTest {

    @Resource
    private TopicPageService topicPageService;

    @Test
    public void testGetTopicPageDrafts() {
        log.info("开始测试 getTopicPageDrafts 方法");
        
        // 测试查询所有专题页草稿
        List<SitePageConfigDraftEntity> allDrafts = topicPageService.getTopicPageDrafts(null);
        log.info("查询所有专题页草稿结果: 共 {} 个草稿", allDrafts.size());
        
        for (SitePageConfigDraftEntity draft : allDrafts) {
            log.info("草稿信息: id={}, pageId={}, name={}, type={}, moduleId={}, status={}, createTime={}", 
                    draft.getId(), draft.getPageId(), draft.getName(), 
                    draft.getType(), draft.getModuleId(), draft.getStatus(), draft.getCreateTime());
        }
        
        log.info("测试完成");
    }
}
