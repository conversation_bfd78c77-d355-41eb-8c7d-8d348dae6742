package com.xiaomi.micar.site.service.robot;

import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 飞书通知测试
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RobotNotifyServiceTest {

    @Resource
    private RobotNotifyService robotNotifyService;

    @Test
    public void testSendApprovalResultNotify() {
        try {
            // 创建测试草稿
            SitePageConfigDraftEntity draft = new SitePageConfigDraftEntity();
            draft.setId(123L);
            draft.setType(2);
            draft.setPageId("homepage");
            draft.setConfig("{\"banners\":[{\"title\":\"测试轮播图\"}]}");

            // 测试参数
            String operator = "张三";
            String resultType = "审批通过";
            String resultMessage = "配置审批已通过";
            String comment = "配置内容符合规范，同意上线";
            String pageName = "首页";
            String moduleName = "轮播图模块";
            String operationType = "修改";

            System.out.println("=== 开始测试飞书卡片发送 ===");
            System.out.println("操作人员: " + operator);
            System.out.println("页面名称: " + pageName);
            System.out.println("模块名称: " + moduleName);
            System.out.println("操作类型: " + operationType);
            System.out.println("审批结果: " + resultType);
            System.out.println("审批备注: " + comment);

            // 直接调用方法
            boolean result = robotNotifyService.sendApprovalResultNotify(
                draft,
                operator,
                resultType,
                resultMessage,
                comment,
                pageName,
                moduleName,
                operationType
            );

            System.out.println("=== 发送结果 ===");
            if (result) {
                System.out.println("✅ 飞书卡片发送成功！");
                System.out.println("请检查飞书群聊是否收到消息");
            } else {
                System.out.println("❌ 飞书卡片发送失败！");
                System.out.println("请检查日志查看具体错误信息");
            }

        } catch (Exception e) {
            System.out.println("❌ 测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
