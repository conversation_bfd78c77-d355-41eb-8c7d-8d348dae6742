package com.xiaomi.micar.site.dao;

import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 时间字段测试
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TimeFieldTest {

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Test
    public void testTimeFieldsIgnored() {
        log.info("开始测试时间字段是否被正确忽略");
        
        // 创建一个新的草稿实体
        SitePageConfigDraftEntity draft = new SitePageConfigDraftEntity();
        draft.setType(1); // DraftType.TOPIC_PAGE_CONTENT.getCode()
        draft.setPageId("test_page_" + System.currentTimeMillis());
        draft.setModuleId(0);
        draft.setName("测试时间字段");
        draft.setConfig("{\"test\": true}");
        draft.setRemark("测试时间字段是否被数据库自动管理");
        draft.setStatus(ConfigStatus.DRAFT.getCode());
        draft.setOperatorId("test_user");
        draft.setOperatorName("测试用户");
        
        // 注意：我们没有设置 createTime 和 updateTime
        log.info("保存前的实体: createTime={}, updateTime={}", draft.getCreateTime(), draft.getUpdateTime());
        
        // 保存实体
        boolean saved = draftEngine.save(draft);
        log.info("保存结果: {}", saved);
        
        if (saved) {
            // 重新查询实体，查看时间字段是否被数据库自动设置
            SitePageConfigDraftEntity savedDraft = draftEngine.getById(draft.getId());
            log.info("保存后的实体: id={}, createTime={}, updateTime={}", 
                    savedDraft.getId(), savedDraft.getCreateTime(), savedDraft.getUpdateTime());
            
            // 等待一秒，然后更新实体
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 更新实体
            savedDraft.setRemark("更新后的备注");
            boolean updated = draftEngine.updateById(savedDraft);
            log.info("更新结果: {}", updated);
            
            if (updated) {
                // 重新查询，查看 updateTime 是否被自动更新
                SitePageConfigDraftEntity updatedDraft = draftEngine.getById(draft.getId());
                log.info("更新后的实体: id={}, createTime={}, updateTime={}", 
                        updatedDraft.getId(), updatedDraft.getCreateTime(), updatedDraft.getUpdateTime());
            }
        }
    }
}
