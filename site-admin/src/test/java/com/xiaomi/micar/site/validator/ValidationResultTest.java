package com.xiaomi.micar.site.validator;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证结果详细错误信息测试
 *
 * <AUTHOR>
 * @since 2025/01/21
 */
@SpringBootTest
public class ValidationResultTest {

    @Autowired
    private TemplateSchemaValidator validator;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testValidationResultSuccess() {
        ValidationResult result = ValidationResult.success();
        assertTrue(result.isValid());
        assertFalse(result.hasErrors());
        assertEquals(0, result.getErrorCount());
        assertNull(result.getFormattedErrorMessage());
    }

    @Test
    public void testValidationResultFailure() {
        ValidationResult result = ValidationResult.failure("$.data[0].name", "该字段为必填项");
        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertEquals(1, result.getErrorCount());
        assertNotNull(result.getFormattedErrorMessage());
        
        System.out.println("错误摘要: " + result.getErrorSummary());
        System.out.println("格式化错误信息: " + result.getFormattedErrorMessage());
    }

    @Test
    public void testValidationErrorFriendlyMessage() {
        ValidationError error = new ValidationError("$.data[0].name", "该字段为必填项", "required");
        
        System.out.println("原始路径: " + error.getPath());
        System.out.println("友好字段名: " + error.getFriendlyFieldName());
        System.out.println("友好错误信息: " + error.getFriendlyMessage());
        System.out.println("完整描述: " + error.getFullFriendlyDescription());
        
        assertEquals("data -> [第0项] -> name", error.getFriendlyFieldName());
    }

    @Test
    public void testDetailedValidationWithInvalidConfig() {
        System.out.println("=== 测试详细验证错误信息 ===");
        
        // 测试无效的横幅配置
        String invalidConfig = "{\n" +
            "  \"data\": [\n" +
            "    {\n" +
            "      \"id\": \"test1\",\n" +
            "      \"name\": \"\",\n" +  // 空字符串，可能违反minLength
            "      \"type\": \"invalid_type\",\n" +  // 无效的枚举值
            "      \"image\": {\n" +
            "        \"src\": \"not_a_url\"\n" +  // 无效的URL格式
            "      }\n" +
            "    }\n" +
            "  ]\n" +
            "}";

        ValidationResult result = validator.validateConfigWithDetails("banner", invalidConfig);
        
        System.out.println("验证结果: " + (result.isValid() ? "✅ 通过" : "❌ 失败"));
        
        if (!result.isValid()) {
            System.out.println("错误数量: " + result.getErrorCount());
            System.out.println("错误摘要: " + result.getErrorSummary());
            System.out.println("\n详细错误信息:");
            System.out.println(result.getFormattedErrorMessage());
            
            System.out.println("\n原始错误列表:");
            for (int i = 0; i < result.getErrors().size(); i++) {
                ValidationError error = result.getErrors().get(i);
                System.out.println(String.format("%d. 路径: %s, 消息: %s, 类型: %s", 
                    i + 1, error.getPath(), error.getMessage(), error.getType()));
                System.out.println("   友好描述: " + error.getFullFriendlyDescription());
            }
        }
        
        // 验证应该失败
        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
    }

    @Test
    public void testDetailedValidationWithEmptyConfig() {
        System.out.println("=== 测试空配置的详细验证 ===");
        
        ValidationResult result = validator.validateConfigWithDetails("banner", "");
        
        System.out.println("验证结果: " + (result.isValid() ? "✅ 通过" : "❌ 失败"));
        
        if (!result.isValid()) {
            System.out.println("错误信息: " + result.getFormattedErrorMessage());
        }
        
        assertFalse(result.isValid());
        assertEquals("配置内容不能为空", result.getErrors().get(0).getMessage());
    }

    @Test
    public void testDetailedValidationWithInvalidJson() {
        System.out.println("=== 测试无效JSON的详细验证 ===");
        
        String invalidJson = "{\"data\": [}";
        ValidationResult result = validator.validateConfigWithDetails("banner", invalidJson);
        
        System.out.println("验证结果: " + (result.isValid() ? "✅ 通过" : "❌ 失败"));
        
        if (!result.isValid()) {
            System.out.println("错误信息: " + result.getFormattedErrorMessage());
        }
        
        assertFalse(result.isValid());
        assertTrue(result.getErrors().get(0).getMessage().contains("JSON格式错误"));
    }

    @Test
    public void testDetailedValidationWithValidConfig() {
        System.out.println("=== 测试有效配置的详细验证 ===");
        
        String validConfig = "{\"data\": [{\"id\": \"test1\", \"name\": \"测试横幅\", \"type\": \"image\", \"image\": {\"src\": \"https://example.com/image.jpg\"}}]}";
        
        ValidationResult result = validator.validateConfigWithDetails("banner", validConfig);
        
        System.out.println("验证结果: " + (result.isValid() ? "✅ 通过" : "❌ 失败"));
        
        if (result.isValid()) {
            System.out.println("配置验证通过，无错误信息");
        } else {
            System.out.println("意外的验证失败: " + result.getFormattedErrorMessage());
        }
        
        assertTrue(result.isValid());
        assertFalse(result.hasErrors());
    }
}