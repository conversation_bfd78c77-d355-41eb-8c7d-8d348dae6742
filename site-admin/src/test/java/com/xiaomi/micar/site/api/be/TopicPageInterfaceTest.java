package com.xiaomi.micar.site.api.be;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.api.dto.RecordDTO;
import com.xiaomi.micar.site.api.model.TopicPageListResp;
import com.xiaomi.micar.site.api.model.TopicPageSaveReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftDetailReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftDeleteReq;
import com.xiaomi.micar.site.api.model.TopicPageDraftListReq;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.enums.BpmOperationType;
import com.xiaomi.micar.site.enums.ProcessInstanceStatusEnum;
import com.xiaomi.micar.site.filter.ResultUtils;
import com.xiaomi.micar.site.model.CommonPagedData;
import com.xiaomi.micar.site.model.UserInfo;
import com.xiaomi.micar.site.model.bpm.BpmCallbackDTO;
import com.xiaomi.micar.site.model.bpm.BpmCallbackResponse;
import com.xiaomi.micar.site.model.bpm.Operator;
import com.xiaomi.micar.site.service.BpmCallbackService;
import com.xiaomi.micar.site.service.BpmInternalService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest
public class TopicPageInterfaceTest {

    @Resource
    private BeTopicPageMgr beTopicPageMgr;
    
    @Resource
    private BpmInternalService bpmInternalService;
    
    @Resource
    private BpmCallbackService bpmCallbackService;
    
    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Resource
    private BpmInternalService bmpInternalService;

    @Resource
    private BpmCallbackService bmpCallbackService;

    private static final String CONFIG = "{\"pageInfo\": {\"desc\": \"专题简介\", \"name\": \"专题名称\", \"image\": {\"src\": \"普通机型封面图URL\", \"width\": 750, \"height\": 400}, \"imageFold\": {\"src\": \"折叠屏封面图URL\", \"width\": 1080, \"height\": 400}}, \"components\": [{\"id\": \"1\", \"title\": \"精选内容\", \"config\": \"{\\\"data\\\":[]}\", \"template\": \"verticalCard\", \"titleTag\": \"模块标签\", \"component\": \"News\", \"dataProvider\": \"newsProvider\", \"dataProviderParams\": \"{\\\"ids\\\":[\\\"wy_YU7\\\",\\\"wy_Ultra\\\",\\\"官方资讯\\\"],\\\"type\\\":\\\"tag\\\",\\\"limit\\\":10,\\\"loadMore\\\":false,\\\"pageSize\\\":10}\"}, {\"id\": \"2\", \"title\": \"热门推荐\", \"config\": \"{\\\"data\\\":[]}\", \"template\": \"fullWidthCard\", \"titleTag\": \"模块标签\", \"component\": \"News\", \"dataProvider\": \"newsProvider\", \"dataProviderParams\": \"{\\\"ids\\\":[\\\"wy_YU7\\\",\\\"wy_Ultra\\\",\\\"官方资讯\\\"],\\\"type\\\":\\\"tag\\\",\\\"limit\\\":10,\\\"loadMore\\\":false,\\\"pageSize\\\":10}\"}, {\"id\": \"3\", \"title\": \"分类内容\", \"config\": \"{\\\"items\\\":[{\\\"text\\\":\\\"分类1\\\",\\\"tabId\\\":\\\"category1\\\",\\\"content\\\":{\\\"data\\\":[]},\\\"selected\\\":true}]}\", \"template\": \"tabScrollList\", \"titleTag\": \"模块标签\", \"component\": \"TabNews\"}, {\"id\": \"4\", \"title\": \"分类推荐\", \"config\": \"{\\\"items\\\":[{\\\"text\\\":\\\"分类1\\\",\\\"tabId\\\":\\\"category1\\\",\\\"content\\\":{\\\"data\\\":[]},\\\"selected\\\":true,\\\"dataProviderParams\\\":{\\\"ids\\\":[\\\"wy_YU7\\\",\\\"wy_Ultra\\\",\\\"官方资讯\\\"],\\\"sort\\\":\\\"time\\\",\\\"type\\\":\\\"tag\\\",\\\"limit\\\":50,\\\"loadMore\\\":true,\\\"pageSize\\\":10}}]}\", \"template\": \"tabScrollList\", \"titleTag\": \"模块标签\", \"component\": \"TabNews\", \"dataProvider\": \"tabNewsProvider\"}]}";

    @Test
    public void testGetAllTopicPages() {
        Result<TopicPageListResp> result = beTopicPageMgr.getAllTopicPages();
        log.info("getAllTopicPages result: {}", JsonUtil.toJSONString(result));
    }

    @Test
    public void testGetTopicPagesWithDrafts() {
        TopicPageDraftListReq req = new TopicPageDraftListReq();
        req.setPage(1L);
        req.setSize(10L);

        Result<CommonPagedData<RecordDTO>> result = beTopicPageMgr.getTopicPagesWithDrafts(req);
        log.info("getTopicPagesWithDrafts result: {}", JsonUtil.toJSONString(result));
    }

    @Test
    public void testGetTopicPagesWithDraftsWithNameSearch() {
        TopicPageDraftListReq req = new TopicPageDraftListReq();
        req.setPage(1L);
        req.setSize(10L);
        req.setName("测试"); // 测试名称搜索功能

        Result<CommonPagedData<RecordDTO>> result = beTopicPageMgr.getTopicPagesWithDrafts(req);
        log.info("getTopicPagesWithDrafts with name search result: {}", JsonUtil.toJSONString(result));
    }

    @Test
    public void testDatabasePagination() {
        // 测试数据库分页功能
        TopicPageDraftListReq req = new TopicPageDraftListReq();
        req.setPage(1L);
        req.setSize(5L); // 小页面大小，便于测试分页

        Result<CommonPagedData<RecordDTO>> result = beTopicPageMgr.getTopicPagesWithDrafts(req);
        log.info("数据库分页测试结果: {}", JsonUtil.toJSONString(result));

        if (result.getCode() == 200 && result.getData() != null) {
            CommonPagedData<RecordDTO> data = result.getData();
            log.info("分页信息: 总记录数={}, 当前页记录数={}, 页面大小={}",
                    data.getTotal(), data.getRecords().size(), req.getSize());

            // 测试第二页
            if (data.getTotal() > req.getSize()) {
                TopicPageDraftListReq req2 = new TopicPageDraftListReq();
                req2.setPage(2L);
                req2.setSize(5L);

                Result<CommonPagedData<RecordDTO>> result2 = beTopicPageMgr.getTopicPagesWithDrafts(req2);
                log.info("第二页测试结果: {}", JsonUtil.toJSONString(result2));
            }
        }
    }

    @Test
    public void testSaveTopicPage() {
        TopicPageSaveReq req = new TopicPageSaveReq();
        req.setPageId("topic_63");
        req.setName("范德萨解放路店撒酒疯");
        req.setConfig(CONFIG);
        req.setRemark("测试111");
        req.setId(63L);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("test");
        userInfo.setUserName("测试用户");
        req.setUserInfo(userInfo);
        
        Result<Void> result = beTopicPageMgr.saveTopicPage(req);
        log.info("saveTopicPage result: {}", JsonUtil.toJSONString(result));
    }

    @Test
    public void testGetDraftDetail() {
        TopicPageDraftDetailReq req = new TopicPageDraftDetailReq();
        req.setId(63L);
        req.setPageId("topic_63");
        
        Result<RecordDTO> result = beTopicPageMgr.getDraftDetail(req);
        log.info("getDraftDetail result: {}", JsonUtil.toJSONString(result));
    }

    @Test
    public void testDeleteDraft() {
        TopicPageDraftDeleteReq req = new TopicPageDraftDeleteReq();
        req.setId(1L);
        req.setPageId("test_page");
        req.setDeleteRemark("测试删除");
        
        Result<Void> result = beTopicPageMgr.deleteDraft(req);
        log.info("deleteDraft result: {}", JsonUtil.toJSONString(result));
    }

    @Test
    public void testCompleteWorkflow() {
        // 完整工作流程测试：保存草稿 -> 提交审核 -> 审核通过
        log.info("开始完整工作流程测试");
        
        // 1. 保存草稿
        TopicPageSaveReq saveReq = new TopicPageSaveReq();
        saveReq.setPageId(null);
        saveReq.setName("workflow_test_" + System.currentTimeMillis());
        saveReq.setConfig(CONFIG);
        saveReq.setRemark("工作流程测试");
        
        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("test");
        userInfo.setUserName("测试用户");
        saveReq.setUserInfo(userInfo);
        
        Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
        log.info("保存草稿结果: {}", JsonUtil.toJSONString(saveResult));
        
        if (!ResultUtils.isSuccess(saveResult)) {
            log.error("保存草稿失败，终止流程测试");
            return;
        }
        
        // 2. 查找刚创建的草稿
        SitePageConfigDraftEntity latestDraft = findLatestDraft(userInfo.getAccount());
        if (latestDraft == null) {
            log.error("未找到最新创建的草稿，终止流程测试");
            return;
        }
        
        log.info("找到最新草稿: draftId={}, pageId={}", latestDraft.getId(), latestDraft.getPageId());
        
        // 3. 提交审核
        Integer operationType = BpmOperationType.MODIFY.getCode();
        String remark = "提交上线审核";
        
        Result<Void> submitResult = bpmInternalService.submitBpmApproval(
                latestDraft.getId(), operationType, remark, userInfo);
        log.info("提交审核结果: {}", JsonUtil.toJSONString(submitResult));
        
        if (!ResultUtils.isSuccess(submitResult)) {
            log.error("提交审核失败: {}", submitResult.getMessage());
            return;
        }
        
        log.info("完整工作流程测试完成");
    }
    
    @Test
    public void testSaveAndSubmitApproval() {
        // 测试保存草稿后立即提交审核
        log.info("开始保存并提交审核测试");
        
        // 1. 保存草稿
        TopicPageSaveReq saveReq = new TopicPageSaveReq();
        saveReq.setPageId(null);
        saveReq.setName("approval_test_" + System.currentTimeMillis());
        saveReq.setConfig(CONFIG);
        saveReq.setRemark("测试保存并提交审核");
        
        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("test");
        userInfo.setUserName("测试用户");
        saveReq.setUserInfo(userInfo);
        
        Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
        log.info("保存草稿结果: {}", JsonUtil.toJSONString(saveResult));
        
        if (ResultUtils.isSuccess(saveResult)) {
            // 2. 查找并提交审核
            SitePageConfigDraftEntity latestDraft = findLatestDraft(userInfo.getAccount());
            if (latestDraft != null) {
                Result<Void> submitResult = bpmInternalService.submitBpmApproval(
                        latestDraft.getId(), 
                        BpmOperationType.MODIFY.getCode(),
                        "自动提交审核", 
                        userInfo);
                log.info("提交审核结果: {}", JsonUtil.toJSONString(submitResult));
            }
        }
    }
    
    @Test
    public void testHandleCallbackApproved() {
        // 测试审核通过回调
        log.info("开始测试审核通过回调");
        
        try {
            // 1. 先保存一个草稿并提交审核，获取businessKey
            TopicPageSaveReq saveReq = new TopicPageSaveReq();
            saveReq.setPageId(null);
            saveReq.setName("callback_test_" + System.currentTimeMillis());
            saveReq.setConfig(CONFIG);
            saveReq.setRemark("回调测试");
            
            UserInfo userInfo = new UserInfo();
            userInfo.setAccount("test");
            userInfo.setUserName("测试用户");
            saveReq.setUserInfo(userInfo);
            
            // 保存草稿
            log.info("步骤1: 保存草稿");
            Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
            log.info("保存草稿结果: {}", JsonUtil.toJSONString(saveResult));
            
            if (!ResultUtils.isSuccess(saveResult)) {
                log.error("保存草稿失败，跳过回调测试");
                return;
            }
            
            // 2. 查找草稿并提交审核
            log.info("步骤2: 查找草稿");
            SitePageConfigDraftEntity latestDraft = findLatestDraft(userInfo.getAccount());
            if (latestDraft == null) {
                log.error("未找到最新草稿，跳过回调测试");
                return;
            }
            
            // 提交审核获取businessKey
            log.info("步骤3: 提交审核");
            Result<Void> submitResult = bpmInternalService.submitBpmApproval(
                    latestDraft.getId(), 
                    BpmOperationType.MODIFY.getCode(),
                    "提交审核获取businessKey", 
                    userInfo);
            log.info("提交审核结果: {}", JsonUtil.toJSONString(submitResult));
            
            if (!ResultUtils.isSuccess(submitResult)) {
                log.error("提交审核失败，跳过回调测试");
                return;
            }
            
            // 3. 重新查询草稿获取businessKey
            log.info("步骤4: 获取businessKey");
            SitePageConfigDraftEntity updatedDraft = draftEngine.getById(latestDraft.getId());
            if (updatedDraft == null || updatedDraft.getGroupKey() == null) {
                log.error("未找到businessKey，跳过回调测试");
                return;
            }
            
            // 4. 构造回调请求 - 审核通过
            log.info("步骤5: 构造回调请求");
            BpmCallbackDTO callback = new BpmCallbackDTO();
            callback.setBusinessKey(updatedDraft.getGroupKey());
            callback.setProcessInstanceStatus(ProcessInstanceStatusEnum.COMPLETED.name());
            
            Operator operator = new Operator();
            operator.setUserName("approver");
            operator.setDisplayName("审批人");
            callback.setOperator(operator);
            callback.setComment("审核通过，测试回调");
            
            // 5. 执行回调测试
            log.info("步骤6: 执行回调测试 - businessKey: {}", updatedDraft.getGroupKey());
            BpmCallbackResponse response = bpmCallbackService.handleSiteConfigCallback(callback);
            log.info("回调执行结果: {}", JsonUtil.toJSONString(response));
            
            // 6. 验证回调结果
            if (response.getCode() == 0) {
                log.info("✅ 回调执行成功！");
            } else {
                log.error("❌ 回调执行失败: {}", response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("测试异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Test
    public void testHandleCallbackRejected() {
        // 测试审核驳回回调
        log.info("开始测试审核驳回回调");
        
        // 1. 先保存一个草稿并提交审核
        TopicPageSaveReq saveReq = new TopicPageSaveReq();
        saveReq.setPageId(null);
        saveReq.setName("reject_test_" + System.currentTimeMillis());
        saveReq.setConfig(CONFIG);
        saveReq.setRemark("驳回测试");
        
        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("test");
        userInfo.setUserName("测试用户");
        saveReq.setUserInfo(userInfo);
        
        Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
        if (!ResultUtils.isSuccess(saveResult)) {
            log.error("保存草稿失败，跳过驳回测试");
            return;
        }
        
        // 2. 提交审核
        SitePageConfigDraftEntity latestDraft = findLatestDraft(userInfo.getAccount());
        if (latestDraft == null) {
            log.error("未找到最新草稿，跳过驳回测试");
            return;
        }
        
        Result<Void> submitResult = bpmInternalService.submitBpmApproval(
                latestDraft.getId(), 
                BpmOperationType.MODIFY.getCode(),
                "提交审核用于驳回测试", 
                userInfo);
        
        if (!ResultUtils.isSuccess(submitResult)) {
            log.error("提交审核失败，跳过驳回测试");
            return;
        }
        
        // 3. 获取businessKey
        SitePageConfigDraftEntity updatedDraft = draftEngine.getById(latestDraft.getId());
        if (updatedDraft == null || updatedDraft.getGroupKey() == null) {
            log.error("未找到businessKey，跳过驳回测试");
            return;
        }
        
        // 4. 构造回调请求 - 审核驳回
        BpmCallbackDTO callback = new BpmCallbackDTO();
        callback.setBusinessKey(updatedDraft.getGroupKey());
        callback.setProcessInstanceStatus(ProcessInstanceStatusEnum.REJECTED.name());
        
        Operator operator = new Operator();
        operator.setUserName("approver");
        operator.setDisplayName("审批人");
        callback.setOperator(operator);
        callback.setComment("审核驳回，需要修改内容");
        
        // 5. 执行回调测试
        BpmCallbackResponse response = bpmCallbackService.handleSiteConfigCallback(callback);
        log.info("驳回回调执行结果: {}", JsonUtil.toJSONString(response));
        
        // 6. 验证回调结果
        if (response.getCode() == 0) {
            log.info("驳回回调执行成功！");
        } else {
            log.error("驳回回调执行失败: {}", response.getMessage());
        }
    }
    
    @Test
    public void testCompleteWorkflowWithCallback() {
        // 完整流程测试：保存草稿 -> 提交审核 -> 审核通过回调 -> 验证最终状态
        log.info("开始完整流程测试（包含回调）");
        
        // 1. 保存草稿
        TopicPageSaveReq saveReq = new TopicPageSaveReq();
        saveReq.setPageId(null);
        saveReq.setName("complete_workflow_" + System.currentTimeMillis());
        saveReq.setConfig(CONFIG);
        saveReq.setRemark("完整流程测试");
        
        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("test");
        userInfo.setUserName("测试用户");
        saveReq.setUserInfo(userInfo);
        
        Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
        log.info("1. 保存草稿结果: {}", JsonUtil.toJSONString(saveResult));
        
        if (!ResultUtils.isSuccess(saveResult)) {
            log.error("保存草稿失败，终止流程测试");
            return;
        }
        
        // 2. 提交审核
        SitePageConfigDraftEntity latestDraft = findLatestDraft(userInfo.getAccount());
        if (latestDraft == null) {
            log.error("未找到最新草稿，终止流程测试");
            return;
        }
        
        log.info("2. 找到草稿: draftId={}, pageId={}", latestDraft.getId(), latestDraft.getPageId());
        
        Result<Void> submitResult = bpmInternalService.submitBpmApproval(
                latestDraft.getId(), 
                BpmOperationType.MODIFY.getCode(),
                "完整流程测试-提交审核", 
                userInfo);
        log.info("3. 提交审核结果: {}", JsonUtil.toJSONString(submitResult));
        
        if (!ResultUtils.isSuccess(submitResult)) {
            log.error("提交审核失败，终止流程测试");
            return;
        }
        
        // 3. 获取businessKey
        SitePageConfigDraftEntity updatedDraft = draftEngine.getById(latestDraft.getId());
        log.info("4. 草稿状态更新: status={}, groupKey={}", 
                updatedDraft.getStatus(), updatedDraft.getGroupKey());
        
        // 4. 执行审核通过回调
        BpmCallbackDTO callback = new BpmCallbackDTO();
        callback.setBusinessKey(updatedDraft.getGroupKey());
        callback.setProcessInstanceStatus(ProcessInstanceStatusEnum.COMPLETED.name());
        
        Operator operator = new Operator();
        operator.setUserName("approver");
        operator.setDisplayName("审批人");
        callback.setOperator(operator);
        callback.setComment("完整流程测试-审核通过");
        
        BpmCallbackResponse response = bpmCallbackService.handleSiteConfigCallback(callback);
        log.info("5. 审核通过回调结果: {}", JsonUtil.toJSONString(response));
        
        // 5. 验证最终状态
        SitePageConfigDraftEntity finalDraft = draftEngine.getById(latestDraft.getId());
        log.info("6. 最终草稿状态: status={}, pageId={}", 
                finalDraft.getStatus(), finalDraft.getPageId());
        
        if (response.getCode() == 0) {
            log.info("✅ 完整流程测试成功完成！");
        } else {
            log.error("❌ 完整流程测试失败: {}", response.getMessage());
        }
    }
    
    @Test
    public void testHandleCallbackOfflineApproved() {
        // 测试下线审核通过回调 - 完整流程
        log.info("开始测试下线审核通过回调");

        try {
            // 1. 先创建并上线一个页面
            log.info("步骤1: 创建并上线一个页面");
            TopicPageSaveReq saveReq = new TopicPageSaveReq();
            saveReq.setPageId(null);
            saveReq.setName("offline_test_" + System.currentTimeMillis());
            saveReq.setConfig(CONFIG);
            saveReq.setRemark("下线测试页面");

            UserInfo userInfo = new UserInfo();
            userInfo.setAccount("test");
            userInfo.setUserName("测试用户");
            saveReq.setUserInfo(userInfo);

            Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
            if (!ResultUtils.isSuccess(saveResult)) {
                log.error("保存草稿失败，跳过下线测试");
                return;
            }

            // 2. 提交上线审核
            log.info("步骤2: 提交上线审核");
            SitePageConfigDraftEntity latestDraft = findLatestDraft(userInfo.getAccount());
            if (latestDraft == null) {
                log.error("未找到最新草稿，跳过下线测试");
                return;
            }

            Result<Void> submitResult = bmpInternalService.submitBpmApproval(
                    latestDraft.getId(),
                    BpmOperationType.MODIFY.getCode(),
                    "提交上线审核",
                    userInfo);

            if (!ResultUtils.isSuccess(submitResult)) {
                log.error("提交上线审核失败，跳过下线测试");
                return;
            }

            // 3. 模拟上线审核通过
            log.info("步骤3: 模拟上线审核通过");
            SitePageConfigDraftEntity updatedDraft = draftEngine.getById(latestDraft.getId());

            BpmCallbackDTO onlineCallback = new BpmCallbackDTO();
            onlineCallback.setBusinessKey(updatedDraft.getGroupKey());
            onlineCallback.setProcessInstanceStatus(ProcessInstanceStatusEnum.COMPLETED.name());

            Operator onlineOperator = new Operator();
            onlineOperator.setUserName("approver");
            onlineOperator.setDisplayName("审批人");
            onlineCallback.setOperator(onlineOperator);
            onlineCallback.setComment("上线审核通过");

            BpmCallbackResponse onlineResponse = bmpCallbackService.handleSiteConfigCallback(onlineCallback);
            log.info("上线回调执行结果: {}", JsonUtil.toJSONString(onlineResponse));

            if (onlineResponse.getCode() != 0) {
                log.error("上线回调失败，跳过下线测试: {}", onlineResponse.getMessage());
                return;
            }

            // 4. 提交下线审核
            log.info("步骤4: 提交下线审核");
            Result<Void> offlineSubmitResult = bmpInternalService.submitBpmApproval(
                    latestDraft.getId(),
                    BpmOperationType.OFFLINE.getCode(),
                    "提交下线审核",
                    userInfo);

            if (!ResultUtils.isSuccess(offlineSubmitResult)) {
                log.error("提交下线审核失败: {}", offlineSubmitResult.getMessage());
                return;
            }

            // 5. 模拟下线审核通过回调
            log.info("步骤5: 执行下线审核通过回调");
            SitePageConfigDraftEntity offlineDraft = draftEngine.getById(latestDraft.getId());

            BpmCallbackDTO offlineCallback = new BpmCallbackDTO();
            offlineCallback.setBusinessKey(offlineDraft.getGroupKey());
            offlineCallback.setProcessInstanceStatus(ProcessInstanceStatusEnum.COMPLETED.name());

            Operator offlineOperator = new Operator();
            offlineOperator.setUserName("approver");
            offlineOperator.setDisplayName("审批人");
            offlineCallback.setOperator(offlineOperator);
            offlineCallback.setComment("下线审核通过，页面下线");

            BpmCallbackResponse offlineResponse = bmpCallbackService.handleSiteConfigCallback(offlineCallback);
            log.info("下线回调执行结果: {}", JsonUtil.toJSONString(offlineResponse));

            // 6. 验证下线结果
            if (offlineResponse.getCode() == 0) {
                log.info("✅ 下线流程测试成功完成！");

                // 验证最终状态
                SitePageConfigDraftEntity finalDraft = draftEngine.getById(latestDraft.getId());
                log.info("最终草稿状态: status={}, pageId={}",
                        finalDraft.getStatus(), finalDraft.getPageId());
            } else {
                log.error("❌ 下线回调执行失败: {}", offlineResponse.getMessage());
            }

        } catch (Exception e) {
            log.error("下线流程测试异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 查找指定用户的最新草稿
     */
    private SitePageConfigDraftEntity findLatestDraft(String operatorId) {
        return draftEngine.lambdaQuery()
                .eq(SitePageConfigDraftEntity::getOperatorId, operatorId)
                .orderByDesc(SitePageConfigDraftEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
    }

    @Test
    public void testPageStatusWorkflow() {
        // 测试页面状态工作流：创建 -> 上线 -> 下线
        log.info("=== 测试页面状态工作流 ===");

        // 1. 创建新专题页
        TopicPageSaveReq saveReq = new TopicPageSaveReq();
        saveReq.setName("状态测试专题页");
        saveReq.setConfig("{\"title\":\"测试页面\",\"components\":[]}");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("test_user");
        userInfo.setUserName("测试用户");
        saveReq.setUserInfo(userInfo);

        Result<Void> saveResult = beTopicPageMgr.saveTopicPage(saveReq);
        log.info("创建专题页结果: {}", JsonUtil.toJSONString(saveResult));

        if (saveResult.getCode() == 200) {
            log.info("✅ 专题页创建成功，此时应该在 site_page_info 中创建了 status=0 的记录");

            // 2. 查询草稿列表，验证创建成功
            TopicPageDraftListReq listReq = new TopicPageDraftListReq();
            listReq.setPage(1L);
            listReq.setSize(10L);
            listReq.setName("状态测试");

            Result<CommonPagedData<RecordDTO>> listResult = beTopicPageMgr.getTopicPagesWithDrafts(listReq);
            log.info("查询草稿列表结果: {}", JsonUtil.toJSONString(listResult));

            if (listResult.getCode() == 200 && listResult.getData() != null && !listResult.getData().getRecords().isEmpty()) {
                RecordDTO draft = listResult.getData().getRecords().get(0);
                log.info("✅ 找到创建的草稿: pageId={}, status={}", draft.getPageId(), draft.getStatus());
                log.info("📝 注意：此时 site_page_info.status=0（未上线），DBSiteDataLoader 应该过滤掉这个页面");
            }
        }
    }
}