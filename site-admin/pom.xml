<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaomi.micar</groupId>
        <artifactId>micar-site</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>site-admin</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.micar</groupId>
            <artifactId>site-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!--spring-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <!-- dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-common</artifactId>
            <version>1.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!-- caffeine -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <!-- openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--youpin infra-->
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>youpin-infra-rpc</artifactId>
        </dependency>
        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.car.iccc</groupId>
            <artifactId>iccc-api</artifactId>
        </dependency>
        <!-- rocketmq-->
        <dependency>
            <groupId>com.mi.car.iccc</groupId>
            <artifactId>iccc-rocketmq-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.mi.car.iccc</groupId>
            <artifactId>iccc-fds-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-keycenter-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.11.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.yaml/snakeyaml -->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version>
        </dependency>
        
        <dependency>
            <groupId>com.xiaomi</groupId>
            <artifactId>keycenter-agent-client</artifactId>
        </dependency>

        <!-- idCenter-->
        <dependency>
            <groupId>com.mi.car.iccc</groupId>
            <artifactId>id-center</artifactId>
        </dependency>

        <!-- logger -->
        <dependency>
            <groupId>com.mi.car.iccc</groupId>
            <artifactId>iccc-logger-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>iccc-json-facade</artifactId>
                    <groupId>com.mi.car.iccc</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Mockito依赖已包含在spring-boot-starter-test中 -->

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-core</artifactId>
            <version>2.7.12-mone-v10-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miapi-doc-annos</artifactId>
            <version>2.7.12-mone-v20-SNAPSHOT</version>
        </dependency>

        <!--   用户人群标签功能    -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>sentinel-dubbo-adapter</artifactId>
                    <groupId>com.alibaba.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-apache-dubbo-adapter</artifactId>
            <version>1.8.2.1-mone-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.mibpm</groupId>
            <artifactId>mibpm-bpm-spring-boot-starter</artifactId>
            <version>1.2.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.github.openfeign</groupId>
                    <artifactId>feign-micrometer</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-micrometer</artifactId>
            <version>11.6</version>
        </dependency>


        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.4.1</version>
        </dependency>

        <!-- JSON Schema 验证库 -->
        <dependency>
            <groupId>com.networknt</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>1.0.87</version>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.properties</include>
                    <include>*.yml</include>
                    <include>*.json</include>
                    <include>*.xml</include>
                    <include>json-schemas/**</include>
                    <include>META-INF/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources/zk</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.properties</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xiaomi.micar.site.SiteAdminApp</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>