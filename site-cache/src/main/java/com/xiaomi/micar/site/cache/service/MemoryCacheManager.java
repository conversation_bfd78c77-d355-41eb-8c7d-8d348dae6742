package com.xiaomi.micar.site.cache.service;

import com.mi.car.iccc.starter.metrics.util.Metrics;
import com.xiaomi.micar.site.core.monitoring.CacheMonitoringEnhancer;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;

import java.util.concurrent.atomic.AtomicReference;

/**
 * 内存缓存管理器
 * 负责管理页面缓存在内存中的存储和访问
 * 使用原子操作确保线程安全
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class MemoryCacheManager {

    @Resource
    private CacheMonitoringEnhancer monitoringEnhancer;

    /**
     * 页面缓存状态映射
     * 页面ID -> 原子引用的响应缓存Map
     */
    private final ConcurrentHashMap<String, AtomicReference<Map<String, String>>> pageStates = new ConcurrentHashMap<>();

    /**
     * 历史快照缓存映射
     * 缓存键格式：combinationKey:clientVersion:apiVersion -> JSON响应字符串
     */
    private final AtomicReference<Map<String, String>> historicalSnapshotCacheRef = new AtomicReference<>(new ConcurrentHashMap<>());

    private final String cacheName = "memory-cache-manager";

    @PostConstruct
    public void initMonitoring() {
        // 注册页面缓存大小监控
        Gauge.builder("cache.page.size", this, mgr -> mgr.getPageCacheTotalSize())
                .tag("cache", cacheName)
                .description("Current page cache size")
                .register(Metrics.getRegistry());

        // 注册历史快照缓存大小监控
        Gauge.builder("cache.snapshot.size", this, mgr -> mgr.getHistoricalSnapshotCacheSize())
                .tag("cache", cacheName)
                .description("Current snapshot cache size")
                .register(Metrics.getRegistry());

        // 注册页面数量监控
        Gauge.builder("cache.page.count", this, mgr -> mgr.getPageCount())
                .tag("cache", cacheName)
                .description("Number of cached pages")
                .register(Metrics.getRegistry());

        // 注册内存使用估算监控（实时计算）
        Gauge.builder("cache.memory.estimated", this, mgr -> mgr.estimateMemoryUsage())
                .tag("cache", cacheName)
                .description("Estimated memory usage in bytes")
                .register(Metrics.getRegistry());

        if (log.isDebugEnabled()) {
            log.debug("Memory cache monitoring initialized for: {}", cacheName);
        }
    }

    /**
     * 原子性地交换页面缓存数据
     */
    public boolean swapPageCache(final String pageId, Map<String, String> responseCache) {
        if (responseCache == null || responseCache.isEmpty()) {
            log.warn("响应缓存为空，跳过交换操作，pageId={}", pageId);
            return false;
        }

        try {
            // 创建新的缓存Map副本，确保线程安全
            Map<String, String> newCacheMap = new ConcurrentHashMap<>(responseCache);

            // 获取或创建 AtomicReference
            AtomicReference<Map<String, String>> reference = pageStates.computeIfAbsent(pageId,
                    k -> new AtomicReference<>());

            // 原子性地替换整个缓存Map
            reference.set(newCacheMap);

            // 记录缓存更新频率和大小分布（移除高基数 page_id 标签）
            monitoringEnhancer.recordCount("cache.update.frequency", 1, "cache_type=page");

            // 计算缓存大小并记录分布（使用快速计算方式，移除高基数标签）
            long cacheSize = calculateFastCacheSize(responseCache);
            monitoringEnhancer.recordCacheSizeDistribution("page", cacheSize, "cache_type=page");

            log.info("成功原子性交换页面缓存，pageId={}, 缓存项数量={}", pageId, responseCache.size());
            return true;

        } catch (Exception e) {
            log.error("原子性交换页面缓存时发生异常，pageId={}: {}", pageId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取组合响应
     */
    public String getCombinationResponse(String pageId, String combinationKey) {
        if (pageId == null || combinationKey == null) {
            return null;
        }

        AtomicReference<Map<String, String>> reference = pageStates.get(pageId);
        if (reference == null) {
            return null;
        }

        Map<String, String> responseCache = reference.get();
        if (responseCache == null) {
            return null;
        }

        return responseCache.get(combinationKey);
    }

    /**
     * 原子性地更新历史快照缓存
     *
     * @param historicalSnapshotCache 历史快照缓存Map
     * @return 是否更新成功
     */
    public boolean swapHistoricalSnapshotCache(Map<String, String> historicalSnapshotCache) {
        if (historicalSnapshotCache == null) {
            log.warn("历史快照缓存为空，跳过更新操作");
            return false;
        }

        try {
            // 创建新的缓存Map副本，确保线程安全
            Map<String, String> newCacheMap = new ConcurrentHashMap<>(historicalSnapshotCache);

            // 原子性地替换整个缓存Map
            historicalSnapshotCacheRef.set(newCacheMap);

            log.info("成功原子性更新历史快照缓存，缓存条目数量={}", historicalSnapshotCache.size());
            return true;

        } catch (Exception e) {
            log.error("原子性更新历史快照缓存时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取历史快照缓存响应
     *
     * @param cacheKey 缓存键（格式：combinationKey:clientVersion:apiVersion）
     * @return JSON响应字符串，如果没有找到则返回null
     */
    public String getHistoricalSnapshotResponse(String cacheKey) {
        if (cacheKey == null) {
            return null;
        }

        Map<String, String> currentCache = historicalSnapshotCacheRef.get();
        String response = currentCache.get(cacheKey);

        if (response != null) {
            if (log.isDebugEnabled()) {
                log.debug("历史快照缓存命中: cacheKey={}", cacheKey);
            }
        } else {
            if (log.isDebugEnabled()) {
                log.debug("历史快照缓存未命中: cacheKey={}", cacheKey);
            }
        }

        return response;
    }

    /**
     * 获取当前历史快照缓存的大小
     *
     * @return 缓存条目数量
     */
    public int getHistoricalSnapshotCacheSize() {
        return historicalSnapshotCacheRef.get().size();
    }

    /**
     * 获取页面缓存统计信息
     * 返回当前所有页面缓存的统计信息，用于缓存状态分析和调试
     *
     * @return 页面ID到缓存项数量的映射
     */
    public Map<String, Integer> getPageCacheStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();

        for (Map.Entry<String, AtomicReference<Map<String, String>>> entry : pageStates.entrySet()) {
            String pageId = entry.getKey();
            AtomicReference<Map<String, String>> reference = entry.getValue();

            if (reference != null) {
                Map<String, String> responseCache = reference.get();
                if (responseCache != null) {
                    stats.put(pageId, responseCache.size());
                }
            }
        }

        return stats;
    }

    /**
     * 移除指定页面的缓存数据
     *
     * @param pageId 页面ID
     * @return 是否成功移除
     */
    public boolean removePageCache(String pageId) {
        if (pageId == null) {
            return false;
        }

        AtomicReference<Map<String, String>> removed = pageStates.remove(pageId);
        if (removed != null) {
            if (log.isDebugEnabled()) {
                log.debug("成功移除页面缓存，pageId={}", pageId);
            }
            return true;
        } else {
            if (log.isDebugEnabled()) {
                log.debug("页面缓存不存在，无需移除，pageId={}", pageId);
            }
            return false;
        }
    }

    /**
     * 批量移除过期页面的缓存数据
     *
     * @param obsoletePageIds 过期页面ID集合
     * @return 成功移除的数量
     */
    public int removeObsoletePageCaches(Set<String> obsoletePageIds) {
        if (obsoletePageIds == null || obsoletePageIds.isEmpty()) {
            return 0;
        }

        int removedCount = 0;
        for (String pageId : obsoletePageIds) {
            if (removePageCache(pageId)) {
                removedCount++;
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("批量移除过期页面缓存完成，总数={}, 成功移除={}", obsoletePageIds.size(), removedCount);
        }
        return removedCount;
    }

    /**
     * 获取当前所有页面ID
     *
     * @return 页面ID集合
     */
    public Set<String> getAllPageIds() {
        return new HashSet<>(pageStates.keySet());
    }

    // ==================== 监控相关方法 ====================

    /**
     * 获取页面缓存总大小
     *
     * @return 所有页面缓存的总条目数
     */
    public double getPageCacheTotalSize() {
        return pageStates.values().stream()
                .mapToInt(ref -> {
                    Map<String, String> cache = ref.get();
                    return cache != null ? cache.size() : 0;
                })
                .sum();
    }

    /**
     * 获取页面数量
     *
     * @return 当前缓存的页面数量
     */
    public double getPageCount() {
        return pageStates.size();
    }

    /**
     * 计算内存使用量（实时快速计算）
     *
     * @return 内存使用量（字节）
     */
    public double estimateMemoryUsage() {
        try {
            return calculateFastMemoryUsage();
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("快速内存计算失败: {}", e.getMessage());
            }
            return 0;
        }
    }

    /**
     * 快速计算内存使用量（性能优先）
     * 只计算字符串内容大小，忽略对象开销
     */
    private double calculateFastMemoryUsage() {
        long totalSize = 0L;

        // 快速计算页面缓存的内存使用
        for (AtomicReference<Map<String, String>> pageRef : pageStates.values()) {
            Map<String, String> pageCache = pageRef.get();
            if (pageCache != null) {
                totalSize += calculateFastCacheSize(pageCache);
            }
        }

        // 快速计算快照缓存的内存使用
        Map<String, String> snapshotCache = historicalSnapshotCacheRef.get();
        if (snapshotCache != null) {
            totalSize += calculateFastCacheSize(snapshotCache);
        }

        // 基础开销估算
        totalSize += pageStates.size() * 32; // 每个页面的基础开销
        totalSize += 64; // 基础对象开销

        return (double) totalSize;
    }

    /**
     * 快速计算缓存大小（用于实时监控）
     * 只计算字符串内容大小，忽略对象开销，性能优先
     *
     * @param cache 缓存Map
     * @return 快速估算的大小（字节）
     */
    private long calculateFastCacheSize(Map<String, String> cache) {
        if (cache == null || cache.isEmpty()) {
            return 0L;
        }

        return cache.entrySet().stream()
            .mapToLong(entry -> {
                // 使用UTF-8编码计算字节长度，性能更好
                String key = entry.getKey();
                String value = entry.getValue();

                long keySize = key != null ? key.getBytes(StandardCharsets.UTF_8).length : 0;
                long valueSize = value != null ? value.getBytes(StandardCharsets.UTF_8).length : 0;

                return keySize + valueSize;
            })
            .sum();
    }

}
