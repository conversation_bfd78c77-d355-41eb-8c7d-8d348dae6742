package com.xiaomi.micar.site.cache.policy;

import lombok.Getter;

/**
 * 缓存策略（最小实现）
 */
@Getter
public enum CachePolicy {
    REDIS_ONLY(ReadPlan.redisOnly(), WritePlan.redisOnly(), Distribute.NONE, ReceiverTarget.NONE),
    MYSQL_BROADCAST_MEMORY(ReadPlan.memoryOnly(), WritePlan.mysqlOnly(), Distribute.BROADCAST, ReceiverTarget.MEMORY);

    private final ReadPlan readPlan;
    private final WritePlan writePlan;
    private final Distribute distribute;
    private final ReceiverTarget receiverTarget;

    CachePolicy(ReadPlan readPlan, WritePlan writePlan, Distribute distribute, ReceiverTarget receiverTarget) {
        this.readPlan = readPlan;
        this.writePlan = writePlan;
        this.distribute = distribute;
        this.receiverTarget = receiverTarget;
    }
}

