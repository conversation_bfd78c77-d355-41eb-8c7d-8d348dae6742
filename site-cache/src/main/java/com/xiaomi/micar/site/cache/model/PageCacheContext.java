package com.xiaomi.micar.site.cache.model;

import java.util.List;
import java.util.Map;
import java.util.Set;
import com.xiaomi.micar.site.model.PageConfigModel;
import lombok.Getter;

/**
 * 页面缓存上下文类，提供页面缓存上下文，供页面响应构建器使用.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public class PageCacheContext {

    /**
     * 页面配置模型
     */
    private final PageConfigModel pageConfig;

    /**
     * 页面ID
     */
    private final String pageId;

    /**
     * 用户组策略
     */
    private final String strategy;

    /**
     * 实验组合（适用的实验列表）
     */
    private final List<Map<String, String>> applicableExperiments;

    /**
     * 组件处理标记，用于避免同一个页面的不同人群中重复处理同一个组件
     * 在页面级别共享，确保同一个组件在整个页面构建过程中只处理一次
     */
    private final Set<String> processedComponents;

    /**
     * 构造函数
     *
     * @param pageConfig 页面配置
     * @param pageId 页面ID
     * @param strategy 用户组策略
     * @param applicableExperiments 适用的实验列表
     * @param processedComponents 组件处理标记
     */
    public PageCacheContext(PageConfigModel pageConfig, String pageId, String strategy,
                           List<Map<String, String>> applicableExperiments, Set<String> processedComponents) {
        this.pageConfig = pageConfig;
        this.pageId = pageId;
        this.strategy = strategy;
        this.applicableExperiments = applicableExperiments;
        this.processedComponents = processedComponents;
    }
}
