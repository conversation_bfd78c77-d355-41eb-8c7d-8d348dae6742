package com.xiaomi.micar.site.cache;


import com.xiaomi.micar.site.model.PageRespV2;

import java.util.Map;

/**
 * 缓存后置处理器接口
 * 用于在缓存处理完成后执行自定义操作
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
public interface CachePostProcessor {

    /**
     * 处理缓存状态
     * 在缓存构建完成后调用
     *
     * @param cacheState 缓存状态
     */
    void process(Map<String, PageRespV2> cacheState);
    
    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getName();
    
    /**
     * 获取处理器优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    default int getOrder() {
        return 0;
    }
}
