package com.xiaomi.micar.site.cache.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 历史快照缓存键生成器
 * 统一管理历史快照缓存键的生成逻辑，确保生成和使用时格式完全一致
 * 
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
public class HistoricalSnapshotCacheKeyGenerator {

    /**
     * 历史快照缓存键分隔符
     */
    private static final String CACHE_KEY_SEPARATOR = "|";

    /**
     * 为查询场景生成历史快照缓存键
     * 用于 HistoricalSnapshotPageAssemblyService 等查询服务
     *
     * @param combinationKey 基础组合键（由调用方生成）
     * @param rnVersion      客户端版本
     * @param version        数据库版本号（1=V1版本，2=V2版本）
     * @return 历史快照缓存键，如果参数无效则返回null
     */
    public static String generateForQuery(String combinationKey, String rnVersion, Integer version) {
        // 转换版本号为API版本字符串
        String apiVersion = CacheKeyUtil.convertVersionToApiVersion(version);

        // 生成最终缓存键
        String cacheKey = generateCacheKey(combinationKey, rnVersion, apiVersion);

        log.debug("生成查询缓存键成功: combinationKey={}, rnVersion={}, version={} -> cacheKey={}",
                combinationKey, rnVersion, version, cacheKey);

        return cacheKey;
    }

    /**
     * 为存储场景生成历史快照缓存键
     * 用于 CacheSyncService 等存储服务
     * 
     * @param combinationKey 基础组合键（从数据库读取）
     * @param rnVersion      客户端版本
     * @param version        数据库版本号（1=V1版本，2=V2版本）
     * @return 历史快照缓存键，如果参数无效则返回null
     */
    public static String generateForStorage(String combinationKey, String rnVersion, Integer version) {
        if (combinationKey == null || rnVersion == null || version == null) {
            log.warn("生成存储缓存键失败，参数不完整: combinationKey={}, rnVersion={}, version={}", 
                    combinationKey, rnVersion, version);
            return null;
        }

        try {
            // 转换版本号为API版本字符串
            String apiVersion = CacheKeyUtil.convertVersionToApiVersion(version);

            // 生成最终缓存键
            String cacheKey = generateCacheKey(combinationKey, rnVersion, apiVersion);
            
            log.debug("生成存储缓存键成功: combinationKey={}, rnVersion={}, version={} -> cacheKey={}", 
                    combinationKey, rnVersion, version, cacheKey);
            
            return cacheKey;

        } catch (Exception e) {
            log.error("生成存储缓存键时发生异常: combinationKey={}, rnVersion={}, version={}", 
                    combinationKey, rnVersion, version, e);
            return null;
        }
    }

    /**
     * 核心缓存键生成逻辑
     * 格式：{combinationKey}|{rnVersion}|{apiVersion}
     * 
     * @param combinationKey 基础组合键
     * @param rnVersion      客户端版本
     * @param apiVersion     API版本字符串
     * @return 历史快照缓存键
     */
    private static String generateCacheKey(String combinationKey, String rnVersion, String apiVersion) {
        return String.join(CACHE_KEY_SEPARATOR, combinationKey, rnVersion, apiVersion);
    }

}
