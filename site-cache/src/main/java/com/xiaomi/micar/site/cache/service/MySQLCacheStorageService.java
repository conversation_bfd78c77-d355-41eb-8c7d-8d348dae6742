package com.xiaomi.micar.site.cache.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.component.ComponentConf;
import com.xiaomi.micar.site.cache.CacheStorageService;
import com.xiaomi.micar.site.cache.util.CacheKeyUtil;
import com.xiaomi.micar.site.model.PageResp;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.dao.SitePageCacheStorageEngine;
import com.xiaomi.micar.site.dao.entity.SitePageCacheStorageEntity;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * MySQL缓存存储服务
 * 负责页面缓存的MySQL存储，支持V1/V2双版本和配置版本追踪
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Slf4j
@Service("mySQLCacheStorageService")
public class MySQLCacheStorageService implements CacheStorageService {

    @Resource
    private SitePageCacheStorageEngine cacheStorageEngine;

    /**
     * 保存页面缓存
     */
    @Override
    public String savePageCache(String batchId, String pageId, Map<String, PageRespV2> responseCache, String configVersion) {
        if (responseCache == null || responseCache.isEmpty()) {
            log.warn("缓存数据为空，跳过保存: {}", pageId);
            return null;
        }

        try {
            List<SitePageCacheStorageEntity> entities = buildEntities(pageId, batchId, responseCache, configVersion);
            boolean success = cacheStorageEngine.saveBatch(entities);

            if (success) {
                log.info("缓存保存成功: pageId={}, batchId={}, configVersion={}, 组合数={}",
                        pageId, batchId, configVersion, responseCache.size());
                return batchId;
            } else {
                log.error("缓存保存失败: pageId={}, batchId={}", pageId, batchId);
                return null;
            }
        } catch (Exception e) {
            log.error("缓存保存异常: pageId={}, batchId={}", pageId, batchId, e);
            return null;
        }
    }

    /**
     * 构建存储实体
     */
    private List<SitePageCacheStorageEntity> buildEntities(String pageId, String batchId,
                                                          Map<String, PageRespV2> responseCache,
                                                          String configVersion) {
        List<SitePageCacheStorageEntity> entities = new ArrayList<>();
        Date now = new Date();

        for (Map.Entry<String, PageRespV2> entry : responseCache.entrySet()) {
            String combinationKey = entry.getKey();
            PageRespV2 pageResp = entry.getValue();

            // V2版本
            String v2Config = JsonUtil.toJSONString(Result.success(pageResp));
            entities.add(createEntity(pageId, combinationKey, v2Config, batchId, 2, configVersion, now));

            // V1版本
            PageResp pageRespV1 = convertV2ToV1(pageResp);
            String v1Config = JsonUtil.toJSONString(Result.success(pageRespV1));
            entities.add(createEntity(pageId, combinationKey, v1Config, batchId, 1, configVersion, now));
        }

        return entities;
    }

    /**
     * 加载页面缓存
     */
    @Override
    public Map<String, String> loadPageCache(String pageId, String batchId) {
        try {
            List<SitePageCacheStorageEntity> entities = cacheStorageEngine.getByPageIdAndBatchId(pageId, batchId);
            if (entities == null || entities.isEmpty()) {
                log.warn("缓存数据未找到: pageId={}, batchId={}", pageId, batchId);
                return null;
            }

            Map<String, String> responseCache = buildResponseCache(entities);
            log.info("缓存加载成功: pageId={}, batchId={}, 组合数={}", pageId, batchId, entities.size());
            return responseCache;

        } catch (Exception e) {
            log.error("缓存加载失败: pageId={}, batchId={}", pageId, batchId, e);
            return null;
        }
    }



    /**
     * 构建响应缓存Map
     */
    private Map<String, String> buildResponseCache(List<SitePageCacheStorageEntity> entities) {
        Map<String, String> responseCache = new HashMap<>();
        for (SitePageCacheStorageEntity entity : entities) {
            String keyWithVersion = CacheKeyUtil.addVersionPrefix(entity.getCombinationKey(), entity.getVersion());
            responseCache.put(keyWithVersion, entity.getConfig());
        }
        return responseCache;
    }

    /**
     * 创建存储实体
     */
    private SitePageCacheStorageEntity createEntity(String pageId, String combinationKey,
                                                   String config, String batchId, Integer version,
                                                   String configVersion, Date now) {
        SitePageCacheStorageEntity entity = new SitePageCacheStorageEntity();
        entity.setPageId(pageId);
        entity.setCombinationKey(combinationKey);
        entity.setConfig(config);
        entity.setBatchId(batchId);
        entity.setVersion(version);
        entity.setConfigVersion(parseConfigVersion(configVersion));
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setDeleted(0);
        return entity;
    }

    /**
     * 解析配置版本
     */
    private Integer parseConfigVersion(String configVersion) {
        if (configVersion == null || configVersion.trim().isEmpty()) {
            return 0;
        }

        try {
            return Integer.parseInt(configVersion.split("-")[0]);
        } catch (NumberFormatException e) {
            log.warn("配置版本解析失败: {}, 使用默认值0", configVersion);
            return 0;
        }
    }

    /**
     * V2转V1版本
     */
    private PageResp convertV2ToV1(PageRespV2 v2) {
        Map<String, Component> componentMap = Maps.newHashMap();
        List<String> moduleList = Lists.newArrayList();

        if (v2.getModules() != null) {
            for (ComponentConf conf : v2.getModules()) {
                componentMap.put(conf.getId(), conf.getConfig());
                moduleList.add(conf.getId());
            }
        }

        PageResp resp = new PageResp();
        resp.setPageId(v2.getPageId());
        resp.setExpId(v2.getExpId());
        resp.setPage(convertPageInfo(v2.getPage()));
        resp.setModules(componentMap);
        resp.setModuleList(moduleList);
        return resp;
    }

    /**
     * 转换页面信息
     */
    private PageResp.PageInfo convertPageInfo(PageRespV2.PageInfo v2PageInfo) {
        if (v2PageInfo == null) {
            return null;
        }

        PageResp.PageInfo pageInfo = new PageResp.PageInfo();
        pageInfo.setTitle(v2PageInfo.getTitle());
        pageInfo.setGroupKey(v2PageInfo.getGroupKey());
        return pageInfo;
    }

}
