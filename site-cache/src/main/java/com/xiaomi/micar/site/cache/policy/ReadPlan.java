package com.xiaomi.micar.site.cache.policy;

import lombok.Getter;

/**
 * 读计划定义（最小实现）
 */
@Getter
public class ReadPlan {

    public enum Mode { MEMORY_ONLY, REDIS_ONLY, MYSQL_ONLY }

    private final Mode mode;

    private ReadPlan(Mode mode) {
        this.mode = mode;
    }

    public static ReadPlan memoryOnly() {
        return new ReadPlan(Mode.MEMORY_ONLY);
    }

    public static ReadPlan redisOnly() {
        return new ReadPlan(Mode.REDIS_ONLY);
    }

    public static ReadPlan mysqlOnly() {
        return new ReadPlan(Mode.MYSQL_ONLY);
    }
}

