package com.xiaomi.micar.site.cache;

import com.xiaomi.micar.site.cache.model.CacheState;
import com.xiaomi.micar.site.cache.service.MemoryCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 站点页面缓存服务
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class SitePageCacheService {

    @Resource
    private MemoryCacheManager memoryCacheManager;

    /**
     * 创建新的缓存状态
     *
     * @return 新的缓存状态
     */
    public CacheState createNewCacheState() {
        CacheState state = new CacheState();
        return state;
    }

    /**
     * 获取页面缓存统计信息
     * 返回当前所有页面缓存的统计信息，用于缓存状态分析和调试
     *
     * @return 页面ID到缓存项数量的映射
     */
    public Map<String, Integer> getPageCacheStats() {
        return memoryCacheManager.getPageCacheStats();
    }

    /**
     * 获取组合响应的字符串格式
     *
     * @param pageId         页面ID
     * @param combinationKey 组合键
     * @return 响应字符串，如果不存在则返回null
     */
    public String getCombinationResponse(String pageId, String combinationKey) {
        return memoryCacheManager.getCombinationResponse(pageId, combinationKey);
    }

}
