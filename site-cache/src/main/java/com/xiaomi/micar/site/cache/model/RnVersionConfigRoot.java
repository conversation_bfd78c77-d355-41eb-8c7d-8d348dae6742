package com.xiaomi.micar.site.cache.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * RN版本配置根对象
 * 支持有 pages 包装的 JSON 格式：{"pages": {"pageId": [RnVersionConfig]}}
 * 例如：{"pages": {"explore": [{"rnVersion": "v2.1", "batchId": "batch_xxx"}]}}
 */
@Data
public class RnVersionConfigRoot {
    private String version;
    private String lastUpdateTime;

    // pages 包装：{"pages": {"pageId": [RnVersionConfig]}}
    // 例如：{"pages": {"explore": [{"rnVersion": "v2.1", "batchId": "batch_xxx"}]}}
    private Map<String, List<RnVersionConfig>> pages;

}
