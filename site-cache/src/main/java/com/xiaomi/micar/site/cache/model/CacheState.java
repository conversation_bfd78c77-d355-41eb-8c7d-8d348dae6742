package com.xiaomi.micar.site.cache.model;

import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存状态类
 * 包含缓存构建过程中的所有状态信息
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Getter
public class CacheState {
    /**
     * 页面配置映射（构建过程中使用）
     * 页面ID -> 页面配置模型
     */
    private final Map<String, PageConfigModel> pageConfigs;

    /**
     * 页面响应对象映射（构建过程中使用）
     * 页面ID -> (组合键 -> 响应对象)
     */
    private final Map<String, Map<String, PageRespV2>> pageResponseObjects;

    /**
     * 构造函数
     */
    public CacheState() {
        this.pageConfigs = new ConcurrentHashMap<>();
        this.pageResponseObjects = new ConcurrentHashMap<>();
    }



    /**
     * 获取所有页面ID
     *
     * @return 所有页面ID集合
     */
    public Set<String> getAllPageIds() {
        return pageConfigs.keySet();
    }

    /**
     * 获取页面配置
     */
    public PageConfigModel getPageConfig(String pageId) {
        return pageId != null ? pageConfigs.get(pageId) : null;
    }

    /**
     * 设置页面配置
     */
    public void setPageConfig(String pageId, PageConfigModel pageConfigModel) {
        if (pageId != null && pageConfigModel != null) {
            pageConfigs.put(pageId, pageConfigModel);
        }
    }

    /**
     * 设置组合响应（对象格式）
     */
    public void setCombinationResponse(String pageId, String combinationKey, PageRespV2 pageResp) {
        if (pageId == null || combinationKey == null || pageResp == null) {
            return;
        }

        pageResponseObjects.computeIfAbsent(pageId, k -> new ConcurrentHashMap<>())
                .put(combinationKey, pageResp);
    }

    /**
     * 获取所有页面的响应对象映射
     */
    public Map<String, Map<String, PageRespV2>> getAllPageResponseObjects() {
        return new HashMap<>(pageResponseObjects);
    }

    /**
     * 获取页面数量
     *
     * @return 页面数量
     */
    public int getPageCount() {
        return pageConfigs.size();
    }

    /**
     * 检查缓存状态是否为空
     *
     * @return 是否为空
     */
    public boolean isEmpty() {
        return pageConfigs.isEmpty();
    }

    /**
     * 获取总的缓存项数量
     *
     * @return 总缓存项数量
     */
    public int getTotalCacheItems() {
        return pageResponseObjects.values().stream()
                .mapToInt(Map::size)
                .sum();
    }

    @Override
    public String toString() {
        return String.format("CacheState{ pageCount=%d, totalItems=%d}", getPageCount(), getTotalCacheItems());
    }
}
