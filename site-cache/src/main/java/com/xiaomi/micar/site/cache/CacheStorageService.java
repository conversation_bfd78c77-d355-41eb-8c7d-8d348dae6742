package com.xiaomi.micar.site.cache;

import com.xiaomi.micar.site.model.PageRespV2;

import java.util.Map;

/**
 * 缓存存储服务接口
 * 定义了缓存存储的通用操作，支持不同的存储实现（MySQL、HBase、Redis等）
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
public interface CacheStorageService {

    /**
     * 保存页面缓存
     *
     * @param batchId       批次ID
     * @param pageId        页面ID
     * @param responseCache 页面响应缓存Map
     * @param configVersion 配置版本
     * @return 批次ID，如果保存失败返回null
     */
    String savePageCache(String batchId, String pageId, Map<String, PageRespV2> responseCache, String configVersion);

    /**
     * 加载页面缓存
     *
     * @param pageId 页面ID
     * @param batchId 批次ID
     * @return 页面响应缓存Map，如果加载失败返回null
     */
    Map<String, String> loadPageCache(String pageId, String batchId);
}
