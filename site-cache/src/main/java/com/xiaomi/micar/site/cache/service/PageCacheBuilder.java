package com.xiaomi.micar.site.cache.service;

import com.xiaomi.micar.site.cache.model.CacheBuilderResult;
import com.xiaomi.micar.site.cache.model.CacheState;
import com.xiaomi.micar.site.cache.model.PageCacheContext;
import com.xiaomi.micar.site.enums.StrategyStateEnum;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;

import lombok.Getter;
import lombok.Setter;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 页面缓存构建器
 * 负责构建页面组合缓存
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class PageCacheBuilder {

    /**
     * 默认用户策略常量
     */
    private static final String DEFAULT_USER_STRATEGY = "all";

    @Getter
    @Setter
    @Builder
    public static class CacheContext {
        private CacheState cacheState;
        private Function<PageCacheContext, PageRespV2> pageRespBuilder;
        private TriFunction<String, String, List<Map<String, String>>,String> combinationKeyGenerator;
    }

    @FunctionalInterface
    public interface TriFunction<T, U, V, R> {
        R apply(T t, U u, V v);
    }

    public PageCacheBuilder() {
        // 无需依赖，通过函数式接口解耦
    }

    /**
     * 构建页面组合缓存
     */
    public CacheBuilderResult buildPageCombinationCache(CacheContext context) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            if (context == null) {
                throw new IllegalArgumentException("Context cannot be null");
            }
            if (context.getCacheState() == null) {
                throw new IllegalArgumentException("CacheState cannot be null");
            }
            if (context.getPageRespBuilder() == null) {
                throw new IllegalArgumentException("PageRespBuilder cannot be null");
            }
            if (context.getCombinationKeyGenerator() == null) {
                throw new IllegalArgumentException("CombinationKeyGenerator cannot be null");
            }

            CacheState cacheState = context.getCacheState();
            AtomicInteger totalCombinations = new AtomicInteger(0);
            int pageCount = cacheState.getPageCount();
            List<String> allPageIds = new ArrayList<>(cacheState.getAllPageIds());

            // IO密集型操作，线程数量和页面数量一致
            int threadPoolSize = allPageIds.size();
            log.info("开始并行构建缓存，页面数量: {}, 线程池大小: {}", allPageIds.size(), threadPoolSize);

            ExecutorService executor = Executors.newFixedThreadPool(threadPoolSize);
            try {
                // 并行处理所有页面，保持逻辑不变但提升性能
                List<CompletableFuture<Void>> futures = allPageIds.stream()
                        .map(pageId -> CompletableFuture.runAsync(() -> {
                            PageConfigModel pageConfig = cacheState.getPageConfig(pageId);
                            if (pageConfig == null) {
                                log.warn("页面配置为空，跳过: {}", pageId);
                                return;
                            }

                            // 根据人群策略状态决定使用哪些groupKeys
                            List<String> targetGroupKeys = getTargetGroupKeys(pageConfig);

                            // 为当前页面创建组件去重标记（页面级别共享）
                            Set<String> pageProcessedComponents = ConcurrentHashMap.newKeySet();

                            for (String groupKey : targetGroupKeys) {
                                try {
                                    PageCacheContext pageCacheContext = new PageCacheContext(
                                        pageConfig, pageId, groupKey, new ArrayList<>(), pageProcessedComponents);

                                    PageRespV2 pageResp = context.getPageRespBuilder().apply(pageCacheContext);

                                    if (pageResp != null) {
                                        String combinationKey = context.getCombinationKeyGenerator().apply(pageId, groupKey, new ArrayList<>());
                                        cacheState.setCombinationResponse(pageId, combinationKey, pageResp);
                                        totalCombinations.incrementAndGet();
                                    } else {
                                        log.warn("页面响应为空，跳过缓存: pageId={}, groupKey={}", pageId, groupKey);
                                    }

                                } catch (Exception e) {
                                    log.error("构建页面缓存失败: pageId={}, groupKey={}, error={}", pageId, groupKey, e.getMessage());
                                }
                            }
                        }, executor))
                        .collect(Collectors.toList());

                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            } finally {
                executor.shutdown();
            }
            stopWatch.stop();
            int finalTotalCombinations = totalCombinations.get();
            log.info("缓存构建完成，耗时 {} ms，总数: {}", stopWatch.getTotalTimeMillis(), finalTotalCombinations);

            return CacheBuilderResult.builder()
                    .success(true)
                    .totalCachedCombinations(finalTotalCombinations)
                    .estimatedTotalCombinations(finalTotalCombinations)
                    .pageCount(pageCount)
                    .processingTimeMs(stopWatch.getTotalTimeMillis())
                    .lazyLoadingEnabled(false)
                    .build();

        } catch (Exception e) {
            log.error("缓存构建失败: {}", e.getMessage(), e);
            return buildErrorResult(stopWatch, e.getMessage());
        }
    }



    /**
     * 根据页面配置获取目标用户组Keys
     * 首先检查人群策略状态，如果未开启则返回默认分组
     * 如果已开启，则从 PageConfigModel 的 groupConfig 中获取实际配置的用户组
     * 如果 groupConfig 为空，则返回默认的 ALL 分组
     */
    private List<String> getTargetGroupKeys(PageConfigModel pageConfig) {
        // 如果页面配置为空，返回默认分组
        if (pageConfig == null) {
            return Arrays.asList(DEFAULT_USER_STRATEGY);
        }

        // 检查人群策略状态，如果未开启，则直接返回默认分组
        if (!StrategyStateEnum.isEnabled(pageConfig.getStrategyState())) {
            log.debug("页面人群策略未开启，使用默认 ALL 分组进行缓存，strategyState: {}", pageConfig.getStrategyState());
            return Arrays.asList(DEFAULT_USER_STRATEGY);
        }

        // 如果 groupConfig 为空，返回默认分组
        if (pageConfig.getGroupConfig() == null || pageConfig.getGroupConfig().isEmpty()) {
            log.debug("页面 groupConfig 为空，使用默认 ALL 分组进行缓存");
            return Arrays.asList(DEFAULT_USER_STRATEGY);
        }

        // 从 groupConfig 中提取所有的 strategy 值
        List<String> groupKeys = pageConfig.getGroupConfig().stream()
                .map(PageConfigModel.GroupConfigModel::getStrategy)
                .filter(strategy -> strategy != null && !strategy.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());

        // 如果提取出的 groupKeys 为空，返回默认分组
        if (groupKeys.isEmpty()) {
            log.debug("页面 groupConfig 中没有有效的 strategy，使用默认 ALL 分组进行缓存");
            return Arrays.asList(DEFAULT_USER_STRATEGY);
        }

        log.debug("页面人群策略已开启，使用配置的用户组进行缓存，groupKeys: {}", groupKeys);
        return groupKeys;
    }


    private CacheBuilderResult buildErrorResult(StopWatch stopWatch, String errorMessage) {
        stopWatch.stop();
        return CacheBuilderResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .processingTimeMs(stopWatch.getTotalTimeMillis())
                .build();
    }
}
