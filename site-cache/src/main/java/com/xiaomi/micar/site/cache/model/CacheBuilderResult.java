package com.xiaomi.micar.site.cache.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 缓存构建结果类，用于封装缓存构建过程的结果信息.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Builder
public class CacheBuilderResult {

    /**
     * 是否成功
     */
    private final boolean success;

    /**
     * 总缓存组合数量
     */
    private final int totalCachedCombinations;

    /**
     * 估计的总组合数量
     */
    private final int estimatedTotalCombinations;

    /**
     * 页面数量
     */
    private final int pageCount;

    /**
     * 处理时间（毫秒）
     */
    private final long processingTimeMs;

    /**
     * 是否启用了懒加载
     */
    private final boolean lazyLoadingEnabled;

    /**
     * 错误信息
     */
    private final String errorMessage;

    @Override
    public String toString() {
        return String.format(
                "CacheBuilderResult{success=%s, totalCached=%d, estimated=%d, pages=%d, timeMs=%d, lazyLoading=%s, error='%s'}",
                success, totalCachedCombinations, estimatedTotalCombinations, pageCount, 
                processingTimeMs, lazyLoadingEnabled, errorMessage != null ? errorMessage : "");
    }
}
