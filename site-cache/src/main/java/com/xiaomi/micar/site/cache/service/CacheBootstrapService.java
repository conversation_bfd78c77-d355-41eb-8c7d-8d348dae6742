package com.xiaomi.micar.site.cache.service;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.cache.model.RnVersionConfigRoot;
import com.xiaomi.micar.site.cache.model.RnVersionConfig;

import io.opentelemetry.extension.annotations.WithSpan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 缓存启动服务
 * 负责应用启动时的本地缓存初始化，包括：
 * 1. Nacos配置监听（批次通知、历史快照配置）
 * 2. 页面策略状态缓存初始化
 * 3. 其他需要预先缓存到本地的数据
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Slf4j
@Component
public class CacheBootstrapService {

    @Resource
    private ConfigService configService;

    @Resource
    private CacheSyncService cacheSyncService;

    @Resource
    private MemoryCacheManager memoryCacheManager;

    @Value("${nacos.config.group:DEFAULT_GROUP}")
    private String nacosGroup;

    @Value("${site.cache.nacos.batch-notification-data-id}")
    private String batchNotificationDataId;

    @Value("${site.cache.nacos.historical-snapshot-data-id}")
    private String historicalSnapshotDataId;

    private static final int MAX_RETRY_COUNT = 3;
    private static final long RETRY_DELAY_MS = 1000;

    @PostConstruct
    public void initialize() {
        log.info("初始化Nacos配置监听服务和缓存");
        try {
            // 初始化Nacos配置监听
            registerBatchNotificationListener();
            registerHistoricalSnapshotConfigListener();
            loadInitialBatchNotificationConfig();
            loadInitialHistoricalSnapshotConfig();

            log.info("缓存启动服务初始化完成");
        } catch (Exception e) {
            log.error("Nacos配置监听服务和缓存初始化失败", e);
            throw new RuntimeException("初始化失败", e);
        }
    }

    private void registerBatchNotificationListener() {
        try {
            configService.addListener(batchNotificationDataId, nacosGroup, new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        handleBatchNotification(configInfo);
                    } catch (Exception e) {
                        log.error("批次通知处理失败", e);
                    }
                }
            });
            log.info("批次通知监听器注册成功");
        } catch (Exception e) {
            log.error("批次通知监听器注册失败", e);
            throw new RuntimeException("批次通知监听器注册失败", e);
        }
    }

    private void registerHistoricalSnapshotConfigListener() {
        try {
            configService.addListener(historicalSnapshotDataId, nacosGroup, new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        handleHistoricalSnapshotConfigChange(configInfo);
                    } catch (Exception e) {
                        log.error("历史快照配置处理失败", e);
                    }
                }
            });
            log.info("历史快照监听器注册成功");
        } catch (Exception e) {
            log.error("历史快照监听器注册失败", e);
            throw new RuntimeException("历史快照监听器注册失败", e);
        }
    }

    private void loadInitialBatchNotificationConfig() throws Exception {
        String configInfo = configService.getConfig(batchNotificationDataId, nacosGroup, 5000);
        if (StringUtils.isBlank(configInfo)) {
            log.warn("批次通知初始配置为空，跳过初始化（首次部署时正常）");
            return;
        }
        handleBatchNotification(configInfo);
        log.info("批次通知初始配置加载完成");
    }

    private void loadInitialHistoricalSnapshotConfig() throws Exception {
        String configInfo = configService.getConfig(historicalSnapshotDataId, nacosGroup, 5000);
        if (StringUtils.isBlank(configInfo)) {
            log.warn("历史快照初始配置为空，跳过初始化（首次部署时正常）");
            return;
        }
        handleHistoricalSnapshotConfigChange(configInfo);
        log.info("历史快照初始配置加载完成");
    }

    /**
     * 处理批次通知
     */
    @WithSpan
    private void handleBatchNotification(String configInfo) {
        @SuppressWarnings("unchecked")
        Map<String, String> pageBatchMap = JsonUtil.parseObject(configInfo, Map.class);
        if (pageBatchMap == null) {
            throw new RuntimeException("批次通知配置解析失败");
        }

        if (pageBatchMap.isEmpty()) {
            log.info("批次通知配置为空，跳过处理（首次部署或清空配置时正常）");
            return;
        }

        log.info("处理批次通知，页面数={}", pageBatchMap.size());
        cacheSyncService.batchSyncCacheFromStorage(pageBatchMap);
    }

    /**
     * 处理历史快照配置变更
     */
    @WithSpan
    private void handleHistoricalSnapshotConfigChange(String configInfo) {
        RnVersionConfigRoot configRoot = JsonUtil.parseObject(configInfo, RnVersionConfigRoot.class);
        if (configRoot == null || configRoot.getPages() == null) {
            throw new RuntimeException("历史快照配置解析失败");
        }

        Map<String, List<RnVersionConfig>> pages = configRoot.getPages();
        log.info("处理历史快照配置变更，页面数={}", pages.size());
        cacheSyncService.loadHistoricalSnapshotCacheFromStorage(pages);
        log.info("历史快照配置更新完成，缓存条目数={}", memoryCacheManager.getHistoricalSnapshotCacheSize());
    }

    /**
     * 发布批次通知
     */
    public boolean publishBatchNotification(Map<String, String> pageBatchMap) {
        try {
            String notificationContent = JsonUtil.toJSONString(pageBatchMap);
            boolean published = publishConfigToNacos(batchNotificationDataId, notificationContent);

            if (published) {
                log.info("批次通知发布成功，页面数={}", pageBatchMap.size());
            } else {
                log.error("批次通知发布失败");
            }
            return published;
        } catch (Exception e) {
            log.error("批次通知发布异常", e);
            return false;
        }
    }

    private boolean publishConfigToNacos(String dataId, String content) {
        int retryCount = 0;
        boolean published = false;
        Exception lastException = null;

        while (retryCount < MAX_RETRY_COUNT && !published) {
            try {
                if (retryCount > 0) {
                    log.info("重试发布配置，第{}次", retryCount + 1);
                }

                published = configService.publishConfig(dataId, nacosGroup, content);

                if (!published) {
                    retryCount++;
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                }
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                log.warn("配置发布异常，重试第{}次", retryCount);

                try {
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("发布过程被中断");
                    break;
                }
            }
        }

        if (!published && lastException != null) {
            log.error("配置发布失败，已达最大重试次数", lastException);
        }

        return published;
    }



}
