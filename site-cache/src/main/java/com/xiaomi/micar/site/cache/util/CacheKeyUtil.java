package com.xiaomi.micar.site.cache.util;

/**
 * 缓存键工具类
 * 统一处理版本前缀的添加和解析逻辑
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
public class CacheKeyUtil {

    /**
     * 版本前缀分隔符
     */
    private static final String VERSION_SEPARATOR = "_";

    /**
     * 版本前缀格式：V{version}_
     */
    private static final String VERSION_PREFIX_FORMAT = "V%d" + VERSION_SEPARATOR;

    /**
     * 为组合键添加版本前缀
     *
     * @param combinationKey 原始组合键
     * @param version        版本号（1=V1版本，2=V2版本）
     * @return 带版本前缀的组合键，格式：V{version}_{combinationKey}
     */
    public static String addVersionPrefix(String combinationKey, Integer version) {
        if (combinationKey == null) {
            return null;
        }
        if (version == null) {
            return combinationKey;
        }

        String versionPrefix = String.format(VERSION_PREFIX_FORMAT, version);
        return versionPrefix + combinationKey;
    }

    /**
     * 将数据库版本号转换为API版本字符串
     * 统一的版本转换逻辑，供所有服务使用
     *
     * @param version 数据库版本号（1=V1版本，2=V2版本）
     * @return API版本字符串（V1或V2）
     */
    public static String convertVersionToApiVersion(Integer version) {
        if (version == null) {
            return "V2"; // 默认使用V2
        }

        switch (version) {
            case 1:
                return "V1";
            case 2:
                return "V2";
            default:
                return "V2"; // 未知版本默认使用V2
        }
    }

    /**
     * 历史快照缓存键分隔符
     */
    private static final String HISTORICAL_CACHE_KEY_SEPARATOR = "|";

    /**
     * 生成历史快照缓存键
     * 格式：combinationKey:clientVersion:apiVersion
     *
     * @param combinationKey 组合键（如：page:explore|group:car_owner）
     * @param clientVersion  客户端版本（如：v2.1）
     * @param apiVersion     API版本（如：V1, V2）
     * @return 历史快照缓存键
     */
    public static String generateHistoricalSnapshotCacheKey(String combinationKey, String clientVersion, String apiVersion) {
        if (combinationKey == null || clientVersion == null || apiVersion == null) {
            return null;
        }
        return String.join(HISTORICAL_CACHE_KEY_SEPARATOR, combinationKey, clientVersion, apiVersion);
    }

}
