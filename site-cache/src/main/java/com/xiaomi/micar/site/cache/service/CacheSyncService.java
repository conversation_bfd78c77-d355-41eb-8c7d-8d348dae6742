package com.xiaomi.micar.site.cache.service;

import com.xiaomi.micar.site.cache.CacheStorageService;
import com.xiaomi.micar.site.cache.model.CacheState;
import com.xiaomi.micar.site.cache.model.RnVersionConfig;
import com.xiaomi.micar.site.cache.util.HistoricalSnapshotCacheKeyGenerator;
import com.xiaomi.micar.site.dao.SitePageCacheStorageEngine;
import com.xiaomi.micar.site.dao.entity.SitePageCacheStorageEntity;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import com.xiaomi.micar.site.util.CombinationKeyParser;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存同步服务
 * 负责缓存数据的存储同步和通知发布
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Slf4j
@Service
public class CacheSyncService {

    @Resource
    private CacheStorageService cacheStorageService;

    @Resource
    @Lazy
    private CacheBootstrapService cacheBootstrapService;

    @Resource
    private MemoryCacheManager memoryCacheManager;

    @Resource
    private SitePageCacheStorageEngine cacheStorageEngine;

    @Resource
    private PageStrategyStateCache pageStrategyStateCache;

    /**
     * 批量同步缓存到本地内存
     */
    public void batchSyncCacheFromStorage(Map<String, String> pageBatchMap) {
        log.info("开始批量同步缓存，页面数量={}", pageBatchMap.size());

        // 1. 获取当前内存中的所有页面ID
        Set<String> currentPageIds = memoryCacheManager.getAllPageIds();
        
        // 2. 获取需要同步的页面ID
        Set<String> syncPageIds = pageBatchMap.keySet();
        
        // 3. 找出需要删除的过期页面（在内存中但不在同步列表中）
        Set<String> obsoletePageIds = new HashSet<>(currentPageIds);
        obsoletePageIds.removeAll(syncPageIds);
        
        // 4. 更新存在的页面缓存
        pageBatchMap.forEach((pageId, batchId) -> {
            Map<String, String> responseCache = cacheStorageService.loadPageCache(pageId, batchId);

            if (responseCache == null || responseCache.isEmpty()) {
                throw new RuntimeException(String.format("缓存数据未找到: pageId=%s, batchId=%s", pageId, batchId));
            }

            // 动态更新页面策略状态信息
            updatePageStrategyState(pageId, responseCache);

            boolean success = memoryCacheManager.swapPageCache(pageId, responseCache);
            if (!success) {
                throw new RuntimeException(String.format("内存缓存更新失败: pageId=%s, batchId=%s", pageId, batchId));
            }

            log.info("缓存同步成功: pageId={}, batchId={}, 组合数={}", pageId, batchId, responseCache.size());
        });

        // 5. 清理过期页面缓存
        if (!obsoletePageIds.isEmpty()) {
            int removedCount = memoryCacheManager.removeObsoletePageCaches(obsoletePageIds);
            log.info("清理过期页面缓存: 发现{}个过期页面，成功清理{}个", obsoletePageIds.size(), removedCount);
        }

        log.info("批量同步缓存完成，页面数量={}，清理过期页面数量={}", pageBatchMap.size(), obsoletePageIds.size());
    }

    /**
     * 同步缓存到存储（支持配置版本）
     */
    public int syncCacheToStorageWithConfigVersion(CacheState cacheState, Map<String, Map<String, PageRespV2>> pageCacheMap) {
        String batchId = generateBatchId();
        Map<String, String> pageBatchMap = new HashMap<>();
        int successCount = 0;

        // 保存缓存到存储
        for (Map.Entry<String, Map<String, PageRespV2>> entry : pageCacheMap.entrySet()) {
            String pageId = entry.getKey();
            Map<String, PageRespV2> responseCache = entry.getValue();
            String configVersion = getConfigVersion(cacheState, pageId);

            if (savePageCacheToStorage(batchId, pageId, responseCache, configVersion)) {
                pageBatchMap.put(pageId, batchId);
                successCount++;
            }
        }

        // 发布通知
        publishNotification(pageBatchMap);

        int failedCount = pageCacheMap.size() - successCount;
        log.info("缓存同步完成，总数={}, 成功={}, 失败={}", pageCacheMap.size(), successCount, failedCount);

        return successCount;
    }

    /**
     * 获取页面的配置版本
     */
    private String getConfigVersion(CacheState cacheState, String pageId) {
        if (cacheState == null) {
            return null;
        }

        PageConfigModel pageConfigModel = cacheState.getPageConfig(pageId);
        return pageConfigModel != null ? pageConfigModel.getVersion() : null;
    }

    /**
     * 保存页面缓存到存储
     */
    private boolean savePageCacheToStorage(String batchId, String pageId,
                                         Map<String, PageRespV2> responseCache,
                                         String configVersion) {
        try {
            String result = cacheStorageService.savePageCache(batchId, pageId, responseCache, configVersion);

            if (result != null) {
                log.info("缓存保存成功: pageId={}, batchId={}, configVersion={}", pageId, batchId, configVersion);
                return true;
            } else {
                log.error("缓存保存失败: pageId={}", pageId);
                return false;
            }
        } catch (Exception e) {
            log.error("缓存保存异常: pageId={}", pageId, e);
            return false;
        }
    }

    /**
     * 发布批次通知
     */
    private void publishNotification(Map<String, String> pageBatchMap) {
        try {
            boolean notified = cacheBootstrapService.publishBatchNotification(pageBatchMap);
            if (notified) {
                log.info("通知发布成功，页面数量={}", pageBatchMap.size());
            } else {
                log.error("通知发布失败，页面数量={}", pageBatchMap.size());
            }
        } catch (Exception e) {
            log.error("通知发布异常，页面数量={}", pageBatchMap.size(), e);
        }
    }

    /**
     * 生成批次ID
     */
    public String generateBatchId() {
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 1000);
        return String.format("batch_%d_%03d", timestamp, random);
    }

    /**
     * 加载历史快照缓存数据
     */
    public void loadHistoricalSnapshotCacheData(String pageId, RnVersionConfig config, Map<String, String> targetCacheMap) {
        try {
            List<SitePageCacheStorageEntity> entities = cacheStorageEngine.getByBatchId(config.getBatchId());
            if (entities == null || entities.isEmpty()) {
                throw new RuntimeException(String.format("历史快照数据未找到: pageId=%s, batchId=%s", pageId, config.getBatchId()));
            }

            int loadedCount = 0;
            for (SitePageCacheStorageEntity entity : entities) {
                try {
                    // 使用统一的缓存键生成器
                    String finalCacheKey = HistoricalSnapshotCacheKeyGenerator.generateForStorage(
                            entity.getCombinationKey(), config.getRnVersion(), entity.getVersion());

                    if (finalCacheKey != null) {
                        targetCacheMap.put(finalCacheKey, entity.getConfig());
                        loadedCount++;
                    } else {
                        log.warn("生成历史快照缓存键失败: entityId={}, combinationKey={}, rnVersion={}, version={}",
                                entity.getId(), entity.getCombinationKey(), config.getRnVersion(), entity.getVersion());
                    }
                } catch (Exception e) {
                    log.error("历史快照条目处理失败: entityId={}, combinationKey={}", entity.getId(), entity.getCombinationKey(), e);
                }
            }

            log.info("历史快照加载成功: pageId={}, batchId={}, 条目数={}", pageId, config.getBatchId(), loadedCount);

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(String.format("历史快照加载异常: pageId=%s, batchId=%s", pageId, config.getBatchId()), e);
        }
    }

    /**
     * 加载历史快照缓存到本地内存
     */
    public void loadHistoricalSnapshotCacheFromStorage(Map<String, List<RnVersionConfig>> pages) {
        log.info("开始加载历史快照缓存，页面配置数: {}", pages.size());
        Map<String, String> newCacheMap = new ConcurrentHashMap<>();

        for (Map.Entry<String, List<RnVersionConfig>> pageEntry : pages.entrySet()) {
            String pageId = pageEntry.getKey();
            List<RnVersionConfig> configs = pageEntry.getValue();

            if (configs == null || configs.isEmpty()) {
                log.warn("页面配置为空，跳过处理: pageId={}", pageId);
                continue;
            }

            for (RnVersionConfig config : configs) {
                if (config == null || StringUtils.isBlank(config.getBatchId())) {
                    log.warn("版本配置无效，跳过处理: pageId={}, config={}", pageId, config);
                    continue;
                }
                loadHistoricalSnapshotCacheData(pageId, config, newCacheMap);
            }
        }

        boolean updated = memoryCacheManager.swapHistoricalSnapshotCache(newCacheMap);
        if (!updated) {
            throw new RuntimeException("历史快照内存缓存更新失败");
        }

        log.info("历史快照缓存加载完成");
    }

    /**
     * 动态更新页面策略状态信息
     * 根据缓存数据分析页面支持的策略，并更新到策略状态缓存中
     *
     * @param pageId        页面ID
     * @param responseCache 页面响应缓存数据
     */
    private void updatePageStrategyState(String pageId, Map<String, String> responseCache) {
        try {
            // 从缓存键中提取策略信息
            Set<String> availableStrategies = extractStrategiesFromCacheKeys(responseCache.keySet());

            // 直接更新页面策略集合
            pageStrategyStateCache.updatePageStrategies(pageId, availableStrategies);

            log.debug("页面策略集合更新: pageId={}, strategies={}", pageId, availableStrategies);

        } catch (Exception e) {
            log.error("更新页面策略集合失败: pageId={}, error={}", pageId, e.getMessage(), e);
        }
    }

    /**
     * 从缓存键中提取策略信息
     * 缓存键格式为: V{version}_{pageId}|{strategy}|exp:{experimentId}:{bucketId}
     * 或者简化格式: V{version}_{pageId}|{strategy}
     *
     * @param cacheKeys 缓存键集合
     * @return 策略集合
     */
    private Set<String> extractStrategiesFromCacheKeys(Set<String> cacheKeys) {
        Set<String> strategies = new HashSet<>();

        for (String cacheKey : cacheKeys) {
            try {
                // 移除版本前缀 (V1_ 或 V2_)
                String keyWithoutVersion = cacheKey;
                if (cacheKey.startsWith("V1_") || cacheKey.startsWith("V2_")) {
                    keyWithoutVersion = cacheKey.substring(3);
                }

                // 解析组合键格式: pageId|routeCode(|exp:...)
                String strategy = CombinationKeyParser.extractRouteCode(keyWithoutVersion);
                if (strategy != null && !strategy.trim().isEmpty()) {
                    strategies.add(strategy);
                }
            } catch (Exception e) {
                log.debug("解析缓存键失败: cacheKey={}, error={}", cacheKey, e.getMessage());
            }
        }

        // 如果没有提取到策略，默认为 "all"
        if (strategies.isEmpty()) {
            strategies.add("all");
        }

        log.debug("从缓存键中提取到策略: cacheKeys={}, strategies={}", cacheKeys, strategies);
        return strategies;
    }
}
