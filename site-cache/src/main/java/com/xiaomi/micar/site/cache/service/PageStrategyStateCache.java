package com.xiaomi.micar.site.cache.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 页面人群策略状态缓存
 * 动态维护每个页面支持的策略集合
 *
 * <AUTHOR>
 * @since 2025/06/29
 */
@Slf4j
@Service
public class PageStrategyStateCache {

    /**
     * 页面策略集合缓存
     * Key: pageId (页面ID)
     * Value: Set<String> (该页面支持的所有策略集合)
     */
    private volatile Map<String, Set<String>> pageStrategiesCache = new ConcurrentHashMap<>();

    /**
     * 默认策略集合（当页面不存在时的默认值）
     */
    private static final Set<String> DEFAULT_STRATEGIES = new HashSet<String>() {{
        add("all");
    }};
    
    /**
     * 检查页面是否开启了人群策略
     * @param pageId 页面ID
     * @return true=开启人群策略, false=关闭人群策略
     */
    public boolean isStrategyEnabled(String pageId) {
        if (pageId == null || pageId.trim().isEmpty()) {
            return true; // 默认开启策略
        }

        Set<String> strategies = getPageStrategies(pageId);
        // 如果有多个策略，或者单个策略不是"all"，则认为开启了策略
        return strategies.size() > 1 || (strategies.size() == 1 && !strategies.contains("all"));
    }

    /**
     * 获取页面支持的所有策略
     * @param pageId 页面ID
     * @return 策略集合
     */
    public Set<String> getPageStrategies(String pageId) {
        if (pageId == null || pageId.trim().isEmpty()) {
            return new HashSet<>(DEFAULT_STRATEGIES);
        }

        return new HashSet<>(pageStrategiesCache.getOrDefault(pageId, DEFAULT_STRATEGIES));
    }
    
    /**
     * 初始化缓存（已废弃，现在通过动态更新维护）
     * @param strategyStates 页面策略状态映射
     * @deprecated 现在通过 updatePageStrategies 动态维护策略集合
     */
    @Deprecated
    public void initializeCache(Map<String, Boolean> strategyStates) {
        log.info("initializeCache 方法已废弃，现在通过动态更新维护策略集合");
    }

    /**
     * 动态更新页面策略集合
     * @param pageId 页面ID
     * @param strategies 策略集合
     */
    public void updatePageStrategies(String pageId, Set<String> strategies) {
        if (pageId == null || pageId.trim().isEmpty()) {
            log.warn("页面ID为空，跳过策略集合更新");
            return;
        }

        if (strategies == null || strategies.isEmpty()) {
            strategies = new HashSet<>(DEFAULT_STRATEGIES);
        }

        Set<String> oldStrategies = pageStrategiesCache.put(pageId, new HashSet<>(strategies));

        if (oldStrategies == null || !oldStrategies.equals(strategies)) {
            log.info("页面策略集合已更新: pageId={}, {} -> {}",
                    pageId, oldStrategies, strategies);
        }
    }
}
