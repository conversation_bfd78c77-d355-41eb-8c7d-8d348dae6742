package com.xiaomi.micar.site.cache.policy;

import lombok.Getter;

/**
 * 写计划定义（最小实现）
 */
@Getter
public class WritePlan {

    public enum Mode { MEMORY_ONLY, REDIS_ONLY, MYSQL_ONLY }

    private final Mode mode;

    private WritePlan(Mode mode) {
        this.mode = mode;
    }

    public static WritePlan memoryOnly() {
        return new WritePlan(Mode.MEMORY_ONLY);
    }

    public static WritePlan redisOnly() {
        return new WritePlan(Mode.REDIS_ONLY);
    }

    public static WritePlan mysqlOnly() {
        return new WritePlan(Mode.MYSQL_ONLY);
    }
}

