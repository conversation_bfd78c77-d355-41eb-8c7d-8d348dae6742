# 站点组件配置示例
iccc:
  site:
    component:
      # 是否启用组件功能，默认为true
      enabled: true
      # 是否启用缓存，默认为true
      cache-enabled: true
      # 缓存刷新间隔（毫秒），默认为600000（10分钟）
      cache-refresh-interval: 600000
      # 缓存配置
      cache:
        # 是否启用本地缓存，默认为true
        local-cache-enabled: true
        # 本地缓存最大数量，默认为1000
        local-cache-max-size: 1000
      # 线程池配置
      thread-pool:
        # 是否启用并行处理，默认为true
        parallel-processing-enabled: true
        # 核心线程数，默认为可用处理器数量
        core-pool-size: 4
        # 最大线程数，默认为可用处理器数量 * 2
        max-pool-size: 8
        # 线程池队列容量，默认为500
        queue-capacity: 500
        # 线程池保持活跃时间（秒），默认为60秒
        keep-alive-seconds: 60
        # 批处理大小，每批处理的页面数量，默认为10
        batch-size: 10