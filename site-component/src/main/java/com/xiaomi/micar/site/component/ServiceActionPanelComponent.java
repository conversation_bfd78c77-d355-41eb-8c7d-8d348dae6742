package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.element.MediaElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * ChargingService 充电服务组件
 * 支持头部横幅（header）、额外操作（extra）和快捷入口（actions）。
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ServiceActionPanelComponent extends BaseComponent implements Component {

    /** 标题 */
    private String title;

    /** 额外操作按钮（如：了解详情） */
    private ButtonElement extra;

    /** 头部媒体区（如：充电网络介绍） */
    private MediaElement header;

    /** 快捷操作入口（附近充电桩、扫码充电等） */
    private List<MediaElement> actions;

    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return header != null || (actions != null && !actions.isEmpty());
    }
}
