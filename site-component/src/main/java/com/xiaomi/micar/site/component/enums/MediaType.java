package com.xiaomi.micar.site.component.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 媒体类型
 */
@Getter
@AllArgsConstructor
public enum MediaType {

    image,
    video;

    public String getCode() {
        return name();
    }

    public static MediaType getByCode(String code) {
        for (MediaType mediaType : MediaType.values()) {
            if (mediaType.getCode().equals(code)) {
                return mediaType;
            }
        }
        throw new UnsupportedOperationException("不支持的媒体类型：" + code);
    }
}

