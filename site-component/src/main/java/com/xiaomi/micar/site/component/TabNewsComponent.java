package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * TabNews多标签图文组件实现
 * 支持单内容和列表内容两种类型
 * 数据来源：运营配置
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TabNewsComponent extends TabsComponent<NewsComponent> implements Component {

    /**
     * 获取组件中的社区类型的refId列表
     * 调用父类的实现，递归获取标签内容中的社区链接ID
     *
     * @return 社区类型的refId列表
     */
    @Override
    @JsonIgnore
    public List<String> getCommunityRefIds() {
        // 调用父类的实现
        return super.getCommunityRefIds();
    }

    /**
     * 检查TabNews组件数据是否已成功填充
     * TabNews组件认为有items字段且items不为空即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return getItems() != null && !getItems().isEmpty();
    }
}
