package com.xiaomi.micar.site.component;


import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Collections;
import java.util.List;

/**
 * 静态站点组件接口
 * 提供基本的组件功能实现
 */
public interface Component {

    String getId();

    void setId(String id);

    /**
     * 埋点：组件id
     *
     * @return 埋点：组件id
     */
    default String getSectionId() {
        return null;
    }

    /**
     * 埋点：组件名称
     *
     * @return 埋点：组件名称
     */
    default String getSectionName() {
        return null;
    }

    default void setSectionId(String sectionId) {
        // nothing
    }

    default void setSectionName(String sectionName) {
        // nothind
    }

    /**
     * 获取组件中的社区类型的refId列表
     * 默认实现返回空列表，各组件类可以根据自身结构进行重写
     *
     * @return 社区类型的refId列表
     */
    @JsonIgnore
    default List<String> getCommunityRefIds() {
        return Collections.emptyList();
    }

    /**
     * 检查组件数据是否已成功填充
     * 默认实现返回true，各组件类可以根据自身数据结构和业务逻辑进行重写
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @JsonIgnore
    default boolean isDataFilled() {
        return true;
    }

}
