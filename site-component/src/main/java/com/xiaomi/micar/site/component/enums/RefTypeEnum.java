package com.xiaomi.micar.site.component.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 链接类型枚举
 */
@Getter
@AllArgsConstructor
public enum RefTypeEnum {

    /**
     * 社区
     */
    COMMUNITY("community", "社区帖子"),

    /**
     * 活动
     */
    ACTIVITY("activity", "活动"),

    H5("h5", "普通H5链接"),

    ROUTER("router", "全栈链接"),
    ;

    private final String code;
    private final String description;

    /**
     * 根据code获取枚举值
     *
     * @param code 代码
     * @return 枚举值，如果不存在则返回null
     */
    public static RefTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (RefTypeEnum refTypeEnum : RefTypeEnum.values()) {
            if (refTypeEnum.getCode().equals(code)) {
                return refTypeEnum;
            }
        }
        return null;
    }
}
