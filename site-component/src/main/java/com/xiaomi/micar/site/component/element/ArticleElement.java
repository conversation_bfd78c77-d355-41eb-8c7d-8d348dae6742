package com.xiaomi.micar.site.component.element;

import com.xiaomi.micar.site.component.model.PublishInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 基础组件：图文
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ArticleElement extends MediaElement implements Element {

    private String summary;

    private PublishInfo publishInfo;

    private List<ButtonElement> buttons;

    /**
     * 浏览次数
     */
    private Integer viewCount;

}
