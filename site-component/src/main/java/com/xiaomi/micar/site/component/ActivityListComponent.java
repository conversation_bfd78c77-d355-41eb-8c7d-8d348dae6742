package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.car.activity.external.api.model.Filter;
import com.xiaomi.car.activity.external.api.resp.ActivityListResp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * ActivityList活动列表组件实现
 * 支持活动列表展示、筛选功能和活动状态显示
 * 数据来源：运营配置
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ActivityListComponent extends BaseComponent implements Component {

    private String id;

    /**
     * 主标题
     */
    private String title;

    /**
     * 筛选条件列表
     */
    private List<Filter> filters;

    /**
     * 活动数据对象，包含分页信息和活动列表
     */
    private ActivityListResp data;

    /**
     * 更多按钮配置
     */
    private ButtonElement more;

    /**
     * 检查ActivityList组件数据是否已成功填充
     * ActivityList组件认为有data字段且data不为null即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return data != null && CollectionUtils.isNotEmpty(data.getRecords());
    }

}
