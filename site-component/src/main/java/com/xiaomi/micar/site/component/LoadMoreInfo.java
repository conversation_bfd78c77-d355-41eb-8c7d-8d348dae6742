package com.xiaomi.micar.site.component;

import lombok.Data;

import java.io.Serializable;

/**
 * 上拉加载更多信息
 * 用于分页数据的版本控制和状态管理
 */
@Data
public class LoadMoreInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否启用上拉加载
     */
    private Boolean enabled;

    /**
     * 数据快照（数据签名）
     * 用于确保分页数据一致性
     */
    private String dataSnapshot;

    /**
     * 创建启用的加载更多信息
     */
    public static LoadMoreInfo enabled(String dataSnapshot) {
        LoadMoreInfo info = new LoadMoreInfo();
        info.setEnabled(true);
        info.setDataSnapshot(dataSnapshot);
        return info;
    }

    /**
     * 创建禁用的加载更多信息
     */
    public static LoadMoreInfo disabled() {
        LoadMoreInfo info = new LoadMoreInfo();
        info.setEnabled(false);
        info.setDataSnapshot(null);
        return info;
    }
}