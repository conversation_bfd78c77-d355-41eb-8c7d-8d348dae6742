package com.xiaomi.micar.site.component.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Tab信息流数据响应
 * 对应API返回的信息流数据结构
 */
@Data
@NoArgsConstructor
public class TabFeedsResponse {
    /**
     * 响应码，200表示成功
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private TabFeedsData data;

    /**
     * Tab信息流数据
     */
    @Data
    @NoArgsConstructor
    public static class TabFeedsData {
        /**
         * 总记录数
         */
        private Integer total;

        /**
         * 分页起始位置
         */
        private String after;

        /**
         * 每页记录数
         */
        private Integer limit;

        /**
         * 记录列表
         */
        private List<FeedRecord> records = new ArrayList<>();
    }

    /**
     * 信息流记录
     */
    @Data
    @NoArgsConstructor
    public static class FeedRecord {
        /**
         * 帖子ID
         */
        private String postId;

        /**
         * 帖子类型
         */
        private Integer type;

        /**
         * 帖子标题
         */
        private String title;

        /**
         * 摘要内容
         */
        private String displaySummary;

        /**
         * 详细摘要内容
         */
        private List<SummaryItem> summary = new ArrayList<>();

        /**
         * 是否点赞
         */
        private Boolean like;

        /**
         * 点赞数
         */
        private Integer likeCnt;

        /**
         * 评论数
         */
        private Integer commentCnt;

        /**
         * 创建时间(时间戳)
         */
        private Long createTime;

        /**
         * 作者信息
         */
        private Author author;

        /**
         * 图片列表
         */
        private List<ImageInfo> imgList = new ArrayList<>();

        /**
         * 视频列表
         */
        private List<VideoInfo> videoList = new ArrayList<>();

        /**
         * 主题列表
         */
        private List<Topic> topicList = new ArrayList<>();

        /**
         * 显示标签
         */
        private List<String> displayTags = new ArrayList<>();

        /**
         * 标签（逗号分隔的ID）
         */
        private String tag;

        /**
         * 关注状态
         */
        private Integer follow;
        /**
         * 地理位置
         */
        private String ipRegion;
    }

    /**
     * 摘要项
     */
    @Data
    @NoArgsConstructor
    public static class SummaryItem {
        /**
         * 类型，如"txt"
         */
        private String type;

        /**
         * 文本内容
         */
        private String txt;
    }

    /**
     * 作者信息
     */
    @Data
    @NoArgsConstructor
    public static class Author {
        /**
         * 用户ID
         */
        @JsonProperty("eUserId")
        private String eUserId;

        /**
         * 用户名
         */
        private String userName;

        /**
         * 头像URL
         */
        private String icon;

        /**
         * 是否为员工
         */
        private Boolean isEmployee;

        /**
         * 身份列表
         */
        private List<Identity> identityList = new ArrayList<>();

        /**
         * 头像框信息
         */
        private HeaderFrame headerFrame;

        /**
         * 勋章
         */
        private PinBadge pinBadge;
    }

    /**
     * 勋章信息
     */
    @Data
    @NoArgsConstructor
    public static class PinBadge {
        /**
         * 勋章ID
         */
        private Integer badgeId;
        /**
         * 勋章图片URLl
         *
         */
        private String img;
    }
    /**
     * 用户身份信息
     */
    @Data
    @NoArgsConstructor
    public static class Identity {
        /**
         * 图标URL
         */
        private String url;

        /**
         * 暗色模式图标URL
         */
        private String darkUrl;

        /**
         * 车型
         */
        private String carModel;

        /**
         * 状态
         */
        private Integer status;
    }

    /**
     * 头像框信息
     */
    @Data
    @NoArgsConstructor
    public static class HeaderFrame {
        /**
         * 框URL
         */
        private String frameUrl;

        /**
         * 暗色模式框URL
         */
        private String darkFrameUrl;
    }

    /**
     * 图片信息
     */
    @Data
    @NoArgsConstructor
    public static class ImageInfo {
        /**
         * 图片URL
         */
        private String imageUrl;

        /**
         * 图片高度
         */
        private Integer height;

        /**
         * 图片宽度
         */
        private Integer width;
    }

    /**
     * 视频信息
     */
    @Data
    @NoArgsConstructor
    public static class VideoInfo {
        /**
         * 封面URL
         */
        private String cover;

        /**
         * 时长
         */
        private Long duration;

        /**
         * 图片高度
         */
        private Integer height;

        /**
         * 图片宽度
         */
        private Integer width;
    }

    /**
     * 主题信息
     */
    @Data
    @NoArgsConstructor
    public static class Topic {
        /**
         * 主题ID
         */
        private String topicId;

        /**
         * 主题名称
         */
        private String topicName;
    }
} 
