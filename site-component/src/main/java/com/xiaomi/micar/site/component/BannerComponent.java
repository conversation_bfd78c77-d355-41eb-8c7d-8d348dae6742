package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.xiaomi.micar.site.component.element.MediaElement;
import com.xiaomi.micar.site.component.enums.RefTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Banner横幅广告组件实现
 * 支持自动播放、自定义播放间隔，每个元素为Image类型
 * 数据来源：运营配置
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BannerComponent extends BaseComponent {

    /**
     * 横幅数据列表
     */
    private List<MediaElement> data;

    @Override
    @JsonIgnore
    public List<String> getCommunityRefIds() {
        List<String> refIds = new ArrayList<>();

        if (data != null) {
            for (MediaElement mediaElement : data) {
                if (mediaElement != null &&
                    StringUtils.isNotBlank(mediaElement.getRefId()) &&
                    StringUtils.isNotBlank(mediaElement.getRefType()) &&
                    RefTypeEnum.COMMUNITY.getCode().equals(mediaElement.getRefType())) {
                    refIds.add(mediaElement.getRefId());
                }
            }
        }

        return refIds;
    }

    /**
     * 检查Banner组件数据是否已成功填充
     * Banner组件认为有data字段且data不为空即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return data != null && !data.isEmpty();
    }
}
