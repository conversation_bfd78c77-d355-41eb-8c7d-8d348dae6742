package com.xiaomi.micar.site.component.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 基础元素：交互
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ActionElement extends BaseElement implements Element {


    private String id;

    /**
     * 交互类型，支持link（跳转）和toast（提示），like
     */
    private String type;

    /**
     * 点赞等数量
     */
    private String text;

    /**
     * 链接URL，当type为link时有效
     */
    private String linkUrl;

    /**
     * 链接跳转方式:
     * - app APP内部打
     * - explorer 调用浏览器打开
     */
    private String linkTarget;

    /**
     * 提示内容，当type为toast时有效
     */
    private String toast;


    /**
     * 操作状态，0表示默认状态，1表示已操作状态
     */
    private Integer status;

    /**
     * 是否要求登录
     */
    private Boolean needLogin;
}
