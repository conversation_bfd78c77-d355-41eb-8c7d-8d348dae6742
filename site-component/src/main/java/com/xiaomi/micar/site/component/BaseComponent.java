package com.xiaomi.micar.site.component;

import com.google.common.collect.Maps;
import com.xiaomi.micar.site.component.constants.TrackConstants;
import lombok.Data;

import java.util.Map;

/**
 * 基础组件
 */
@Data
public class BaseComponent implements Component {

    private String id;

    /**
     * 业务类型标识（如：service_booking / charging_service / insurance_service）
     */
    private String businessType;

    @SuppressWarnings("MemberName")
    private Map<String, String> _track;

    public void track(String sectionId, String sectionName) {
        if (_track == null) {
            _track = Maps.newHashMap();
        }
        _track.put(TrackConstants.SECTION_ID, sectionId);
        _track.put(TrackConstants.SECTION_NAME, sectionName);
    }

}
