package com.xiaomi.micar.site.component.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 基础组件：媒体
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MediaElement extends BaseElement implements Element {
    /**
     * 类型  image / video
     * {@link com.xiaomi.micar.site.component.enums.MediaType}
     */
    private String type;

    private String title;
    private String subTitle;
    private String label;

    private ImageElement image;
    private ImageElement feedCover;
    private ImageElement imageFold;

    private VideoElement video;
    private VideoElement videoFold;

    private ActionElement action;

    /**
     * 链接类型，例如：community（社区）
     */
    private String refType;

    /**
     * 链接ID，例如社区的postId
     */
    private String refId;

    private String id;

    private String name;

}
