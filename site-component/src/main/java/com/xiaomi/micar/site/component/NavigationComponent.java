package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.component.enums.RefTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Navigation导航列表组件实现
 * 支持设置每行展示的列数，每个元素为Image类型
 * 数据来源：运营配置
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class NavigationComponent extends BaseComponent implements Component {

    /**
     * 主标题
     */
    private String title;

    /**
     * 每行展示的列数
     */
    private Integer columns;

    /**
     * 导航数据列表
     */
    private List<ArticleElement> data;

    @Override
    @JsonIgnore
    public List<String> getCommunityRefIds() {
        List<String> refIds = new ArrayList<>();

        if (data != null) {
            for (ArticleElement articleElement : data) {
                if (articleElement != null &&
                    StringUtils.isNotBlank(articleElement.getRefId()) &&
                    StringUtils.isNotBlank(articleElement.getRefType()) &&
                    RefTypeEnum.COMMUNITY.getCode().equals(articleElement.getRefType())) {
                    refIds.add(articleElement.getRefId());
                }
            }
        }

        return refIds;
    }

    /**
     * 检查Navigation组件数据是否已成功填充
     * Navigation组件认为有data字段且data不为空即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return data != null && !data.isEmpty();
    }
}
