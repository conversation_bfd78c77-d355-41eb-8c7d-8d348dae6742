package com.xiaomi.micar.site.component.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 基础元素：图片
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ImageElement extends BaseElement implements Element {

    private String id;
    private String src;
    private Integer width;
    private Integer height;

    public ImageElement() {
    }
}
