package com.xiaomi.micar.site.component;

import com.xiaomi.micar.site.component.element.ActionElement;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.element.ImageElement;
import com.xiaomi.micar.site.component.element.VideoElement;
import com.xiaomi.micar.site.component.model.PostTopicInfo;
import com.xiaomi.micar.site.component.model.PublishInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * TabFeeds多标签精彩瞬间组件实现
 * 支持多标签下的用户动态或内容展示
 * 数据来源：运营配置+社区数据
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TabFeedsComponent extends TabsComponent<Void> implements Component {


    private String id;


    /**
     * 内容数据
     */
    private FeedData data;

    /**
     * 数据容器
     */
    @Data
    public static class FeedData {
        /**
         * 当前数据起始位置
         */
        private Integer offset;

        /**
         * 内容记录列表
         */
        private List<FeedItem> records;
        /**
         * 是否还有更多
         */
        private boolean hasMore;
    }

    /**
     * 内容项
     */
    @Data
    public static class FeedItem {

        private String id;

        /**
         * 发布信息
         */
        private PublishInfo publishInfo;

        /**
         * 内容摘要
         */
        private String summary;

        /**
         * 图片image 视频 video
         */
        private String type;

        /**
         * 图片数量
         */
        private Integer imageCount;

        /**
         * 图片列表，最多三个
         */
        private List<ImageElement> imageList;

        /**
         * 视频
         */
        private List<VideoElement> videoList;

        /**
         * 帖子列表
         */
        private List<PostTopicInfo> topicList;

        /**
         * 标签列表
         */
        private List<String> displayTags;

        private ActionElement action;

        /**
         * 交互操作列表
         */
        private List<ButtonElement> buttons;
    }

    /**
     * 检查TabFeeds组件数据是否已成功填充
     * TabFeeds组件认为有data字段且data不为null即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    public boolean isDataFilled() {
        return data != null && CollectionUtils.isNotEmpty(data.getRecords());
    }

}
