package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.element.MediaElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * ServiceBooking 服务预约组件
 * 结构示例参考：
 * {
 * "title": "服务预约",
 * "extra": {ButtonElement},
 * "items": [TabsComponent.TabItem],
 * "actions": [{MediaElement}]
 * }
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AppointmentTabsComponent extends TabsComponent<Void> implements Component {

    /**
     * 额外操作区域（例如：我的保养计划 按钮）
     */
    private ButtonElement extra;

    /**
     * 快捷操作入口（例如：道路救援、事故报案 等）
     */
    private List<MediaElement> actions;

    /**
     * 判断数据是否填充：有 tabs 或 actions 视为已填充
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return (getItems() != null && !getItems().isEmpty())
                || (actions != null && !actions.isEmpty());
    }
}
