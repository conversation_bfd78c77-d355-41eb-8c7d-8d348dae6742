package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.enums.RefTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * News图文列表组件实现
 * 支持展示新闻或文章列表，包含发布信息和交互操作
 * 数据来源：运营配置
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class NewsComponent extends BaseComponent implements Component {

    /**
     * 主标题
     */
    private String title;

    /**
     * 标题标签
     */
    private String titleTag;

    /**
     * 更多
     */
    private ButtonElement extra;

    /**
     * 新闻列表数据
     */
    private List<ArticleElement> data;

    /**
     * 上拉加载更多信息
     * 空表示不需要上拉加载
     */
    private LoadMoreInfo loadMore;

    @Override
    @JsonIgnore
    public List<String> getCommunityRefIds() {
        List<String> refIds = new ArrayList<>();

        if (data != null) {
            for (ArticleElement articleElement : data) {
                if (articleElement != null &&
                    StringUtils.isNotBlank(articleElement.getRefId()) &&
                    StringUtils.isNotBlank(articleElement.getRefType()) &&
                    RefTypeEnum.COMMUNITY.getCode().equals(articleElement.getRefType())) {
                    refIds.add(articleElement.getRefId());
                }
            }
        }

        return refIds;
    }

    /**
     * 检查News组件数据是否已成功填充
     * News组件认为有data字段且data不为空即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return data != null && !data.isEmpty();
    }
}
