package com.xiaomi.micar.site.component.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 按钮类型
 */
@Getter
@AllArgsConstructor
public enum ButtonType {
    link,
    toast,
    text,
    like,
    comment,
    ;

    public String getCode() {
        return name();
    }

    public static ButtonType getByCode(String code) {
        for (ButtonType mediaType : ButtonType.values()) {
            if (mediaType.getCode().equals(code)) {
                return mediaType;
            }
        }
        throw new UnsupportedOperationException("不支持的按钮类型：" + code);
    }
}

