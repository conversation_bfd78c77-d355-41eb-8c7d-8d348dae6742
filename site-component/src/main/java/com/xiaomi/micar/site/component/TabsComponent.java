package com.xiaomi.micar.site.component;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * TabList标签列表组件实现
 * 支持底部或顶部标签列表，可设置选中状态和交互功能
 * 数据来源：运营配置
 *
 * @param <T> 标签内容
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TabsComponent<T> extends BaseComponent implements Component {


    private String title;

    /**
     * 标题标签
     */
    private String titleTag;


    /**
     * 标签项列表
     */
    private List<TabItem<T>> items = new ArrayList<>();

    /**
     * 标签项
     *
     * @param <T> 标签内容
     */
    @Data
    public static class TabItem<T> {

        /**
         * tabId
         */
        private String tabId;

        /**
         * 标签标题
         */
        private String text;

        /**
         * 标签标题英文
         */
        private String textEn;

        /**
         * Tab页面URL
         */
        private String url;
        /**
         * 标签图标URL
         */
        private String icon;

        /**
         * 选中状态下的图标URL
         */
        private String iconSelected;

        /**
         * 是否选中
         */
        private Boolean selected;

        /**
         * 标签内容
         */
        private T content;

        /**
         * 数据提供者名称
         */
        private String dataProvider;

        /**
         * 数据提供者参数
         */
        private Object dataProviderParams;
    }

    @Override
    @JsonIgnore
    public List<String> getCommunityRefIds() {
        List<String> refIds = new ArrayList<>();

        if (items != null) {
            for (TabItem<T> tabItem : items) {
                if (tabItem != null && tabItem.getContent() != null) {
                    // 如果内容是组件，递归获取其中的社区链接ID
                    if (tabItem.getContent() instanceof Component) {
                        Component component = (Component) tabItem.getContent();
                        refIds.addAll(component.getCommunityRefIds());
                    }

                    // 如果内容是列表，遍历列表中的每个元素
                    if (tabItem.getContent() instanceof List) {
                        List<?> contentList = (List<?>) tabItem.getContent();
                        for (Object item : contentList) {
                            if (item instanceof Component) {
                                Component component = (Component) item;
                                refIds.addAll(component.getCommunityRefIds());
                            }
                        }
                    }
                }
            }
        }

        return refIds;
    }

    /**
     * 检查Tabs组件数据是否已成功填充
     * Tabs组件认为有items字段且items不为空即为数据填充成功
     *
     * @return 如果组件数据已正确填充则返回true，否则返回false
     */
    @Override
    @JsonIgnore
    public boolean isDataFilled() {
        return items != null && !items.isEmpty();
    }
}
