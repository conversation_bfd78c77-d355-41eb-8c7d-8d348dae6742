package com.xiaomi.micar.site.component.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

/**
 * 基础元素：按钮
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ButtonElement extends BaseElement implements Element {

    private String id;

    /**
     * 按钮类型： text文本 / like点赞 / comment评论
     * {@link  com.mi.car.iccc.starter.site.enums.ButtonType}
     */
    private String type;

    /**
     * 按钮文本、点赞数量等
     */
    private String text;

    /**
     * 操作状态，0表示默认状态，1表示checked
     */
    private Integer status;

    // 按钮交互

    /**
     * 按钮点击交互，支持link（跳转）和toast（提示）
     */
    private String action;

    /**
     * 链接URL，当type为link时有效
     */
    private String linkUrl;

    /**
     * h5 / router
     * @see com.xiaomi.micar.site.component.enums.RefTypeEnum
     */
    private String refType;

    /**
     *
     */
    private String refId;


    /**
     * 链接跳转方式:
     * - app APP内部打
     * - explorer 调用浏览器打开
     */
    private String linkTarget;

    /**
     * 提示内容，当type为toast时有效
     */
    private String toast;
    /**
     * 按钮样式
     * position: up
     */
    private Map<String,Object> buttonStyle;
}
