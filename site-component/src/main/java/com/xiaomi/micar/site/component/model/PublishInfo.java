package com.xiaomi.micar.site.component.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 社区帖子：发布信息
 */
@Data
public class PublishInfo {

    @JsonProperty("eUserId")
    private String eUserId;

    /**
     * 作者名称
     */
    private String author;

    /**
     * 作者头像URL
     */
    private String avatar;

    /**
     * 是否为员工
     */
    private Boolean isEmployee;

    /**
     * 作者主页链接URL
     */
    private String linkUrl;


    /**
     * 发布时间
     */
    private String time;

    /**
     * 发布地点
     */
    private String ipRegion;

    /**
     * 身份列表
     */
    private List<TabFeedsResponse.Identity> identityList = new ArrayList<>();

    /**
     * 头像框信息
     */
    private TabFeedsResponse.HeaderFrame headerFrame;

    /**
     * 勋章
     */
    private TabFeedsResponse.PinBadge pinBadge;

}
