package com.xiaomi.micar.site.component.element;

import com.google.common.collect.Maps;
import com.xiaomi.micar.site.component.constants.TrackConstants;
import lombok.Data;

import java.util.Map;

/**
 * 基础元素
 */
@Data
public class BaseElement implements Element {

    private String id;

    private String name;

    @SuppressWarnings("MemberName")
    private Map<String, String> _track;


    public void track(String itemId, String itemName) {
        if (_track == null) {
            _track = Maps.newHashMap();
        }
        _track.put(TrackConstants.ITEM_ID, itemId);
        _track.put(TrackConstants.ITEM_NAME, itemName);
    }


    public void track(String itemId, String itemName, String itemType) {
        if (_track == null) {
            _track = Maps.newHashMap();
        }
        _track.put(TrackConstants.ITEM_ID, itemId);
        _track.put(TrackConstants.ITEM_NAME, itemName);
        _track.put(TrackConstants.ITEM_TYPE, itemType);
    }


}
