FROM micr.cloud.mioffice.cn/miflow/java:openjdk-8-centos7.3-maven3.6.3 AS builder
WORKDIR /build
COPY . /build
RUN mvn clean package -U -DskipTests -pl site-server -am

FROM cr.d.xiaomi.net/miflow/java:openjdk-8-centos7.3

ENV SERVICE_NAME "site-server"

ARG OPENTELEMETRY_URL=https://pkgs.d.xiaomi.net/artifactory/releases/io/opentelemetry/javaagent/opentelemetry-javaagent/1.13.1-milatest/opentelemetry-javaagent-1.13.1-milatest.jar
ADD ${OPENTELEMETRY_URL} /home/<USER>/app/mitelemetry/agent/opentelemetry-javaagent-all.jar

COPY --from=builder /build/${SERVICE_NAME}/target/*.jar /home/<USER>/app/app.jar
COPY ./${SERVICE_NAME}/script/entrypoint.sh /home/<USER>/app/entrypoint.sh
RUN chmod +x /home/<USER>/app/entrypoint.sh

WORKDIR /home/<USER>/

ENTRYPOINT ["/home/<USER>/app/entrypoint.sh"]
