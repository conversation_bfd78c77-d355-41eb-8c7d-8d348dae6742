package com.xiaomi.micar.site.api.fe;

import com.xiaomi.car.activity.external.api.req.ActivityListQueryReq;
import com.xiaomi.car.activity.external.api.resp.ActivityListResp;
import com.xiaomi.micar.site.api.fe.aop.FeLoginCheck;
import com.xiaomi.micar.site.service.remote.RemoteActivityDataSource;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 活动服务
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Slf4j
@ApiModule(value = "活动服务接口", apiInterface = FeActivityService.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class FeActivityServiceImpl implements FeActivityService {

    @Resource
    private RemoteActivityDataSource activityDataSource;

    @FeLoginCheck(required = false)
    @ApiDoc(value = "加载活动列表")
    @Override
    public Result<?> activityList(ActivityListQueryReq request) {
        ActivityListResp activityListResp = activityDataSource.queryActivityList(request);
        if (activityListResp == null) {
            return Result.fail(GeneralCodes.InternalError, "获取活动数据异常");
        }
        return Result.success(activityListResp);
    }

}
