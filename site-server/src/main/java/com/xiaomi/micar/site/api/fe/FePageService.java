package com.xiaomi.micar.site.api.fe;

import com.xiaomi.micar.site.api.model.FeedsLoadReq;

import java.util.Map;

/**
 * 页面服务接口
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
public interface FePageService {

    /**
     * 返回页面内容
     *
     * @return 页面内容
     */
    String pageData(Map<String, String> request);

    /**
     * 返回页面内容V2优化版本
     *
     * @param request 请求参数
     * @return 页面内容
     */
    String pageDataV2Optimized(Map<String, String> request);

    /**
     * 返回专题页内容V2优化版本
     * 功能与 pageDataV2Optimized 相同，但接口独立，便于后续做接口维度的限流等操作
     *
     * @param request 请求参数
     * @return 专题页内容
     */
    String topicPageDataV2Optimized(Map<String, String> request);
}
