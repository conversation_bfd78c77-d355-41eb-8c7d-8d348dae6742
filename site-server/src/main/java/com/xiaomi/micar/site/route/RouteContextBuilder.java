package com.xiaomi.micar.site.route;

import com.xiaomi.micar.site.service.UserStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 路由上下文构建器：将“用户在各维度命中的取值集合”抽象为 RouteContext。
 * - 目前支持：UserStrategyEnum（单值）、CarModelEnum（多值）。
 * - 后续可扩展更多维度（如实验桶、地区等）。
 */
@Slf4j
@Component
public class RouteContextBuilder {

    public static final String DIM_USER_STRATEGY = "UserStrategyEnum";
    public static final String DIM_CAR_MODEL = "CarModelEnum";

    @Resource
    private UserStrategyService userStrategyService;

    /**
     * 解析并构建路由上下文（不包含任何监控侧逻辑）。
     */
    public RouteContext resolve(String pageId, Map<String, Object> queryParams, String userId) {
        Map<String, List<String>> dims = new HashMap<>();

        // 1) 人群维度：优先从参数多值解析；否则从服务获取单值
        List<String> strategies = extractStrategies(queryParams);
        if (strategies.isEmpty()) {
            String strategy = userStrategyService.determineUserStrategyWithPageCheck(pageId, queryParams, userId);
            if (strategy != null && !strategy.isEmpty()) {
                strategies = Collections.singletonList(strategy);
            }
        }
        if (!strategies.isEmpty()) {
            dims.put(DIM_USER_STRATEGY, strategies);
        }

        // 2) 车型维度：从 queryParams 解析（支持 car_model / carModel，支持逗号分隔多值）
        List<String> models = extractCarModels(queryParams);
        if (!models.isEmpty()) {
            dims.put(DIM_CAR_MODEL, models);
        }

        return RouteContext.of(dims);
    }

    /**
     * 提取主用的人群策略（用于兼容历史逻辑，如组合键生成）。
     */
    public String primaryUserStrategy(RouteContext ctx) {
        List<String> vals = ctx == null ? null : ctx.getDimValues().get(DIM_USER_STRATEGY);
        if (vals == null || vals.isEmpty()) return "all";
        return vals.get(0);
    }

    private List<String> extractCarModels(Map<String, Object> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return Collections.emptyList();
        }
        Object raw = queryParams.get("car_model");
        if (raw == null) {
            raw = queryParams.get("carModel");
        }
        if (raw == null) {
            return Collections.emptyList();
        }

        try {
            if (raw instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) raw;
                List<String> out = new ArrayList<>();
                for (Object o : list) {
                    if (o == null) {
                        continue;
                    }
                    String v = String.valueOf(o).trim();
                    if (!v.isEmpty()) {
                        out.add(v);
                    }
                }
                return out;
            }
            String s = String.valueOf(raw).trim();
            if (s.isEmpty()) {
                return Collections.emptyList();
            }
            String[] parts = s.split(",");
            List<String> out = new ArrayList<>();
            for (String p : parts) {
                String v = p.trim();
                if (!v.isEmpty()) {
                    out.add(v);
                }
            }
            return out;
        } catch (Exception e) {
            log.warn("[RouteContextBuilder] 解析 car_model 参数失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private List<String> extractStrategies(Map<String, Object> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return Collections.emptyList();
        }
        Object raw = queryParams.get("strategy");
        if (raw == null) {
            raw = queryParams.get("user_strategy");
        }
        if (raw == null) {
            raw = queryParams.get("userStrategy");
        }
        if (raw == null) {
            return Collections.emptyList();
        }
        try {
            if (raw instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) raw;
                List<String> out = new ArrayList<>();
                for (Object o : list) {
                    if (o == null) {
                        continue;
                    }
                    String v = String.valueOf(o).trim();
                    if (!v.isEmpty()) {
                        out.add(v);
                    }
                }
                return out;
            }
            String s = String.valueOf(raw).trim();
            if (s.isEmpty()) {
                return Collections.emptyList();
            }
            String[] parts = s.split(",");
            List<String> out = new ArrayList<>();
            for (String p : parts) {
                String v = p.trim();
                if (!v.isEmpty()) {
                    out.add(v);
                }
            }
            return out;
        } catch (Exception e) {
            log.warn("[RouteContextBuilder] 解析 strategy 参数失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
}
