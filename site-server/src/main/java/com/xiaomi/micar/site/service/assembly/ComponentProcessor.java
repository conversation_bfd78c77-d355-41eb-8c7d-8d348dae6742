package com.xiaomi.micar.site.service.assembly;


import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.component.element.VideoElement;
import com.xiaomi.micar.site.component.element.ActionElement;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.element.MediaElement;
import com.xiaomi.micar.site.component.enums.RefTypeEnum;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.service.provider.ComponentDataProvider;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.component.ComponentConf;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.service.http.RouterService;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import com.xiaomi.micar.site.model.PostDetailResponse;
import com.xiaomi.micar.site.model.CommunityPostType;
import com.xiaomi.micar.site.enums.PageTypeEnum;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.dao.SitePageInfoEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 组件处理器 - 专门处理组件相关逻辑
 * 职责：处理组件数据填充、链接转换等
 */
@Slf4j
@Service
public class ComponentProcessor {

    private final BeanFactory beanFactory;

    @Resource
    private SitePageInfoEngine pageInfoEngine;

    // 页面类型缓存，避免重复查询数据库
    private final Map<String, Boolean> pageTypeCache = new ConcurrentHashMap<>();

    // 社区API重试器
    private final Retryer<PostDetailResponse> communityApiRetryer;

    public ComponentProcessor(BeanFactory beanFactory) {
        this.beanFactory = beanFactory;
        this.communityApiRetryer = RetryerBuilder.<PostDetailResponse>newBuilder()
                .retryIfRuntimeException()
                .withWaitStrategy(WaitStrategies.fixedWait(1, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();
    }

    /**
     * 处理模块数据，填充页面响应中的模块内容
     */
    public void processModules(ResponseContext context, PageRespV2 pageResp) {
        List<ComponentConf> modules = pageResp.getModules();
        if (modules == null || modules.isEmpty()) {
            return;
        }

        List<ComponentConf> processedModules = new ArrayList<>();
        Set<String> postIdsSet = new LinkedHashSet<>(); // 使用 LinkedHashSet 保持顺序并去重

        // 获取页面级别的组件去重标记（在同一页面的不同人群中共享）
        Set<String> processedComponents = context.getProcessedComponents();

        Map<String, PageConfigModel.ComponentModel> componentConfigMap = context.getPageConfig()
                .getComponents()
                .stream()
                .collect(Collectors.toMap(
                        PageConfigModel.ComponentModel::getId,
                        c -> c
                ));

        for (ComponentConf componentConf : modules) {
            String componentId = componentConf.getId();
            PageConfigModel.ComponentModel componentModel = componentConfigMap.get(componentId);

            if (componentModel == null) {
                log.warn("Component model not found for component: {}", componentId);
                continue;
            }

            Component component = componentConf.getConfig();
            if (component == null) {
                log.warn("Component is null for componentId: {}", componentId);
                continue;
            }

            // 处理数据提供者
            if (processDataProvider(context, component, componentModel, componentId)) {
                processedModules.add(componentConf);
                log.debug("Component {} processed successfully", componentId);
            } else {
                log.warn("Component {} excluded from response", componentId);
            }

            // 处理链接转换（避免重复处理同一个组件）
            String componentKey = generateComponentKey(pageResp.getPageId(), componentId);
            if (!processedComponents.contains(componentKey)) {
                processComponentLinks(component);
                processedComponents.add(componentKey);
                log.debug("组件链接处理完成: {}", componentKey);
            } else {
                log.debug("跳过重复处理的组件: {}", componentKey);
            }

            // 只有非专题页才进行postId收集优化
            if (!isTopicPage(pageResp.getPageId())) {
                collectMediaElementLinkIds(component, postIdsSet);
            }
        }

        pageResp.setModules(processedModules);
        // 转换为 List 并设置到响应中
        pageResp.setPostIds(new ArrayList<>(postIdsSet));

        log.debug("收集到 {} 个去重后的帖子ID", postIdsSet.size());
    }

    /**
     * 处理数据提供者
     */
    private boolean processDataProvider(ResponseContext context,
                                        Component component,
                                        PageConfigModel.ComponentModel componentModel,
                                        String componentId) {
        String dataProviderName = componentModel.getDataProvider();
        
        return StringUtils.isNotBlank(dataProviderName) 
                ? processWithDataProvider(context, component, componentModel, componentId, dataProviderName)
                : true;
    }

    /**
     * 使用数据提供者处理组件
     */
    private boolean processWithDataProvider(ResponseContext context,
                                           Component component,
                                           PageConfigModel.ComponentModel componentModel,
                                           String componentId,
                                           String dataProviderName) {
        try {
            ComponentDataProvider provider = (ComponentDataProvider) beanFactory.getBean(dataProviderName);
            log.debug("Processing component {} with data provider {}", componentId, dataProviderName);

            // 临时保存原始查询参数（需要深拷贝避免引用问题）
            Map<String, Object> originalQueryParams = context.getQueryParams() != null 
                ? new HashMap<>(context.getQueryParams()) 
                : null;
            
            // 处理数据提供者参数
            processDataProviderParams(context, componentModel, componentId);

            // 处理组件数据
            provider.process(context, component);

            // 恢复原始查询参数，避免影响其他组件
            context.setQueryParams(originalQueryParams);

            // 验证组件是否成功填充数据
            return component.isDataFilled();

        } catch (Exception e) {
            log.error("Failed to process component {} with provider {}: {}",
                    componentId, dataProviderName, e.getMessage(), e);
            return false;
        }
    }


    /**
     * 处理数据提供者参数
     */
    private void processDataProviderParams(ResponseContext context,
                                           PageConfigModel.ComponentModel componentModel,
                                           String componentId) {
        String dataProviderParams = componentModel.getDataProviderParams();
        if (StringUtils.isBlank(dataProviderParams)) {
            return;
        }

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = JsonUtil.parseObject(dataProviderParams, Map.class);
            if (paramsMap != null) {
                mergeQueryParams(context, paramsMap);
                log.info("Parsed dataProviderParams for component {}: {}", componentId, context.getQueryParams());
            }
        } catch (Exception e) {
            log.warn("Error parsing dataProviderParams for component {}: {}", componentId, e.getMessage());
            context.setQueryParams(new HashMap<>());
        }
    }

    /**
     * 合并查询参数
     */
    private void mergeQueryParams(ResponseContext context, Map<String, Object> paramsMap) {
        Map<String, Object> queryParams = context.getQueryParams();
        if (MapUtils.isNotEmpty(queryParams)) {
            queryParams.putAll(paramsMap);
        } else {
            context.setQueryParams(paramsMap);
        }
    }

    /**
     * 处理组件内部链接转换
     */
    public void processComponentLinks(Component component) {
        if (component == null) {
            return;
        }

        try {
            // 使用反射处理所有字段
            processFieldsRecursively(component);
        } catch (Exception e) {
            log.error("Failed to process component links: {}", e.getMessage(), e);
        }
    }

    /**
     * 递归处理字段
     */
    private void processFieldsRecursively(Object obj) throws IllegalAccessException {
        processFieldsRecursively(obj, new HashSet<>());
    }

    /**
     * 递归处理字段（带循环检测）
     */
    private void processFieldsRecursively(Object obj, Set<Object> visited) throws IllegalAccessException {
        if (obj == null) {
            return;
        }

        // 防止循环引用导致的无限递归
        if (visited.contains(obj)) {
            return;
        }
        visited.add(obj);


        Class<?> clazz = obj.getClass();
        List<Field> fields = new ArrayList<>();

        // 正确收集所有字段（包括父类），避免重复
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(obj);

            if (value == null) {
                continue;
            }

            if (value instanceof MediaElement) {
                // 注意：MediaElement 要在 Component 之前判断，因为 ArticleElement extends MediaElement
                processMediaElement((MediaElement) value);
            } else if (value instanceof ButtonElement) {
                processButtonElement((ButtonElement) value);
            } else if (value instanceof Component) {
                processFieldsRecursively(value, visited);
            } else if (value instanceof Collection) {
                for (Object item : ((Collection<?>) value)) {
                    if (item instanceof MediaElement) {
                        log.debug("处理集合中的MediaElement: class={}, id={}", item.getClass().getSimpleName(), ((MediaElement) item).getId());
                        processMediaElement((MediaElement) item);
                    } else if (item instanceof ButtonElement) {
                        log.debug("处理集合中的ButtonElement: class={}", item.getClass().getSimpleName());
                        processButtonElement((ButtonElement) item);
                    } else if (item instanceof Component && !(item instanceof MediaElement) && !(item instanceof ButtonElement)) {
                        log.debug("处理集合中的Component: class={}", item.getClass().getSimpleName());
                        processFieldsRecursively(item, visited);
                    } else if (item != null && !isPrimitiveOrWrapper(item.getClass())) {
                        log.debug("递归处理集合中的其他对象: class={}", item.getClass().getSimpleName());
                        processFieldsRecursively(item, visited);
                    }
                }
            }
            // 移除了过于宽泛的递归逻辑，避免无限递归
        }

        // 注意：不再移除visited中的对象，确保同一对象在整个处理过程中只被处理一次
    }

    /**
     * 处理媒体元素
     */
    private void processMediaElement(MediaElement element) {
        log.debug("Processing MediaElement: id={}, refType={}, refId={}",
                 element.getId(), element.getRefType(), element.getRefId());

        RefTypeEnum refType = RefTypeEnum.getByCode(element.getRefType());
        if (refType == null) {
            log.warn("Unknown refType: {} for element: {}", element.getRefType(), element.getId());
            return;
        }

        ActionElement action = element.getAction();
        if (action == null) {
            action = new ActionElement();
            element.setAction(action);
            log.debug("Created new ActionElement for element: {}", element.getId());
        }

        if (refType == RefTypeEnum.H5) {
            log.debug("Processing H5 element: {}", element.getId());
            processH5Element(element, action);
        } else if (refType == RefTypeEnum.ROUTER) {
            log.debug("Processing ROUTER element: {}", element.getId());
            processRouterElement(element, action);
        } else if (refType == RefTypeEnum.COMMUNITY) {
            log.debug("Processing COMMUNITY element: {}", element.getId());
            processCommunityElement(element, action);
        } else {
            throw new IllegalArgumentException("Unsupported refType: " + refType);
        }

        log.debug("Finished processing MediaElement: id={}, action.type={}, action.linkUrl={}",
                 element.getId(), action.getType(), action.getLinkUrl());
    }

    /**
     * 处理社区元素，根据帖子类型设置正确的URL
     */
    private void processCommunityElement(MediaElement element, ActionElement action) {
        try {
            PostDetailResponse response = communityApiRetryer.call(() -> {
                // 直接调用API获取帖子详情（不使用缓存）
                CommunityApiFeignClient communityApiFeignClient = beanFactory.getBean(CommunityApiFeignClient.class);
                PostDetailResponse apiResponse = communityApiFeignClient.getPostDetailList(element.getRefId());

                // 检查API响应是否有效，如果无效则抛出异常触发重试
                if (apiResponse == null || apiResponse.getCode() != 200 || CollectionUtils.isEmpty(apiResponse.getData())) {
                    throw new RuntimeException(String.format("社区API返回无效响应: postId=%s, code=%s, dataEmpty=%s", 
                            element.getRefId(), 
                            apiResponse != null ? apiResponse.getCode() : "null",
                            apiResponse != null ? CollectionUtils.isEmpty(apiResponse.getData()) : "null"));
                }
                
                return apiResponse;
            });
            
            PostDetailResponse.PostDetail postDetail = response.getData().get(0);
            // 通过帖子类型判断是否为视频
            boolean isVideo = postDetail.getType() != null && 
                             postDetail.getType().equals(CommunityPostType.VIDEO_ARTICLE.getCode());
            log.debug("Post {} type: {}, isVideo: {}", element.getRefId(), postDetail.getType(), isVideo);
            
            // 设置正确的URL
            RouterService routerService = beanFactory.getBean(RouterService.class);
            String url = routerService.getCommunityCommentUrl(element.getRefId(), isVideo, null, null);
            action.setType("link");
            action.setLinkUrl(url);

            //帖子类型的将帖子 id 设置为主键
            element.setId(element.getRefId());

            element.setType(isVideo ? "video" : "image");

            if (isVideo && element.getVideo() == null) {
                VideoElement videoElement = new VideoElement();
                videoElement.setCover(element.getImage().getSrc());
                videoElement.setWidth(element.getImage().getWidth());
                videoElement.setHeight(element.getImage().getHeight());
                element.setVideo(videoElement);
                element.setVideoFold(videoElement);
                element.setImage(null);
                element.setImageFold(null);
            }

            log.debug("Successfully processed community element {}: {}", element.getId(), url);
            
        } catch (Exception e) {
            log.error("处理社区元素失败: elementId={}, postId={}", element.getId(), element.getRefId(), e);
            throw new RuntimeException(String.format("处理社区元素失败: elementId=%s, postId=%s", 
                                                    element.getId(), element.getRefId()), e);
        }
    }

    /**
     * 处理extra
     */
    private void processButtonElement(ButtonElement element) {
        RefTypeEnum refType = RefTypeEnum.getByCode(element.getRefType());
        if (refType == null || StringUtils.isEmpty(element.getRefId())) {
            return;
        }

        if (refType == RefTypeEnum.H5) {
            RouterService routerService = beanFactory.getBean(RouterService.class);
            element.setType("link");
            element.setLinkUrl(routerService.getCommunityFullStackUrl(element.getRefId()));
            element.setRefType(null);
            element.setRefId(null);
        } else if (refType == RefTypeEnum.ROUTER) {
            element.setType("link");
            element.setLinkUrl(element.getRefId());
            element.setRefType(null);
            element.setRefId(null);
        } else if (refType == RefTypeEnum.ACTIVITY || refType == RefTypeEnum.COMMUNITY) {
            // 无需特殊处理
        } else {
            throw new IllegalArgumentException("Unsupported refType: " + refType);
        }
    }

    /**
     * 处理H5元素
     */
    private void processH5Element(MediaElement element, ActionElement action) {
        if (!StringUtils.isEmpty(element.getRefId())) {
            action.setType("link");
            Assert.hasLength(element.getRefId(), "refId cannot be empty for H5 type");
            try {
                RouterService routerService = beanFactory.getBean(RouterService.class);

                String postId = routerService.getPostIdFromWebUrl(element.getRefId());
                if (postId != null) {
                    action.setLinkUrl(routerService.getCommunityFullStackUrl(element.getRefId()));
                    element.setRefId(postId);
                    element.setRefType(RefTypeEnum.COMMUNITY.getCode());
                    return;
                }

                String actId = routerService.getActIdFromWebUrl(element.getRefId());
                if (actId != null) {
                    action.setLinkUrl(routerService.getCommunityFullStackUrl(element.getRefId()));
                    element.setRefId(actId);
                    element.setRefType(RefTypeEnum.ACTIVITY.getCode());
                }
            } catch (Exception e) {
                log.warn("Failed to process H5 element: {}", e.getMessage());
            }
        }
    }

    /**
     * 处理路由元素
     */
    private void processRouterElement(MediaElement element, ActionElement action) {
        if (!StringUtils.isEmpty(element.getRefId())) {
            action.setType("link");
            action.setLinkUrl(element.getRefId());
            element.setRefType(null);
            element.setRefId(null);
        }
    }

    /**
     * 判断是否为专题页
     * 通过查询数据库中的页面类型来判断，避免硬编码
     * 使用缓存机制避免重复查询数据库
     *
     * @param pageId 页面ID
     * @return true=专题页，false=非专题页
     */
    private boolean isTopicPage(String pageId) {
        if (StringUtils.isBlank(pageId)) {
            return false;
        }

        // 先从缓存中获取
        Boolean cached = pageTypeCache.get(pageId);
        if (cached != null) {
            return cached;
        }

        try {
            SitePageInfoEntity pageInfo = pageInfoEngine.lambdaQuery()
                    .eq(SitePageInfoEntity::getPageId, pageId)
                    .one();

            boolean isTopicPage = false;
            if (pageInfo != null) {
                isTopicPage = PageTypeEnum.isTopicPage(pageInfo.getPageType());
                log.debug("页面类型判断: pageId={}, pageType={}, isTopicPage={}",
                        pageId, pageInfo.getPageType(), isTopicPage);
            } else {
                log.debug("页面信息不存在，默认为非专题页: pageId={}", pageId);
            }

            // 缓存结果
            pageTypeCache.put(pageId, isTopicPage);
            return isTopicPage;

        } catch (Exception e) {
            log.error("查询页面类型异常，默认为非专题页: pageId={}", pageId, e);
            // 异常情况下也缓存false，避免重复查询
            pageTypeCache.put(pageId, false);
            return false;
        }
    }

    /**
     * 收集组件中的媒体元素链接ID
     */
    private void collectMediaElementLinkIds(Component component, Set<String> postIds) {
        if (component == null) {
            return;
        }

        try {
            List<String> communityRefIds = component.getCommunityRefIds();
            if (communityRefIds != null && !communityRefIds.isEmpty()) {
                postIds.addAll(communityRefIds);
                log.debug("从组件 {} 收集到 {} 个社区链接ID",
                         component.getClass().getSimpleName(), communityRefIds.size());
            }
        } catch (Exception e) {
            log.warn("Failed to collect community link IDs from component {}: {}",
                    component.getClass().getSimpleName(), e.getMessage());
        }
    }



    /**
     * 判断是否为基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == Boolean.class ||
               clazz == Character.class ||
               clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               clazz == String.class ||
               clazz.isEnum();
    }

    /**
     * 生成组件唯一标识键
     * 用于避免同一个组件在不同人群中被重复处理
     *
     * @param pageId 页面ID
     * @param componentId 组件ID
     * @return 组件唯一标识键
     */
    private String generateComponentKey(String pageId, String componentId) {
        return pageId + ":" + componentId;
    }

    // 已移除全局清理方法，改为使用局部变量进行去重
}
