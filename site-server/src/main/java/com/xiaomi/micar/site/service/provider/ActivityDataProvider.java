package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.component.ActivityListComponent;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.car.activity.external.api.req.ActivityListQueryReq;
import com.xiaomi.car.activity.external.api.resp.ActivityListResp;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.service.remote.RemoteActivityDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 活动列表数据提供者
 * 负责从活动服务获取数据并填充到ActivityListComponent中
 */
@Slf4j
@org.springframework.stereotype.Component
public class ActivityDataProvider extends AbstractComponentDataProvider {

    /**
     * 缓存键前缀
     */
    private static final String CACHE_KEY_PREFIX = "activity_list";

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_LIMIT = 6;

    /**
     * 最大分页大小
     */
    private static final int MAX_LIMIT = 20;

    /**
     * 最小分页大小
     */
    private static final int MIN_LIMIT = 1;

    /**
     * 活动数据源服务
     */
    @Resource
    private RemoteActivityDataSource activityDataSource;


    @Override
    protected boolean validateComponentType(Component componentInfo) {
        return componentInfo instanceof ActivityListComponent;
    }

    @Override
    protected void doProcess(ResponseContext context, Component componentInfo) {
        ActivityListComponent activityListComponent = (ActivityListComponent) componentInfo;

        try {
            // 获取请求参数并创建查询请求
            ActivityListQueryReq queryReq = createActivityQueryRequest(context);

            // 生成缓存键
            String cacheKey = generateCacheKey(CACHE_KEY_PREFIX, context.getUserId(),
                    Optional.ofNullable(context.getQueryParams()).map(Object::hashCode).orElse(0));

            // 调用数据源并处理结果
            fetchAndProcessActivityData(activityListComponent, queryReq, cacheKey);
        } catch (Exception e) {
            handleProcessingError(componentInfo, e);
        }
    }

    /**
     * 创建活动查询请求
     *
     * @param context 响应上下文
     * @return 活动查询请求
     */
    private ActivityListQueryReq createActivityQueryRequest(ResponseContext context) {
        // 获取请求参数
        Map<String, Object> queryParams = Optional.ofNullable(context.getQueryParams())
                .map(HashMap::new)
                .orElseGet(HashMap::new);

        // 创建 ActivityQueryReq 对象
        ActivityListQueryReq queryReq = new ActivityListQueryReq();

        // 设置分页参数
        Integer limit = getIntParam(queryParams, "limit", DEFAULT_LIMIT);
        if (limit < MIN_LIMIT) {
            limit = MIN_LIMIT;
        }
        if (limit > MAX_LIMIT) {
            limit = MAX_LIMIT;
        }
        queryReq.setLimit(limit);

        // 设置 after 参数 (起始位置)
        if (queryParams.containsKey("after")) {
            Integer offset = getIntParam(queryParams, "offset", 0);
            queryReq.setOffset(offset);
        }

        // 设置过滤ID
        setFilterIds(queryParams, queryReq);

        return queryReq;
    }

    /**
     * 设置过滤ID
     *
     * @param queryParams 查询参数
     * @param queryReq    查询请求
     */
    private void setFilterIds(Map<String, Object> queryParams, ActivityListQueryReq queryReq) {
        if (queryParams.containsKey("filterIds")) {
            Object filterIdsObj = queryParams.get("filterIds");
            if (filterIdsObj instanceof String) {
                String filterIdsStr = (String) filterIdsObj;
                if (!filterIdsStr.isEmpty()) {
                    String[] idsArray = filterIdsStr.split(",");
                    Set<String> filterIds = new HashSet<>(Arrays.asList(idsArray));
                    queryReq.setFilterIds(filterIds);
                }
            }
        }
    }

    /**
     * 获取和处理活动数据
     *
     * @param activityListComponent 活动列表组件
     * @param queryReq              查询请求
     * @param cacheKey              缓存键
     */
    private void fetchAndProcessActivityData(ActivityListComponent activityListComponent,
                                             ActivityListQueryReq queryReq,
                                             String cacheKey) {
        // 使用重试机制执行API调用，获取完整的活动列表数据
        ActivityListResp activityData = executeWithRetry(
                cacheKey,
                () -> activityDataSource.queryActivityList(queryReq)
        );

        // 设置组件数据
        if (activityData != null) {
            activityListComponent.setData(activityData);

            log.debug("处理活动列表成功，总数: {}, 当前偏移: {}, 是否有更多: {}",
                    activityData.getTotal(), activityData.getOffset(), activityData.getHasMorePage());
        }
    }

    @Override
    protected void handleProcessingError(Component componentInfo, Exception e) {
        log.error("ActivityList组件处理失败，已设置空数据: {}", e.getMessage());
    }


}
