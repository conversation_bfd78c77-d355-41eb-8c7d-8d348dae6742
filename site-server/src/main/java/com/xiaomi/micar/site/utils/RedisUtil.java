package com.xiaomi.micar.site.utils;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.redis.SiteRedisKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redis工具类
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@Component
public class RedisUtil {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置字符串值
     *
     * @param redisKey Redis键
     * @param value    值
     * @param args     参数
     */
    public void set(SiteRedisKey redisKey, String value, String... args) {
        String key = redisKey.toFormat(args);
        long expire = redisKey.getExpire();
        TimeUnit timeUnit = redisKey.getTimeUnit();
        if (expire > 0) {
            redisTemplate.opsForValue().set(key, value, expire, timeUnit);
        } else {
            redisTemplate.opsForValue().set(key, value);
        }
    }

    /**
     * 设置对象值（序列化为JSON）
     *
     * @param redisKey Redis键
     * @param value    对象值
     * @param args     参数
     * @param <T>      对象类型
     */
    public <T> void setObject(SiteRedisKey redisKey, T value, String... args) {
        if (value == null) {
            return;
        }
        String jsonValue = JsonUtil.toJSONString(value);
        if (StringUtils.hasText(jsonValue)) {
            set(redisKey, jsonValue, args);
        }
    }

    /**
     * 获取字符串值
     *
     * @param redisKey Redis键
     * @param args     参数
     * @return 字符串值
     */
    public String get(SiteRedisKey redisKey, String... args) {
        Object val = redisTemplate.opsForValue().get(redisKey.toFormat(args));
        if (val == null) {
            return null;
        }
        return String.valueOf(val);
    }

    /**
     * 获取对象值（从JSON反序列化）
     *
     * @param redisKey Redis键
     * @param clazz    对象类型
     * @param args     参数
     * @param <T>      对象类型
     * @return 对象值
     */
    public <T> T getObject(SiteRedisKey redisKey, Class<T> clazz, String... args) {
        String jsonValue = get(redisKey, args);
        if (!StringUtils.hasText(jsonValue)) {
            return null;
        }
        return JsonUtil.parseObject(jsonValue, clazz);
    }

    /**
     * 批量获取字符串值
     *
     * @param redisKey Redis键
     * @param ids      ID列表，用于格式化Redis键
     * @return 字符串值列表
     */
    public List<String> multiGet(SiteRedisKey redisKey, List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建所有键
        List<String> keys = ids.stream()
                .map(id -> redisKey.toFormat(id))
                .collect(Collectors.toList());

        // 批量获取值
        List<Object> values = redisTemplate.opsForValue().multiGet(keys);
        if (values == null) {
            return Collections.emptyList();
        }

        // 转换为字符串列表
        return values.stream()
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 批量获取对象值（从 JSON 反序列化）
     *
     * @param redisKey Redis键
     * @param ids      ID列表，用于格式化Redis键
     * @param clazz    对象类型
     * @param <T>      对象类型
     * @return 对象值映射，key为ID，value为对象
     */
    public <T> Map<String, T> multiGetObjects(SiteRedisKey redisKey, List<String> ids, Class<T> clazz) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量获取字符串值
        List<String> jsonValues = multiGet(redisKey, ids);
        if (jsonValues.isEmpty()) {
            return Collections.emptyMap();
        }

        // 构建结果映射
        Map<String, T> result = new HashMap<>(ids.size());
        for (int i = 0; i < Math.min(ids.size(), jsonValues.size()); i++) {
            String jsonValue = jsonValues.get(i);
            if (StringUtils.hasText(jsonValue)) {
                T obj = JsonUtil.parseObject(jsonValue, clazz);
                if (obj != null) {
                    result.put(ids.get(i), obj);
                }
            }
        }

        return result;
    }

    /**
     * 删除键
     *
     * @param redisKey Redis键
     * @param args     参数
     */
    public void delete(SiteRedisKey redisKey, String... args) {
        redisTemplate.delete(redisKey.toFormat(args));
    }

    /**
     * 设置Hash字段
     *
     * @param redisKey Redis键
     * @param field    字段
     * @param value    值
     * @param args     参数
     */
    public void hashPut(SiteRedisKey redisKey, String field, String value, String... args) {
        String key = redisKey.toFormat(args);
        redisTemplate.opsForHash().put(key, field, value);
        long expire = redisKey.getExpire();
        TimeUnit timeUnit = redisKey.getTimeUnit();
        if (expire > 0) {
            redisTemplate.expire(key, expire, timeUnit);
        }
    }

    /**
     * 设置Hash字段（对象值序列化为JSON）
     *
     * @param redisKey Redis键
     * @param field    字段
     * @param value    对象值
     * @param args     参数
     * @param <T>      对象类型
     */
    public <T> void hashPutObject(SiteRedisKey redisKey, String field, T value, String... args) {
        if (value == null) {
            return;
        }
        String jsonValue = JsonUtil.toJSONString(value);
        if (StringUtils.hasText(jsonValue)) {
            hashPut(redisKey, field, jsonValue, args);
        }
    }

    /**
     * 批量设置Hash字段（对象值序列化为JSON）
     *
     * @param redisKey Redis键
     * @param dataMap  数据Map
     * @param args     参数
     * @param <T>      对象类型
     */
    public <T> void hashPutAllObjects(SiteRedisKey redisKey, Map<String, T> dataMap, String... args) {
        if (dataMap == null || dataMap.isEmpty()) {
            return;
        }

        String key = redisKey.toFormat(args);
        Map<Object, Object> hashMap = new HashMap<>(dataMap.size());

        for (Map.Entry<String, T> entry : dataMap.entrySet()) {
            if (entry.getValue() != null) {
                String jsonValue = JsonUtil.toJSONString(entry.getValue());
                if (StringUtils.hasText(jsonValue)) {
                    hashMap.put(entry.getKey(), jsonValue);
                }
            }
        }

        if (!hashMap.isEmpty()) {
            redisTemplate.opsForHash().putAll(key, hashMap);
            long expire = redisKey.getExpire();
            TimeUnit timeUnit = redisKey.getTimeUnit();
            if (expire > 0) {
                redisTemplate.expire(key, expire, timeUnit);
            }
        }
    }

    /**
     * 获取Hash字段
     *
     * @param redisKey Redis键
     * @param field    字段
     * @param args     参数
     * @return 字符串值
     */
    public String hashGet(SiteRedisKey redisKey, String field, String... args) {
        Object val = redisTemplate.opsForHash().get(redisKey.toFormat(args), field);
        if (val == null) {
            return null;
        }
        return String.valueOf(val);
    }

    /**
     * 获取Hash字段（从JSON反序列化）
     *
     * @param redisKey Redis键
     * @param field    字段
     * @param clazz    对象类型
     * @param args     参数
     * @param <T>      对象类型
     * @return 对象值
     */
    public <T> T hashGetObject(SiteRedisKey redisKey, String field, Class<T> clazz, String... args) {
        String jsonValue = hashGet(redisKey, field, args);
        if (!StringUtils.hasText(jsonValue)) {
            return null;
        }
        return JsonUtil.parseObject(jsonValue, clazz);
    }

    /**
     * 批量获取Hash字段（从JSON反序列化）
     *
     * @param redisKey Redis键
     * @param fields   字段列表
     * @param clazz    对象类型
     * @param args     参数
     * @param <T>      对象类型
     * @return 对象Map
     */
    public <T> Map<String, T> hashMultiGetObjects(SiteRedisKey redisKey, List<String> fields, Class<T> clazz, String... args) {
        if (fields == null || fields.isEmpty()) {
            return new HashMap<>();
        }

        String key = redisKey.toFormat(args);
        List<Object> values = redisTemplate.opsForHash().multiGet(key, fields.stream().map(f -> (Object) f).collect(Collectors.toList()));

        Map<String, T> result = new HashMap<>(fields.size());
        for (int i = 0; i < fields.size(); i++) {
            Object value = values.get(i);
            if (value != null) {
                String jsonValue = String.valueOf(value);
                if (StringUtils.hasText(jsonValue)) {
                    T obj = JsonUtil.parseObject(jsonValue, clazz);
                    if (obj != null) {
                        result.put(fields.get(i), obj);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 获取所有Hash字段
     *
     * @param redisKey Redis键
     * @param args     参数
     * @return Hash字段Map
     */
    public Map<Object, Object> hashEntries(SiteRedisKey redisKey, String... args) {
        String key = redisKey.toFormat(args);
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 获取所有Hash字段（从JSON反序列化）
     *
     * @param redisKey Redis键
     * @param clazz    对象类型
     * @param args     参数
     * @param <T>      对象类型
     * @return 对象Map
     */
    public <T> Map<String, T> hashEntriesObjects(SiteRedisKey redisKey, Class<T> clazz, String... args) {
        Map<Object, Object> entries = hashEntries(redisKey, args);
        if (entries == null || entries.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, T> result = new HashMap<>(entries.size());
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            if (entry.getValue() != null) {
                String jsonValue = String.valueOf(entry.getValue());
                if (StringUtils.hasText(jsonValue)) {
                    T obj = JsonUtil.parseObject(jsonValue, clazz);
                    if (obj != null) {
                        result.put(String.valueOf(entry.getKey()), obj);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 判断Hash字段是否存在
     *
     * @param redisKey Redis键
     * @param field    字段
     * @param args     参数
     * @return 是否存在
     */
    public boolean hashExists(SiteRedisKey redisKey, String field, String... args) {
        return Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(redisKey.toFormat(args), field));
    }

    /**
     * 删除Hash字段
     *
     * @param redisKey Redis键
     * @param fields   字段
     * @param args     参数
     */
    public void hashDelete(SiteRedisKey redisKey, Object[] fields, String... args) {
        if (fields != null && fields.length > 0) {
            redisTemplate.opsForHash().delete(redisKey.toFormat(args), fields);
        }
    }

    // ========== List 操作方法 ==========

    /**
     * 向List右侧推入多个值
     *
     * @param redisKey Redis键
     * @param values   值列表
     * @param args     参数
     */
    public void listRightPushAll(SiteRedisKey redisKey, List<String> values, String... args) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        String key = redisKey.toFormat(args);
        redisTemplate.opsForList().rightPushAll(key, values.toArray());

        // 设置过期时间
        long expire = redisKey.getExpire();
        TimeUnit timeUnit = redisKey.getTimeUnit();
        if (expire > 0) {
            redisTemplate.expire(key, expire, timeUnit);
        }
    }

    /**
     * 获取List指定范围的值
     *
     * @param redisKey Redis键
     * @param start    起始位置
     * @param end      结束位置
     * @param args     参数
     * @return 值列表
     */
    public List<String> listRange(SiteRedisKey redisKey, long start, long end, String... args) {
        List<Object> objects = redisTemplate.opsForList().range(redisKey.toFormat(args), start, end);
        if (CollectionUtils.isEmpty(objects)) {
            return Collections.emptyList();
        }
        return objects.stream()
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 获取List长度
     *
     * @param redisKey Redis键
     * @param args     参数
     * @return List长度
     */
    public Long listSize(SiteRedisKey redisKey, String... args) {
        return redisTemplate.opsForList().size(redisKey.toFormat(args));
    }

    /**
     * 删除List
     *
     * @param redisKey Redis键
     * @param args     参数
     */
    public void listDelete(SiteRedisKey redisKey, String... args) {
        redisTemplate.delete(redisKey.toFormat(args));
    }

    /**
     * 原子性替换List数据
     * 使用临时键+重命名确保操作原子性，避免并发查询时数据不一致
     *
     * @param redisKey Redis键
     * @param values   新的数据列表
     * @param args     参数
     */
    public void listAtomicReplace(SiteRedisKey redisKey, List<String> values, String... args) {
        if (CollectionUtils.isEmpty(values)) {
            return;
        }
        
        String finalKey = redisKey.toFormat(args);
        String tempKey = finalKey + ":temp:" + System.currentTimeMillis();
        
        try {
            // 1. 先在临时键中存储新数据
            redisTemplate.opsForList().rightPushAll(tempKey, values.toArray());
            
            // 2. 设置临时键过期时间
            long expire = redisKey.getExpire();
            TimeUnit timeUnit = redisKey.getTimeUnit();
            if (expire > 0) {
                redisTemplate.expire(tempKey, expire, timeUnit);
            }
            
            // 3. 原子性重命名，替换旧数据
            redisTemplate.rename(tempKey, finalKey);
            
        } catch (Exception e) {
            // 如果出错，清理临时键
            redisTemplate.delete(tempKey);
            throw e;
        }
    }
}
