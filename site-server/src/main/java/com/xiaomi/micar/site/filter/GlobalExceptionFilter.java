package com.xiaomi.micar.site.filter;

import com.mi.car.iccc.iccccommonutil.errorcode.IcccCode;
import com.mi.car.iccc.iccccommonutil.exception.IcccBaseException;
import com.mi.car.iccc.iccccommonutil.exception.IcccConstraintViolationException;
import com.mi.car.iccc.iccccommonutil.util.CheckEnumUtil;
import com.mi.car.iccc.iccccommonutil.util.ValidatorUtils;
import com.xiaomi.hera.trace.context.TraceIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.AsyncRpcResult;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.service.GenericService;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/7/17 下午2:46
 */
@Activate(
        group = {CommonConstants.PROVIDER},
        value = "exceptionFilter"
)
@Slf4j
public class GlobalExceptionFilter implements Filter, Filter.Listener {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {

        if (invocation != null && GenericService.class != invoker.getInterface()) {
            Object[] args = invocation.getArguments();
            if (args != null) {
                for (Object arg : args) {
                    if (arg != null) {
                        try {
                            ValidatorUtils.validate(arg);
                            CheckEnumUtil.validCheckEnumWithException(arg);
                        } catch (IllegalAccessException e) {
                            log.error("GlobalExceptionFilter IllegalAccessException", e);
                            return generateReturnResult(invoker, invocation, e);
                        } catch (IcccConstraintViolationException | IllegalArgumentException e) {
                            log.warn("GlobalExceptionFilter.  service={}, method={}, message={}, arg={}",
                                    invoker.getInterface().getName(), invocation.getMethodName(), e.getMessage(), arg);
                            return generateReturnResult(invoker, invocation, e);
                        }
                    }
                }
            }
        }

        return invoker.invoke(invocation);
    }


    @Override
    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {


        setTraceId(appResponse);

        if (appResponse.hasException() && GenericService.class != invoker.getInterface()) {
            Throwable exception = appResponse.getException();
            if (exception instanceof IllegalArgumentException) {
                com.xiaomi.youpin.infra.rpc.Result<Object> result = ResultUtils.paramError(exception.getMessage());
                result.setTraceId(TraceIdUtil.traceId());
                appResponse.setException(null);
                appResponse.setValue(result);
            } else if (exception instanceof IcccBaseException) {
                try {
                    log.info("IcccBaseException 异常处理:", exception);
                    IcccCode icccCode = ((IcccBaseException) exception).getErrorCode();
                    Class<?> result = Class.forName("com.xiaomi.youpin.infra.rpc.Result");
                    Constructor<?> constructor = result.getDeclaredConstructor(int.class, String.class, Object.class);
                    constructor.setAccessible(true);
                    Object instance = constructor.newInstance(icccCode.getCode(), icccCode.getMessage(), null);

                    Field field = result.getDeclaredField("traceId");
                    field.setAccessible(true);
                    field.set(instance, TraceIdUtil.traceId());

                    appResponse.setException(null);
                    appResponse.setValue(instance);
                } catch (ClassNotFoundException | NoSuchMethodException | InvocationTargetException |
                         InstantiationException | IllegalAccessException | NoSuchFieldException e) {
                    throw new RuntimeException(e);
                }
            } else if (exception instanceof SiteBizException) {
                try {
                    RtCode rtCode = ((SiteBizException) exception).getRtCode();
                    Class<?> result = Class.forName("com.xiaomi.youpin.infra.rpc.Result");
                    Constructor<?> constructor = result.getDeclaredConstructor(int.class, String.class, Object.class);
                    constructor.setAccessible(true);
                    Object instance = constructor.newInstance(rtCode.getCode(), exception.getMessage(), null);

                    Field field = result.getDeclaredField("traceId");
                    field.setAccessible(true);
                    field.set(instance, TraceIdUtil.traceId());

                    appResponse.setException(null);
                    appResponse.setValue(instance);
                } catch (ClassNotFoundException | NoSuchMethodException | InvocationTargetException |
                         InstantiationException | IllegalAccessException | NoSuchFieldException e) {
                    throw new RuntimeException(e);
                }
            } else {
                appResponse.setException(null);
                appResponse.setValue(ResultUtils.commonError(RtCode.ERROR.getMessage()));
                log.error("GlobalExceptionFilter catch exception", exception);
            }
        }
    }

    @Override
    public void onError(Throwable t, Invoker<?> invoker, Invocation invocation) {
        log.error("GlobalExceptionFilter catch Throwable", t);
    }

    private void setTraceId(Result result) {
        Object resultValue = result == null ? null : result.getValue();

        if (result == null) {
            return;
        }

        try {
            if (resultValue instanceof com.xiaomi.youpin.infra.rpc.Result) {
                com.xiaomi.youpin.infra.rpc.Result instance = (com.xiaomi.youpin.infra.rpc.Result) resultValue;
                Class<?> clazzName = Class.forName("com.xiaomi.youpin.infra.rpc.Result");
                Field field = clazzName.getDeclaredField("traceId");
                field.setAccessible(true);
                field.set(instance, TraceIdUtil.traceId());
            }
        } catch (IllegalAccessException | NoSuchFieldException | ClassNotFoundException e) {
            log.error("setTraceId失败, {}", result);
        }

    }


    private Result generateReturnResult(Invoker<?> invoker, Invocation invocation, Exception e1) {
        try {
            AsyncRpcResult asyncRpcResult = new AsyncRpcResult(new CompletableFuture<>(), invocation);
            Method method = invoker.getInterface().getMethod(invocation.getMethodName(),
                    invocation.getParameterTypes());
            Class<?> returnType = method.getReturnType();
            com.xiaomi.youpin.infra.rpc.Result<Object> result = ResultUtils.paramError(e1.getMessage());
            asyncRpcResult.setValue(result);
            return asyncRpcResult;
        } catch (Exception e) {
            log.error("GlobalExceptionFilter generateReturnResult error", e);
            return AsyncRpcResult.newDefaultAsyncResult(e, invocation);
        }
    }
}
