package com.xiaomi.micar.site.utils;

import com.xiaomi.micar.site.filter.ResultUtils;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;

/**
 * Result 创建工具类
 * 提供创建带有自定义 code 和 message 的 Result 对象的工具方法
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResultHelper {

    /**
     * 创建带有自定义 code 和 message 的失败 Result
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     Result 的泛型类型
     * @return 失败的 Result 对象
     */
    public static <T> Result<T> fail(Integer code, String message) {
        return fail(code, message, null);
    }

    /**
     * 创建带有自定义 code、message 和 data 的失败 Result
     *
     * @param code    错误码
     * @param message 错误消息
     * @param data    数据对象
     * @param <T>     Result 的泛型类型
     * @return 失败的 Result 对象
     */
    @SuppressWarnings("unchecked")
    public static <T> Result<T> fail(Integer code, String message, T data) {
        if (code == null) {
            log.warn("错误码为空，使用默认业务错误");
            return ResultUtils.bizError(message != null ? message : "未知错误");
        }

        try {
            // 使用反射创建 Result 对象
            Class<?> resultClass = Class.forName("com.xiaomi.youpin.infra.rpc.Result");
            Constructor<?> constructor = resultClass.getDeclaredConstructor(int.class, String.class, Object.class);
            constructor.setAccessible(true);
            Object resultInstance = constructor.newInstance(code, message, data);
            return (Result<T>) resultInstance;
        } catch (Exception e) {
            log.error("创建自定义Result失败, code={}, message={}", code, message, e);
            // 回退到默认的业务错误
            return ResultUtils.bizError(message != null ? message : "创建Result失败");
        }
    }

}
