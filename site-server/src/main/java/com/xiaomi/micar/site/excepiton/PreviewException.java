package com.xiaomi.micar.site.excepiton;

import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

/**
 * 页面预览异常
 * 当预览过程中发生错误时抛出此异常
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
public class PreviewException extends RuntimeException {

    private final ErrorCode errorCode;

    public PreviewException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public PreviewException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    // 常用的预览异常工厂方法
    public static PreviewException paramError(String message) {
        return new PreviewException(GeneralCodes.ParamError, message);
    }

    public static PreviewException permissionDenied(String message) {
        return new PreviewException(GeneralCodes.ParamError, message);
    }

    public static PreviewException internalError(String message) {
        return new PreviewException(GeneralCodes.InternalError, message);
    }

    public static PreviewException internalError(String message, Throwable cause) {
        return new PreviewException(GeneralCodes.InternalError, message, cause);
    }
}
