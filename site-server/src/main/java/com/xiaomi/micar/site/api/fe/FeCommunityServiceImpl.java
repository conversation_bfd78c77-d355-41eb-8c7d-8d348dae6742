package com.xiaomi.micar.site.api.fe;


import com.xiaomi.micar.site.component.TabFeedsComponent;
import com.xiaomi.micar.site.api.fe.aop.FeLoginCheck;
import com.xiaomi.micar.site.api.model.FeedsLoadReq;
import com.xiaomi.micar.site.api.model.GetPostDetailReq;
import com.xiaomi.micar.site.api.model.GetPostRelationReq;
import com.xiaomi.micar.site.api.model.LoadFeedsReq;
import com.xiaomi.micar.site.api.model.PostLikeReq;
import com.xiaomi.micar.site.model.PostActionResponse;
import com.xiaomi.micar.site.service.CommunityPostCacheService;
import com.xiaomi.micar.site.service.FeedsLoadService;
import com.xiaomi.micar.site.service.remote.RemoteCommunityDataSource;
import com.xiaomi.micar.site.utils.ResultHelper;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 社区服务
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Slf4j
@ApiModule(value = "社区服务接口", apiInterface = FeCommunityService.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class FeCommunityServiceImpl implements FeCommunityService {

    @Resource
    private RemoteCommunityDataSource communityDataSource;

    @Resource
    private CommunityPostCacheService communityPostCacheService;


    @Resource
    private FeedsLoadService feedsLoadService;

    @FeLoginCheck(required = false)
    @ApiDoc(value = "加载活动精彩瞬间")
    public Result<?> loadActivityFeeds(LoadFeedsReq request) {

        Map<String, Object> params = new HashMap<>();
        // 设置分页参数
        params.put("limit", request.getLimit());
        // 设置offset参数
        Integer offset = Optional.ofNullable(request.getOffset()).orElse(0);
        params.put("after", offset);
        // 设置tabId参数
        String tabId = Optional.ofNullable(request.getTabId()).orElse("all");
        params.put("tabId", tabId);
        // 设置过滤ID（如果有）
        if (request.getFilterIds() != null && !request.getFilterIds().isEmpty()) {
            params.put("filterIds", request.getFilterIds());
        }
        TabFeedsComponent.FeedData feedData = communityDataSource.fetchTabFeeds(request.getMid(), params);
        return Result.success(feedData);
    }

    @Override
    @FeLoginCheck
    @ApiDoc(value = "获取帖子点赞状态、点赞数量、评论数量")
    public Result<?> getPostRelation(GetPostRelationReq request) {
        Assert.hasLength(request.getPostIds(), "请求参数有误");
        return communityDataSource.getPostRelation(request.getMid(), request.getPostIds());
    }

    @Override
    @FeLoginCheck
    @ApiDoc(value = "帖子点赞")
    public Result<Void> postLike(PostLikeReq request) {
        Assert.hasLength(request.getPostId(), "请求参数有误");
        PostActionResponse postActionResponse = communityDataSource.likePost(request.getMid(), request.getPostId());
        if (postActionResponse.isSuccess()){
            return Result.success(null);
        }
        return ResultHelper.fail(postActionResponse.getCode(),postActionResponse.getMessage());
    }

    @Override
    @FeLoginCheck
    @ApiDoc(value = "帖子取消点赞")
    public Result<Void> postCancelLike(PostLikeReq request) {
        PostActionResponse postActionResponse = communityDataSource.unlikePost(request.getMid(), request.getPostId());
        if (postActionResponse.isSuccess()){
            return Result.success(null);
        }
        return ResultHelper.fail(postActionResponse.getCode(),postActionResponse.getMessage());
    }

    @Override
    @FeLoginCheck(required = false)
    @ApiDoc(value = "获取帖子详情")
    public String getPostDetail(GetPostDetailReq request) {
        Assert.notEmpty(request.getPostIds(), "请求参数有误");

        String resultJsonString = communityPostCacheService.getPostDetailResultString(request.getPostIds());

        log.info("获取帖子详情，请求数量: {}, postIds: {}",
                request.getPostIds().size(), request.getPostIds());

        return resultJsonString;
    }


    @Override
    public String loadFeeds(FeedsLoadReq request) {
        try {
            log.info("Dubbo服务加载Feed流数据: pageId={}, moduleId={}, offset={}",
                    request.getPageId(), request.getModuleId(), request.getOffset());

            String jsonResponse = feedsLoadService.loadFeeds(request);

            log.info("Dubbo服务Feed流数据加载完成: pageId={}, moduleId={}",
                    request.getPageId(), request.getModuleId());

            return jsonResponse;

        } catch (Exception e) {
            log.error("Dubbo服务加载Feed流数据异常: pageId={}, moduleId={}",
                    request.getPageId(), request.getModuleId(), e);
            return "{\"code\":500,\"message\":\"加载数据失败: " + e.getMessage().replace("\"", "\\\"") + "\",\"success\":false}";
        }
    }

}
