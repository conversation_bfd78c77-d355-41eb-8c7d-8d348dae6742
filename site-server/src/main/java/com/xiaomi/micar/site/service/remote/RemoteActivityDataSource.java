package com.xiaomi.micar.site.service.remote;

import com.xiaomi.car.activity.external.api.ActivityForSiteApi;
import com.xiaomi.car.activity.external.api.req.ActivityListQueryReq;
import com.xiaomi.car.activity.external.api.resp.ActivityListResp;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 活动数据源远程实现
 * 负责通过Dubbo接口获取活动相关数据
 */
@Slf4j
@Component
public class RemoteActivityDataSource {

    @DubboReference(
            group = "${dubbo.consumer.activity-site}",
            timeout = 3000,
            check = false,
            registry = {"carlifeRegistry"},
            interfaceClass = ActivityForSiteApi.class)
    private ActivityForSiteApi activityForSiteApi;


    public ActivityListResp queryActivityList(ActivityListQueryReq activityQueryReq) {
        // 记录请求参数摘要
        log.debug("向活动服务发送请求，用户ID: {}, 分页大小: {}",
                activityQueryReq.getMid(), activityQueryReq.getLimit());

        // 调用Dubbo接口获取数据
        Result<ActivityListResp> response = activityForSiteApi.queryActivityList(activityQueryReq);

        // 验证响应
        if (isSuccessResponse(response)) {
            return response.getData();
        } else {
            log.error("获取活动列表失败，错误码: {}, 错误信息: {}, 响应: {}",
                    response != null ? response.getCode() : "null",
                    response != null ? response.getMessage() : "null",
                    response);
            throw new RuntimeException("获取活动列表失败: "
                    + (response != null ? response.getMessage() : "响应为空"));
        }
    }

    /**
     * 检查响应是否成功
     *
     * @param response API响应
     * @return 如果响应成功且包含数据则返回true
     */
    private boolean isSuccessResponse(Result<ActivityListResp> response) {
        return response != null
                && response.getCode() == GeneralCodes.OK.getCode()
                && response.getData() != null;
    }
}
