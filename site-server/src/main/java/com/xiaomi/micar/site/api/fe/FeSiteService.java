package com.xiaomi.micar.site.api.fe;

import com.xiaomi.micar.site.api.model.GetSiteConfResp;
import com.xiaomi.micar.site.component.TabsComponent;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.Map;

/**
 * 系统配置服务接口
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
public interface FeSiteService {

    /**
     * 站点配置获取请求
     *
     * @return resp
     */
    Result<GetSiteConfResp> getSiteConfig(Map<String, String> request);


    /**
     * 获取页面顶部tab配置
     *
     * @param request 请求参数
     * @return resp
     */
    Result<TabsComponent<Void>> topTabBar(Map<String, String> request);
}
