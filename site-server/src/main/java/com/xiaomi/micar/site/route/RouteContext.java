package com.xiaomi.micar.site.route;

import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 路由上下文：携带当前请求在各个维度上“可用/命中”的取值集合。
 * key 为维度名（必须与配置的 dimensionOrder 一致），value 为该维度允许的取值列表。
 */
@Data
public class RouteContext {
    /**
     * 例如：{"UserStrategyEnum": ["car_owner"], "CarModelEnum": ["su7", "su7Pro"]}
     */
    private Map<String, List<String>> dimValues;

    public static RouteContext of(Map<String, List<String>> dimValues) {
        RouteContext ctx = new RouteContext();
        ctx.setDimValues(dimValues);
        return ctx;
    }

    public List<String> valuesOf(String dim) {
        if (dimValues == null) return Collections.emptyList();
        List<String> vals = dimValues.get(dim);
        return vals == null ? Collections.emptyList() : vals;
    }
}

