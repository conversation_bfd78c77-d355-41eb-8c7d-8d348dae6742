package com.xiaomi.micar.site.service.assembly;

import com.xiaomi.micar.site.model.ExperimentResult;
import com.xiaomi.micar.site.cache.model.CacheBuilderResult;
import com.xiaomi.micar.site.cache.model.PageCacheContext;
import com.xiaomi.micar.site.core.monitoring.CacheMonitoring;
import com.xiaomi.micar.site.core.monitoring.CacheMonitoringEnhancer;
import com.xiaomi.micar.site.service.assembly.provider.SiteDataProviderFactory;
import com.xiaomi.micar.site.service.assembly.provider.SiteDataProviderFactory.DataSourceType;
import com.xiaomi.micar.site.cache.service.PageCacheBuilder;
import com.xiaomi.micar.site.service.config.PageConfigService;
import com.xiaomi.micar.site.utils.CombinationKeyGenerate;

import com.xiaomi.micar.site.cache.CachePostProcessor;
import com.xiaomi.micar.site.cache.SitePageCacheService;
import com.xiaomi.micar.site.cache.model.CacheState;
import com.xiaomi.micar.site.cache.service.CacheSyncService;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 页面缓存预加载服务
 * 负责页面缓存的预构建、后置处理和存储同步
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Service
public class PageCachePreloadService {

    @Resource
    private SitePageCacheService cacheService;
    @Resource
    private PageConfigService pageConfigService;
    @Resource
    private PageResponseBuilder pageResponseBuilder;
    @Resource
    private PageCacheBuilder cacheBuilder;
    @Resource
    private SiteDataProviderFactory siteDataProviderFactory;
    @Resource
    private List<CachePostProcessor> cachePostProcessors;
    @Resource
    private CacheMonitoringEnhancer monitoringEnhancer;
    @Resource
    private com.xiaomi.micar.site.service.cache.CacheSyncOrchestrator cacheSyncOrchestrator;


    /**
     * 预加载页面缓存（指定数据源）
     */
    public void preloadAllPageCombinations(DataSourceType dataSourceType) {
        // 监控数据获取阶段
        List<SitePageConfig> pageConfigs = monitoringEnhancer.monitorStage(
            "preload.data_fetch",
            () -> siteDataProviderFactory.getAllPageConfigs(dataSourceType),
            "stage=data_fetch", "type=page_configs"
        );

        List<SiteExpConfig> experiments = monitoringEnhancer.monitorStage(
            "preload.data_fetch",
            () -> siteDataProviderFactory.getAllExperiments(dataSourceType),
            "stage=data_fetch", "type=experiments"
        );

        List<SiteExpBuckets> expBuckets = monitoringEnhancer.monitorStage(
            "preload.data_fetch",
            () -> siteDataProviderFactory.getAllExperimentBuckets(dataSourceType),
            "stage=data_fetch", "type=exp_buckets"
        );

        // 记录数据获取统计
        monitoringEnhancer.recordCount("preload.pages_count", pageConfigs.size(), "source=" + dataSourceType);
        monitoringEnhancer.recordCount("preload.experiments_count", experiments.size(), "source=" + dataSourceType);

        preloadAllPageCombinations(pageConfigs, experiments, expBuckets);
    }

    /**
     * 预加载页面缓存（默认数据源）
     */
    @CacheMonitoring(
        value = "preload.default_combinations",
        type = CacheMonitoring.MonitoringType.PERFORMANCE,
        tags = {"operation=preload", "service=cache_preload", "source=default"}
    )
    public void preloadAllPageCombinations() {
        preloadAllPageCombinations(siteDataProviderFactory.getDefaultType());
    }

    /**
     * 预加载页面缓存核心方法
     * 构建所有页面的缓存组合并同步到存储
     */
    public void preloadAllPageCombinations(
            List<SitePageConfig> pageConfigs,
            List<SiteExpConfig> experiments,
            List<SiteExpBuckets> expBuckets) {

        if (CollectionUtils.isEmpty(pageConfigs)) {
            log.warn("页面配置为空，跳过缓存预加载");
            return;
        }

        long startTime = System.currentTimeMillis();
        log.info("开始预加载页面缓存，页面数量: {}", pageConfigs.size());

        try {
            // 监控配置解析阶段
            CacheState cacheState = monitoringEnhancer.monitorStage(
                "preload.cache_build",
                () -> buildCacheState(pageConfigs, experiments, expBuckets),
                "stage=cache_state_build"
            );

            // 监控页面缓存构建阶段
            CacheBuilderResult result = monitoringEnhancer.monitorStage(
                "preload.cache_build",
                () -> buildPageCache(cacheState),
                "stage=page_cache_build"
            );

            if (!result.isSuccess()) {
                log.error("缓存构建失败: {}", result.getErrorMessage());
                throw new RuntimeException("缓存构建失败: " + result.getErrorMessage());
            }

            // 监控后置处理与同步阶段
            monitoringEnhancer.monitorStage(
                "preload.post_process_sync",
                () -> postProcessAndSyncCache(cacheState),
                "stage=post_process_sync"
            );

            // 记录生成的缓存组合数量
            monitoringEnhancer.recordCount("preload.combinations_count",
                result.getTotalCachedCombinations(), "status=success");

            log.info("缓存预加载完成，耗时: {}ms，生成组合: {}个",
                    System.currentTimeMillis() - startTime, result.getTotalCachedCombinations());

        } catch (Exception e) {
            log.error("缓存预加载失败: {}", e.getMessage(), e);
            throw new RuntimeException("缓存预加载失败", e);
        }
    }

    /**
     * 构建缓存状态
     */
    private CacheState buildCacheState(List<SitePageConfig> pageConfigs,
                                      List<SiteExpConfig> experiments,
                                      List<SiteExpBuckets> expBuckets) {
        CacheState cacheState = cacheService.createNewCacheState();

        for (SitePageConfig pageConfig : pageConfigs) {
            PageConfigModel pageConfigModel = pageConfigService.createPageConfigObject(
                    pageConfig, experiments, expBuckets);
            if (pageConfigModel != null) {
                cacheState.setPageConfig(pageConfig.getPageId(), pageConfigModel);
            }
        }

        return cacheState;
    }

    /**
     * 构建页面缓存
     */
    private CacheBuilderResult buildPageCache(CacheState cacheState) {
        PageCacheBuilder.CacheContext context = PageCacheBuilder.CacheContext.builder()
                .pageRespBuilder(this::buildPageResponse)
                .combinationKeyGenerator(CombinationKeyGenerate::generateCombinationKey)
                .cacheState(cacheState)
                .build();

        return cacheBuilder.buildPageCombinationCache(context);
    }

    /**
     * 构建页面响应
     */
    private PageRespV2 buildPageResponse(PageCacheContext context) {
        ExperimentResult experimentResult = new ExperimentResult(context.getApplicableExperiments());

        return pageResponseBuilder.buildPageResponse(
                context.getPageConfig(),
                context.getPageId(),
                context.getStrategy(),
                null,
                null,
                experimentResult,
                false,
                context.getProcessedComponents()  // 传递页面级别的去重标记
        );
    }

    /**
     * 后置处理并同步缓存
     * 执行后置处理器并按策略同步到存储/分发
     */
    private void postProcessAndSyncCache(CacheState cacheState) {
        Map<String, Map<String, PageRespV2>> pageCacheMap = collectPageResponseMap(cacheState);

        if (pageCacheMap.isEmpty()) {
            log.warn("缓存数据为空，跳过发布");
            return;
        }

        // 执行后置处理器
        applyPostProcessors(pageCacheMap);

        // 同步到存储（按策略分流，传递配置版本信息）
        cacheSyncOrchestrator.syncByPolicy(cacheState, pageCacheMap);
    }

    /**
     * 汇集页面缓存映射
     */
    private Map<String, Map<String, PageRespV2>> collectPageResponseMap(CacheState cacheState) {
        return cacheState.getAllPageResponseObjects();
    }

    /**
     * 执行后置处理器
     */
    private void applyPostProcessors(Map<String, Map<String, PageRespV2>> pageCacheMap) {
        if (CollectionUtils.isEmpty(cachePostProcessors)) {
            return;
        }

        cachePostProcessors.stream()
                .sorted(Comparator.comparingInt(CachePostProcessor::getOrder))
                .forEach(processor -> {
                    try {
                        for (Map.Entry<String, Map<String, PageRespV2>> entry : pageCacheMap.entrySet()) {
                            processor.process(entry.getValue());
                        }
                    } catch (Exception e) {
                        log.error("后置处理器执行失败: {}", processor.getName(), e);
                    }
                });
    }

}
