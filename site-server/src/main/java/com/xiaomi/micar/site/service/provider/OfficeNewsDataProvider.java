package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.component.NewsComponent;
import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.config.SiteComponentProperties;
import com.xiaomi.micar.site.service.remote.RemoteCommunityDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 官方动态数据提供者
 */
@Slf4j
@org.springframework.stereotype.Component
public class OfficeNewsDataProvider extends AbstractComponentDataProvider {

    /**
     * 请求ID生成时的随机数上限
     */
    private static final int REQUEST_ID_RANDOM_BOUND = 10000;
    public static final int DEFAULT_LIMIT = 8;

    @Resource
    private SiteComponentProperties properties;
    @Resource
    private RemoteCommunityDataSource communityDataSource;

    @Override
    protected boolean validateComponentType(Component componentInfo) {
        return componentInfo instanceof NewsComponent;
    }

    @Override
    protected void doProcess(ResponseContext context, Component componentInfo) {
        NewsComponent newsComponent = (NewsComponent) componentInfo;


        // 生成请求ID
        String requestId = "REQ-" + System.currentTimeMillis() + "-"
                + (int) (Math.random() * REQUEST_ID_RANDOM_BOUND);
        log.info("[{}] 开始处理官方动态组件", requestId);

        // 检查组件是否启用
        SiteComponentProperties.ComponentApiConfig config = properties.getApi().getOfficeNews();
        if (!config.isEnabled()) {
            log.warn("[{}] 官方动态组件已通过配置禁用", requestId);
            newsComponent.setData(Collections.emptyList());
            return;
        }

        // 获取用户ID和查询参数
        String userId = context.getUserId();
        Map<String, Object> queryParams = Optional.ofNullable(context.getQueryParams())
                .map(HashMap::new)
                .orElseGet(HashMap::new);

        queryParams.put("limit", DEFAULT_LIMIT);


        log.debug("[{}] 处理参数: userId={}, 参数: {}", requestId, userId, queryParams);

        // 使用重试机制调用数据源获取数据
        String cacheKey = generateCacheKey("office_news", userId, queryParams);
        log.debug("[{}] 生成缓存键: {}", requestId, cacheKey);

        log.info("[{}] 使用重试机制调用API获取官方动态数据", requestId);

        // 直接获取转换后的 List<ArticleComponent> 数据
        List<ArticleElement> newsItems = executeWithRetry(cacheKey,
                () -> communityDataSource.fetchOfficeNews(userId, queryParams));

        // 直接设置组件数据
        newsComponent.setData(newsItems != null ? newsItems : Collections.emptyList());

        // 记录处理结果
        int newsCount = newsComponent.getData() != null ? newsComponent.getData().size() : 0;
        log.info("[{}] 官方动态组件处理完成，填充了 {} 条新闻", requestId, newsCount);
    }

    /**
     * 生成缓存键
     *
     * @param prefix      前缀
     * @param userId      用户ID
     * @param queryParams 查询参数
     * @return 缓存键
     */
    private String generateCacheKey(String prefix, String userId, Map<String, Object> queryParams) {
        StringBuilder sb = new StringBuilder(prefix);
        sb.append("_").append(userId != null ? userId : "anonymous");

        // 添加查询参数中的关键值到缓存键
        if (queryParams != null && !queryParams.isEmpty()) {
            queryParams.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> sb.append("_").append(entry.getKey())
                            .append("=").append(entry.getValue()));
        }

        return sb.toString();
    }

    @Override
    protected void handleProcessingError(Component componentInfo, Exception e) {
        if (componentInfo instanceof NewsComponent) {
            NewsComponent newsComponent = (NewsComponent) componentInfo;
            // 设置空数据，确保前端不会因为数据缺失而崩溃
            newsComponent.setData(Collections.emptyList());
            log.error("News组件处理失败，已设置空数据: {}", e.getMessage());
        }
    }
}
