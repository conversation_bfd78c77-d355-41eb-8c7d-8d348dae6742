package com.xiaomi.micar.site.service.remote;

import com.xiaomi.micar.site.component.model.TabFeedsResponse;
import com.xiaomi.micar.site.model.OfficeNewsResponse;
import com.xiaomi.micar.site.model.PostDetailResponse;
import com.xiaomi.micar.site.model.PostDetailWithAuthorResponse;
import com.xiaomi.micar.site.model.PostRelationResponse;
import com.xiaomi.micar.site.model.response.PostIdsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * Feign Client for Community API
 */
@FeignClient(name = "CommunityApiService", url = "${iccc.site.api.base-url}")
public interface CommunityApiFeignClient {

    //internal 发现-探索
    String OFFICE_NEWS_PATH = "/api/internal/site/overall/official/v1/list";
    //internal 发现-活动
    String TAB_FEEDS_PATH = "/api/internal/site/highlight/v1/list";
    //（409）查询帖子点赞关系
    String POST_RELATION_PATH = "/api/internal/site/post/v1/relation";
    // 点赞
    String POST_LIKE_PATH = "/api/internal/site/post/v1/like";
    // 取消点赞
    String POST_UNLIKE_PATH = "/api/internal/site/post/v1/like/cancel";
    // 查询帖子详情
    String POST_DETAIL_PATH = "/api/internal/site/post/v1/detail";
    // 查询帖子详情（带作者信息）
    String POST_DETAIL_WITH_AUTHOR_PATH = "/api/internal/site/post/v1/detailWithAuthor";
    // 根据标签/作者获取帖子ID列表
    String GET_POST_IDS_PATH = "/api/internal/site/post/v1/getPostIds";

    @GetMapping(value = OFFICE_NEWS_PATH, produces = "application/json")
    OfficeNewsResponse getOfficeNews(@RequestParam Map<String, Object> params);

    @GetMapping(value = TAB_FEEDS_PATH, produces = "application/json")
    TabFeedsResponse getTabFeeds(@RequestParam Map<String, Object> params);

    @GetMapping(value = POST_RELATION_PATH, produces = "application/json")
    PostRelationResponse getPostRelation(@RequestParam("userId") String userId, @RequestParam("postIds") String postIds);

    @PostMapping(value = POST_LIKE_PATH)
    Map<String, Object> likePost(@RequestBody Map<String, Object> body);

    @PostMapping(value = POST_UNLIKE_PATH)
    Map<String, Object> unlikePost(@RequestBody Map<String, Object> body);

    /**
     * 获取帖子详情（列表入参版本）
     * 内部会将List转换为逗号分隔的字符串
     *
     * @param postIds 帖子ID列表
     * @return 帖子详情响应
     */
    @GetMapping(value = POST_DETAIL_PATH, produces = "application/json")
    PostDetailResponse getPostDetailList(@RequestParam("postIds") String postIds);

    @GetMapping(value = GET_POST_IDS_PATH, produces = "application/json")
    PostIdsResponse getPostIds(@RequestParam("operaTags") String operaTags,
                               @RequestParam("userIds") String userIds,
                               @RequestParam("limit") Integer limit);

    /**
     * 获取帖子详情（带作者信息）
     * 新接口，返回更丰富的作者信息和话题信息
     *
     * @param postIds 帖子ID列表，逗号分隔，最大数量20
     * @return 帖子详情响应（直接返回List格式）
     */
    @GetMapping(value = POST_DETAIL_WITH_AUTHOR_PATH, produces = "application/json")
    PostDetailWithAuthorResponse getPostDetailWithAuthor(@RequestParam("postIds") String postIds);

}
