package com.xiaomi.micar.site.convert;

import com.xiaomi.micar.site.component.element.ActionElement;
import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.element.ImageElement;
import com.xiaomi.micar.site.component.element.VideoElement;
import com.xiaomi.micar.site.component.model.PublishInfo;
import com.xiaomi.micar.site.component.enums.RefTypeEnum;
import com.xiaomi.micar.site.model.CommunityPostType;
import com.xiaomi.micar.site.model.OfficeNewsResponse;
import com.xiaomi.micar.site.service.http.RouterService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社区数据转换工具类
 * 专门处理社区相关数据的转换逻辑
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
public class CommunityDataConverter {

    // 时间格式化常量
    private static final DateTimeFormatter MONTH_DAY_FORMATTER = DateTimeFormatter.ofPattern("MM-dd");
    private static final DateTimeFormatter FULL_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter FULL_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 时间阈值常量
    private static final long SECONDS_THRESHOLD = 60;
    private static final long MINUTES_THRESHOLD = 60;

    /**
     * 将 OfficeNewsRecord 转换为 ArticleElement（带 RouterService）
     * 用于需要生成跳转链接的场景
     */
    public static ArticleElement convertFromOfficeNewsRecord(OfficeNewsResponse.OfficeNewsRecord record, RouterService routerService) {
        if (record == null) {
            return null;
        }

        ArticleElement newsItem = new ArticleElement();
        newsItem.track(record.getPostId(), null, String.valueOf(record.getType()));

        // 帖子id 设置数据唯一 id 为帖子 id、RN用于打点使用
        newsItem.setId(record.getPostId());
        newsItem.setRefId(record.getPostId());
        newsItem.setRefType(RefTypeEnum.COMMUNITY.getCode());

        // 设置标题和摘要 - 优先使用 title
        if (StringUtils.isNotBlank(record.getTitle())) {
            newsItem.setTitle(record.getTitle());
            newsItem.setSubTitle(record.getDisplaySummary());
        } else if (StringUtils.isNotBlank(record.getDisplaySummary())) {
            newsItem.setTitle(record.getDisplaySummary());
        } else {
            newsItem.setTitle(""); // 默认空标题
        }

        // 设置发布信息（新接口有更丰富的作者信息）
        if (record.getAuthor() != null) {
            PublishInfo publishInfo = new PublishInfo();
            publishInfo.setAuthor(record.getAuthor().getUserName());
            publishInfo.setAvatar(record.getAuthor().getIcon());
            publishInfo.setEUserId(record.getAuthor().getEUserId());
            publishInfo.setIsEmployee(record.getAuthor().getIsEmployee());

            if (record.getCreateTime() != null) {
                publishInfo.setTime(formatTimeByRules(record.getCreateTime()));
            }

            newsItem.setPublishInfo(publishInfo);
        }

        // 设置图片
        newsItem.setType(CommunityPostType.VIDEO_ARTICLE.getCode().equals(record.getType()) ? "video" : "image");
        if (!CollectionUtils.isEmpty(record.getImgList())) {
            OfficeNewsResponse.ImageItem feedCover = record.getFeedCover() == null ? record.getImgList().get(0) : record.getFeedCover();
            ImageElement image = new ImageElement();
            image.setSrc(feedCover.getImageUrl());
            image.setWidth(feedCover.getWidth());
            image.setHeight(feedCover.getHeight());
            newsItem.setImage(image);
        }

        // 设置视频
        if (!CollectionUtils.isEmpty(record.getVideoList())) {
            OfficeNewsResponse.ImageItem feedCover = record.getFeedCover();
            newsItem.setVideo(record.getVideoList()
                    .stream()
                    .findFirst()
                    .map(videoInfo -> {
                        VideoElement video = new VideoElement();
                        video.setCover(feedCover != null ? feedCover.getImageUrl() : videoInfo.getCover());
                        video.setHeight(feedCover != null ? feedCover.getHeight() : videoInfo.getHeight());
                        video.setWidth(feedCover != null ? feedCover.getWidth() : videoInfo.getWidth());
                        video.setDuration(videoInfo.getDuration());
                        return video;
                    })
                    .orElse(null));
        }

        // 设置浏览次数（新接口提供的字段）
        if (record.getViewCount() != null) {
            newsItem.setViewCount(record.getViewCount());
        }

        // 设置跳转链接和按钮（如果提供了 RouterService）
        if (routerService != null) {
            ActionElement action = createAction(record, routerService);
            newsItem.setAction(action);

            // 设置交互按钮
            newsItem.setButtons(createButtons(record));
        }

        return newsItem;
    }

    /**
     * 将 OfficeNewsRecord 列表转换为 ArticleElement 列表
     */
    public static List<ArticleElement> convertFromOfficeNewsRecords(List<OfficeNewsResponse.OfficeNewsRecord> records) {
        return convertFromOfficeNewsRecords(records, null);
    }

    /**
     * 将 OfficeNewsRecord 列表转换为 ArticleElement 列表（带 RouterService）
     */
    public static List<ArticleElement> convertFromOfficeNewsRecords(List<OfficeNewsResponse.OfficeNewsRecord> records, RouterService routerService) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        return records.stream()
                .map(record -> convertFromOfficeNewsRecord(record, routerService))
                .collect(Collectors.toList());
    }

    /**
     * 按规则格式化时间（迁移自 RemoteCommunityDataSource）
     * 保持原有的复杂时间格式化逻辑
     */
    private static String formatTimeByRules(long timestamp) {
        // 转换时间戳为Instant
        Instant publishInstant = Instant.ofEpochMilli(timestamp);
        Instant currentInstant = Instant.now();

        // 转换为LocalDateTime以便进行日期比较
        LocalDateTime publishDateTime = LocalDateTime.ofInstant(publishInstant, ZoneId.systemDefault());
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentInstant, ZoneId.systemDefault());

        // 获取日期部分用于比较是否同一天
        LocalDate publishDate = publishDateTime.toLocalDate();
        LocalDate currentDate = currentDateTime.toLocalDate();

        // 计算时间差
        long secondsDiff = ChronoUnit.SECONDS.between(publishInstant, currentInstant);
        long minutesDiff = ChronoUnit.MINUTES.between(publishInstant, currentInstant);
        long hoursDiff = ChronoUnit.HOURS.between(publishInstant, currentInstant);
        long daysDiff = ChronoUnit.DAYS.between(publishDate, currentDate);

        // 判断是否在同一天
        if (publishDate.equals(currentDate)) {
            if (secondsDiff < SECONDS_THRESHOLD) {
                // 少于60秒，显示"刚刚"
                return "刚刚";
            } else if (minutesDiff < MINUTES_THRESHOLD) {
                // 1-60分钟，显示"X分钟前"
                return minutesDiff + "分钟前";
            } else {
                // 超过60分钟，显示"X小时前"
                return hoursDiff + "小时前";
            }
        } else if (daysDiff == 1) {
            // 昨天
            return "昨天";
        } else if (daysDiff == 2) {
            // 前天
            return "2天前";
        } else if (daysDiff > 2) {
            // 发布日期超过2天
            if (publishDate.getYear() == currentDate.getYear()) {
                // 同年，显示"MM-dd"格式
                return publishDateTime.format(MONTH_DAY_FORMATTER);
            } else {
                // 不同年，显示完整日期
                return publishDateTime.format(FULL_DATE_FORMATTER);
            }
        } else {
            // 兜底方案：使用完整日期时间格式
            return publishDateTime.format(FULL_DATE_TIME_FORMATTER);
        }
    }

    /**
     * 创建交互操作
     *
     * @param record 新闻记录
     * @param routerService 路由服务
     * @return 交互操作
     */
    private static ActionElement createAction(OfficeNewsResponse.OfficeNewsRecord record, RouterService routerService) {
        boolean isVideo = CommunityPostType.VIDEO_ARTICLE.getCode().equals(record.getType());
        ActionElement action = new ActionElement();
        action.setType("link");
        action.setLinkUrl(routerService.getCommunityPostUrl(record.getPostId(), isVideo, null, null));
        return action;
    }

    /**
     * 创建交互按钮列表
     *
     * @param record 新闻记录
     * @return 交互按钮列表
     */
    private static List<ButtonElement> createButtons(OfficeNewsResponse.OfficeNewsRecord record) {
        List<ButtonElement> buttons = new ArrayList<>();

        // 点赞操作
        ButtonElement likeAction = new ButtonElement();
        likeAction.setType("like");
        likeAction.setText(String.valueOf(record.getLikeCnt()));
        likeAction.setStatus(Boolean.TRUE.equals(record.getLike()) ? 1 : 0);
        buttons.add(likeAction);

        return buttons;
    }

}
