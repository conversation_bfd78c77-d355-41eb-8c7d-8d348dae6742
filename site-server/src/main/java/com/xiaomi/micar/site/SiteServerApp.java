package com.xiaomi.micar.site;

import com.alibaba.nacos.api.annotation.NacosProperties;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.xiaomi.mone.dubbo.docs.EnableDubboApiDocs;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 官网站点服务APP
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@SpringBootApplication
@EnableDubboApiDocs
@EnableNacosConfig(
        globalProperties = @NacosProperties(
                serverAddr = "${nacos.config.addr}",
                namespace = "${nacos.config.namespace}"
        )
)
@EnableDubbo
@EnableTransactionManagement(proxyTargetClass = true)
@EnableFeignClients
@EnableScheduling
@ComponentScan(basePackages = {"com.xiaomi.micar.site"})
public class SiteServerApp {

    public static void main(String[] args) {
        SpringApplication.run(SiteServerApp.class, args);
    }
}
