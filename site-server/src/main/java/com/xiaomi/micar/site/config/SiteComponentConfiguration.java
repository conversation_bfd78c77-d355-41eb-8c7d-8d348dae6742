package com.xiaomi.micar.site.config;

import com.xiaomi.micar.site.service.assembly.provider.CustomSiteDataProvider;
import com.xiaomi.micar.site.service.assembly.provider.EventDrivenSiteDataProvider;
import com.xiaomi.micar.site.service.assembly.provider.SiteDataProviderFactory;
import com.xiaomi.micar.site.service.DBSiteDataLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 站点组件统一配置类.
 *
 * <p>整合所有相关的配置类，提供统一的配置入口。</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class SiteComponentConfiguration {

    private EventDrivenSiteDataProvider eventDrivenSiteDataProvider;
    private CustomSiteDataProvider customSiteDataProvider;
    private SiteDataProviderFactory siteDataProviderFactory;
    @Resource
    private DBSiteDataLoader dbSiteDataLoader;

    /**
     * 初始化站点数据提供者
     * 整合了 SiteDataProviderConfig 的功能。
     */
    @PostConstruct
    public void initializeDataProviders() {
        log.info("Initializing site data providers");

        // 创建事件驱动站点数据提供者
        this.eventDrivenSiteDataProvider = createEventDrivenSiteDataProvider();

        // 创建自定义站点数据提供者（如果有数据加载器的话）
        this.customSiteDataProvider = createCustomSiteDataProvider();

        // 创建站点数据提供者工厂
        this.siteDataProviderFactory = createSiteDataProviderFactory();
    }

    /**
     * 创建事件驱动站点数据提供者.
     *
     * @return 事件驱动站点数据提供者
     */
    private EventDrivenSiteDataProvider createEventDrivenSiteDataProvider() {
        // 这里可以根据配置决定是否创建
        // 简化处理，直接创建
        log.info("Creating event-driven site data provider");
        return new EventDrivenSiteDataProvider();
    }

    /**
     * 创建自定义站点数据提供者.
     * 需要用户提供 CustomSiteDataProvider.DataLoader 的实现。
     *
     * @return 自定义站点数据提供者
     */
    private CustomSiteDataProvider createCustomSiteDataProvider() {
        // 如果存在基于数据库的自定义数据加载器，则使用它构建自定义数据提供者
        if (dbSiteDataLoader != null) {
            log.info("Creating custom site data provider with DBSiteDataLoader");
            return new CustomSiteDataProvider(dbSiteDataLoader);
        }
        log.info("Custom site data provider not configured");
        return null;
    }

    /**
     * 创建站点数据提供者工厂.
     *
     * @return 站点数据提供者工厂
     */
    private SiteDataProviderFactory createSiteDataProviderFactory() {
        log.info("Creating site data provider factory");
        SiteDataProviderFactory factory = new SiteDataProviderFactory();

        // 注册事件驱动数据提供者
        if (eventDrivenSiteDataProvider != null) {
            factory.put(SiteDataProviderFactory.DataSourceType.EVENT,
                    eventDrivenSiteDataProvider);
        }

        // 注册自定义数据提供者
        if (customSiteDataProvider != null) {
            factory.put(SiteDataProviderFactory.DataSourceType.CUSTOM,
                    customSiteDataProvider);
        }

        // 设置默认数据源类型
        if (customSiteDataProvider != null) {
            factory.setDefaultType(SiteDataProviderFactory.DataSourceType.CUSTOM);
        } else if (eventDrivenSiteDataProvider != null) {
            factory.setDefaultType(SiteDataProviderFactory.DataSourceType.EVENT);
        }

        return factory;
    }

    /**
     * 获取站点数据提供者工厂
     *
     * @return 站点数据提供者工厂
     */
    public SiteDataProviderFactory getSiteDataProviderFactory() {
        return siteDataProviderFactory;
    }
}
