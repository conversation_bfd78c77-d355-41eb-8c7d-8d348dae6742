package com.xiaomi.micar.site.utils;

import com.xiaomi.micar.site.constants.SiteComponentConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实验工具类
 * 提供共享的实验相关功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class CombinationKeyGenerate {
    
    /**
     * 桶的总数量。
     */
    private static final int BUCKET_VOLUME = 1000;
    public static String generateCombinationKey(
            final String pageId,
            final String strategy) {

        StringBuilder keyBuilder = new StringBuilder()
                .append(pageId)
                .append(SiteComponentConstants.CombinationKey.DELIMITER)
                .append(strategy);
        return keyBuilder.toString();
    }
    /**
     * 生成组合键.
     *
     * @param pageId 页面ID
     * @param strategy 用户组ID
     * @param experimentCombinations 实验组合
     * @return 组合键
     */
    public static String generateCombinationKey(
            final String pageId,
            final String strategy,
            final List<Map<String, String>> experimentCombinations) {

        StringBuilder keyBuilder = new StringBuilder()
                .append(pageId)
                .append(SiteComponentConstants.CombinationKey.DELIMITER)
                .append(strategy);

        if (experimentCombinations != null && !experimentCombinations.isEmpty()) {
            keyBuilder.append(SiteComponentConstants.CombinationKey.DELIMITER)
                    .append(SiteComponentConstants.CombinationKey.EXPERIMENT_PREFIX);

            String combinationsStr = experimentCombinations.stream()
                    .sorted(Comparator.comparing((Map<String, String> m) ->
                            m.get(SiteComponentConstants.Experiment.EXPERIMENT_ID)))
                    .map(m -> m.get(SiteComponentConstants.Experiment.EXPERIMENT_ID)
                            + SiteComponentConstants.CombinationKey.BUCKET_DELIMITER
                            + m.get(SiteComponentConstants.Bucket.BUCKET_ID))
                    .collect(Collectors.joining(
                            SiteComponentConstants.CombinationKey.COMBINATION_DELIMITER));

            keyBuilder.append(combinationsStr);
        }
        return keyBuilder.toString();
    }
    
    /**
     * 获取用户的桶ID
     *
     * @param userId 用户ID
     * @param experimentId 实验ID
     * @return 用户的桶ID，如果生成失败则返回-1
     */
    public static int generateBucketIdForUser(final String userId, final String experimentId) {
        if (userId == null || experimentId == null) {
            return -1;
        }
        
        try {
            // 使用实验ID和用户ID作为种子
            String hashKey = experimentId + ":" + userId;
            
            // 使用HashFunctions工具类的MurmurHash3算法计算哈希值
            int hashValue = HashFunctions.murmurHash3(hashKey);
            return hashValue % BUCKET_VOLUME;
        } catch (Exception e) {
            log.error("Error generating bucket ID for user: {}, experiment: {}", userId, experimentId, e);
            return -1;
        }
    }

}
