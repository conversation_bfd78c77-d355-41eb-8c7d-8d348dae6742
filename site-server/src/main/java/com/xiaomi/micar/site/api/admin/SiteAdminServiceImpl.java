package com.xiaomi.micar.site.api.admin;

import com.xiaomi.micar.site.event.CacheRefreshEvent;
import com.xiaomi.micar.site.service.assembly.SitePageAssemblyService;
import com.xiaomi.micar.site.up.service.UpServiceManager;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;

/**
 * 官网管理服务实现
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Slf4j
@ApiModule(value = "社区服务接口", apiInterface = SiteAdminService.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class SiteAdminServiceImpl implements SiteAdminService {

    @Resource
    private SitePageAssemblyService sitePageAssemblyService;

    @Resource
    private UpServiceManager upServiceManager;

    @Resource
    private ApplicationContext context;

    @Override
    @ApiDoc(value = "更新站点数据缓存")
    public void refreshCache(boolean forceRefresh) {
        context.publishEvent(new CacheRefreshEvent(forceRefresh));
    }

    @ApiDoc(value = "可视化内存数据")
    @Override
    public String visualizeCacheState() {

        String s = sitePageAssemblyService.visualizeCacheState();

        return s;

    }

    @Override
    @ApiDoc(value = "重新加载本地人群数据")
    public void reloadLocalUpData() {
        // 触发加载
        upServiceManager.getUpService(UpServiceManager.LOCAL_UP_SERVICE).loadAll();
    }


}
