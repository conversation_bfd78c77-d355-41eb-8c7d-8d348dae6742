package com.xiaomi.micar.site.service.assembly.provider;

import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 事件驱动的站点数据提供者
 * 通过监听事件获取站点数据
 */
@Slf4j
public class EventDrivenSiteDataProvider implements SiteDataProvider {

    private final List<SitePageConfig> pageConfigs = new CopyOnWriteArrayList<>();
    private final List<SiteExpConfig> experiments = new CopyOnWriteArrayList<>();
    private final List<SiteExpBuckets> experimentBuckets = new CopyOnWriteArrayList<>();

    /**
     * 构造函数
     */
    public EventDrivenSiteDataProvider() {
        log.info("Initialized event-driven site data provider");
    }

    /**
     * 监听页面配置更新事件
     * 
     * @param event 页面配置更新事件
     */
    @EventListener
    public void onPageConfigsUpdated(PageConfigsUpdatedEvent event) {
        log.info("Received page configs update event with {} configs", event.getPageConfigs().size());
        pageConfigs.clear();
        pageConfigs.addAll(event.getPageConfigs());
    }

    /**
     * 监听实验更新事件
     * 
     * @param event 实验更新事件
     */
    @EventListener
    public void onExperimentsUpdated(ExperimentsUpdatedEvent event) {
        log.info("Received experiments update event with {} experiments", event.getExperiments().size());
        experiments.clear();
        experiments.addAll(event.getExperiments());
    }

    /**
     * 监听实验桶更新事件
     * 
     * @param event 实验桶更新事件
     */
    @EventListener
    public void onExperimentBucketsUpdated(ExperimentBucketsUpdatedEvent event) {
        log.info("Received experiment buckets update event with {} buckets", event.getExperimentBuckets().size());
        experimentBuckets.clear();
        experimentBuckets.addAll(event.getExperimentBuckets());
    }

    @Override
    public List<SitePageConfig> getAllPageConfigs() {
        return new ArrayList<>(pageConfigs);
    }

    @Override
    public List<SiteExpConfig> getAllExperiments() {
        return new ArrayList<>(experiments);
    }

    @Override
    public List<SiteExpBuckets> getAllExperimentBuckets() {
        return new ArrayList<>(experimentBuckets);
    }

    /**
     * 页面配置更新事件
     */
    public static class PageConfigsUpdatedEvent {
        private final List<SitePageConfig> pageConfigs;

        public PageConfigsUpdatedEvent(List<SitePageConfig> pageConfigs) {
            this.pageConfigs = pageConfigs;
        }

        public List<SitePageConfig> getPageConfigs() {
            return pageConfigs;
        }
    }

    /**
     * 实验更新事件
     */
    public static class ExperimentsUpdatedEvent {
        private final List<SiteExpConfig> experiments;

        public ExperimentsUpdatedEvent(List<SiteExpConfig> experiments) {
            this.experiments = experiments;
        }

        public List<SiteExpConfig> getExperiments() {
            return experiments;
        }
    }

    /**
     * 实验桶更新事件
     */
    public static class ExperimentBucketsUpdatedEvent {
        private final List<SiteExpBuckets> experimentBuckets;

        public ExperimentBucketsUpdatedEvent(List<SiteExpBuckets> experimentBuckets) {
            this.experimentBuckets = experimentBuckets;
        }

        public List<SiteExpBuckets> getExperimentBuckets() {
            return experimentBuckets;
        }
    }

}
