package com.xiaomi.micar.site.config;

import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.annotation.NacosProperties;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 官网 Nacos 动态配置
 *
 * <AUTHOR>
 * @since 2025/01/03
 */
@Slf4j
@Configuration
@EnableNacosConfig(globalProperties = @NacosProperties(serverAddr = "${nacos.config.addr}", namespace = "${nacos.config.namespace:}"))
@Data
public class NacosSiteBizConfig {

    private static final String NACOS_DATA_GROUP = "DEFAULT_GROUP";
    private static final int DUBBO_TIMEOUT = 10000;

    private SiteConfig siteConfig;


    @Value("${nacos.biz.site_config}")
    private String siteConfigDataId;


    @NacosInjected
    private ConfigService configService;

    /**
     * 将 NacosInjected 的 ConfigService 暴露为 Spring Bean
     * 这样其他组件就可以通过 @Autowired 或 @Resource 注入
     */
    @Bean
    public ConfigService configService() {
        return configService;
    }

    @NacosConfigListener(dataId = "${nacos.biz.site_config}")
    public void syncSiteConfig(String config) {
        setSiteConfig(config);
    }

    @PostConstruct
    private void init() throws Exception {
        // 初始化配置
        String siteConfigStr = configService.getConfig(siteConfigDataId, NACOS_DATA_GROUP, DUBBO_TIMEOUT);
        setSiteConfig(siteConfigStr);

    }

    private void setSiteConfig(String config) {
        try {
            siteConfig = JsonUtil.parseObject(config, SiteConfig.class);
            log.info("cur siteConfig , {}", config);
        } catch (Exception e) {
            log.error("setSiteConfig error, content = {}", config, e);
        }
    }

    public boolean isWhiteUser(String userId) {
        if (siteConfig.getWhileMidList().contains(userId)) {
            return true;
        }
        return false;
    }
}
