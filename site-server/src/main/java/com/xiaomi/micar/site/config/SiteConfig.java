package com.xiaomi.micar.site.config;

import com.xiaomi.micar.site.component.TabsComponent;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 站点配置
 */
@Data
public class SiteConfig {


    /**
     * 底部tab配置
     */
    private TabsComponent<Void> tabBar;

    /**
     * 按身份区分配置底TabBar
     */
    private Map<String, TabsComponent<Void>> tabBarMap;

    /**
     * 白名单（直接查询，不走缓存）
     */
    private List<String> whileMidList;

    /**
     * 灰度百分比，范围0-100
     * 0表示不开启灰度，100表示全量开启
     */
    private Integer grayScalePercent;


    /**
     * 旧版本的底部tab配置
     */
    private TabsComponent<Void> legacyTabBar;

    /**
     * 支持新版本底部标签栏的最低版本号
     * 默认为1.14.4
     */
    private String minVersionForNewTabBar = "1.14.4";


    /**
     * 页面顶部tab配置
     */
    private TabsComponent<Void> topTabBar;

}
