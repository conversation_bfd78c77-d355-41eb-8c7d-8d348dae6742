package com.xiaomi.micar.site.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 哈希函数工具类.
 *
 * <p>提供各种哈希算法实现。</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class HashFunctions {

    /**
     * MurmurHash3 固定种子值.
     */
    private static final int MURMUR_SEED = 0x9747b28c;

    /**
     * MurmurHash3 常量 C1.
     */
    private static final int MURMUR_C1 = 0xcc9e2d51;

    /**
     * MurmurHash3 常量 C2.
     */
    private static final int MURMUR_C2 = 0x1b873593;

    /**
     * MurmurHash3 常量 R1.
     */
    private static final int MURMUR_R1 = 15;

    /**
     * MurmurHash3 常量 R2.
     */
    private static final int MURMUR_R2 = 13;

    /**
     * MurmurHash3 常量 M.
     */
    private static final int MURMUR_M = 5;

    /**
     * MurmurHash3 常量 N.
     */
    private static final int MURMUR_N = 0xe6546b64;

    /**
     * 最终混合常量 1.
     */
    private static final int MIX_CONST_1 = 0x85ebca6b;

    /**
     * 最终混合常量 2.
     */
    private static final int MIX_CONST_2 = 0xc2b2ae35;

    /**
     * FNV-1a 质数.
     */
    private static final int FNV_PRIME = 0x01000193; // 16777619

    /**
     * FNV-1a 初始哈希值.
     */
    private static final int FNV_INIT = 0x811c9dc5; // 2166136261

    /**
     * 字节掩码.
     */
    private static final int BYTE_MASK = 0xFF;

    /**
     * 字节位移量 8.
     */
    private static final int SHIFT_8 = 8;

    /**
     * 字节位移量 16.
     */
    private static final int SHIFT_16 = 16;

    /**
     * 字节位移量 24.
     */
    private static final int SHIFT_24 = 24;

    /**
     * 哈希右移位数 16.
     */
    private static final int SHIFT_RIGHT_16 = 16;

    /**
     * 哈希右移位数 13.
     */
    private static final int SHIFT_RIGHT_13 = 13;

    /**
     * 整数 4.
     */
    private static final int INT_4 = 4;

    /**
     * 整数 3.
     */
    private static final int INT_3 = 3;

    /**
     * 整数 2.
     */
    private static final int INT_2 = 2;

    /**
     * 整数 1.
     */
    private static final int INT_1 = 1;
    /**
     * 私有构造函数，防止实例化.
     */
    private HashFunctions() {
        // 工具类不应被实例化
    }

    /**
     * 使用MurmurHash3算法计算字符串的哈希值.
     *
     * <p>这是默认的哈希方法，用于实验桶的分配。</p>
     *
     * @param key 要计算哈希的字符串
     * @return 哈希值（非负整数）
     */
    public static int hash(final String key) {
        return Math.abs(murmurHash3(key));
    }

    /**
     * MurmurHash3算法实现.
     *
     * <p>这是一种快速、高质量的非加密哈希函数，适用于哈希表和一致性哈希。</p>
     *
     * @param key 要计算哈希的字符串
     * @return 哈希值
     */
    public static int murmurHash3(final String key) {
        if (key == null) {
            return 0;
        }

        byte[] data = key.getBytes(StandardCharsets.UTF_8);
        int length = data.length;
        int seed = MURMUR_SEED;

        // 初始化哈希值
        int hash = seed;

        // 处理主体部分
        int i = 0;
        while (i + INT_4 <= length) {
            int k = (data[i] & BYTE_MASK)
                    | ((data[i + INT_1] & BYTE_MASK) << SHIFT_8)
                    | ((data[i + INT_2] & BYTE_MASK) << SHIFT_16)
                    | ((data[i + INT_3] & BYTE_MASK) << SHIFT_24);

            k *= MURMUR_C1;
            k = Integer.rotateLeft(k, MURMUR_R1);
            k *= MURMUR_C2;

            hash ^= k;
            hash = Integer.rotateLeft(hash, MURMUR_R2);
            hash = hash * MURMUR_M + MURMUR_N;

            i += INT_4;
        }

        // 处理剩余字节
        int k = 0;
        switch (length - i) {
            case INT_3:
                k ^= (data[i + INT_2] & BYTE_MASK) << SHIFT_16;
                // fall through
            case INT_2:
                k ^= (data[i + INT_1] & BYTE_MASK) << SHIFT_8;
                // fall through
            case INT_1:
                k ^= (data[i] & BYTE_MASK);
                k *= MURMUR_C1;
                k = Integer.rotateLeft(k, MURMUR_R1);
                k *= MURMUR_C2;
                hash ^= k;
                break;
            default:
                break;
        }

        // 最终混合
        hash ^= length;
        hash ^= (hash >>> SHIFT_RIGHT_16);
        hash *= MIX_CONST_1;
        hash ^= (hash >>> SHIFT_RIGHT_13);
        hash *= MIX_CONST_2;
        hash ^= (hash >>> SHIFT_RIGHT_16);

        return hash;
    }

    /**
     * 使用MD5算法计算字符串的哈希值.
     *
     * <p>适用于需要更高质量哈希的场景。</p>
     *
     * @param key 要计算哈希的字符串
     * @return 哈希值（非负整数）
     */
    public static int md5Hash(final String key) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(key.getBytes(StandardCharsets.UTF_8));

            // 取前4个字节转换为整数
            return Math.abs((digest[0] & BYTE_MASK)
                    | ((digest[INT_1] & BYTE_MASK) << SHIFT_8)
                    | ((digest[INT_2] & BYTE_MASK) << SHIFT_16)
                    | ((digest[INT_3] & BYTE_MASK) << SHIFT_24));
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5算法不可用，回退到MurmurHash3
            return Math.abs(murmurHash3(key));
        }
    }

    /**
     * 使用FNV-1a算法计算字符串的哈希值.
     *
     * <p>这是一种简单但高效的非加密哈希函数。</p>
     *
     * @param key 要计算哈希的字符串
     * @return 哈希值（非负整数）
     */
    public static int fnv1aHash(final String key) {
        if (key == null) {
            return 0;
        }

        int hash = FNV_INIT;

        for (byte b : key.getBytes(StandardCharsets.UTF_8)) {
            hash ^= (b & BYTE_MASK);
            hash *= FNV_PRIME;
        }

        return Math.abs(hash);
    }
}
