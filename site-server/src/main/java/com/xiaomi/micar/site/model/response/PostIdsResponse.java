package com.xiaomi.micar.site.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.xiaomi.micar.site.model.response.deserializer.PostIdsDataDeserializer;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 获取帖子ID列表响应
 *
 * <AUTHOR>
 * @since 2025/06/24
 */
@Data
public class PostIdsResponse {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 帖子ID列表
     * 当API返回空字符串时，自动转换为空列表
     */
    @JsonDeserialize(using = PostIdsDataDeserializer.class)
    private List<String> data = new ArrayList<>();
}
