package com.xiaomi.micar.site.service.config;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.enums.ComponentType;
import com.xiaomi.micar.site.model.PageConfigData;
import com.xiaomi.micar.site.utils.ExperimentBucketUtil;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 页面配置服务
 * 负责页面配置的解析和模型转换
 */
@Slf4j
public class PageConfigService {

    /**
     * 创建页面配置对象
     */
    public PageConfigModel createPageConfigObject(
            SitePageConfig pageConfig,
            List<SiteExpConfig> experiments,
            List<SiteExpBuckets> expBuckets) {

        if (pageConfig == null) {
            return null;
        }

        // 检查配置是否为空（下线的页面配置为null）
        if (pageConfig.getConfig() == null || pageConfig.getConfig().trim().isEmpty()) {
            log.info("页面配置为空，跳过处理: pageId={}", pageConfig.getPageId());
            return null;
        }

        PageConfigData pageConfigData = JsonUtil.parseObject(pageConfig.getConfig(), PageConfigData.class);
        if (pageConfigData == null) {
            log.error("解析页面配置失败: pageId={}, config={}", pageConfig.getPageId(), pageConfig.getConfig());
            return null;
        }

        PageConfigModel model = new PageConfigModel();
        model.setPageId(pageConfig.getPageId());
        model.setPageName(pageConfig.getPageName());
        model.setStrategyState(pageConfig.getStrategyState()); // 设置人群策略状态
        model.setVersion(generateVersionString(pageConfig, experiments, expBuckets));
        // 处理页面信息
        if (pageConfigData.getPageInfo() != null && pageConfigData.getPageInfo().getTitle() != null) {
            model.setTitle(pageConfigData.getPageInfo().getTitle());
        } else {
            model.setTitle(pageConfigData.getTitle());
        }

        // 处理组件、用户组和实验
        if (!CollectionUtils.isEmpty(pageConfigData.getComponents())) {
            model.setComponents(processComponents(pageConfigData));
            model.setGroupConfig(processUserGroups(pageConfigData));
            model.setExperiments(processExperiments(pageConfig, experiments, expBuckets));

            // 设置附加属性
            Map<String, Object> additionalProperties = new HashMap<>();
            additionalProperties.put("groupId", pageConfigData.getGroupId());
            model.setAdditionalProperties(additionalProperties);
        }

        return model;
    }

    /**
     * 生成版本号
     */
    private String generateVersionString(SitePageConfig pageConfig,
                                       List<SiteExpConfig> experiments,
                                       List<SiteExpBuckets> expBuckets) {
        int pageVersion = pageConfig.getVersion() != null ? pageConfig.getVersion() : 0;

        String pageId = pageConfig.getPageId();
        int expMaxVersion = experiments.stream()
                .filter(exp -> pageId.equals(exp.getPageId()))
                .mapToInt(exp -> exp.getVersion() != null ? exp.getVersion() : 0)
                .max().orElse(0);

        int bucketMaxVersion = expBuckets.stream()
                .filter(bucket -> pageId.equals(bucket.getPageId()))
                .mapToInt(bucket -> bucket.getVersion() != null ? bucket.getVersion() : 0)
                .max().orElse(0);

        return String.format("%d-%d-%d", pageVersion, expMaxVersion, bucketMaxVersion);
    }

    /**
     * 处理组件
     */
    private List<PageConfigModel.ComponentModel> processComponents(PageConfigData pageConfigData) {
        return pageConfigData.getComponents().stream()
                .map(this::createComponentModel)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 创建组件模型
     */
    private PageConfigModel.ComponentModel createComponentModel(PageConfigData.ComponentConfigData componentData) {
        if (componentData == null || StringUtils.isBlank(componentData.getId())) {
            return null;
        }

        Component component = createComponent(componentData);
        if (component == null) {
            return null;
        }

        PageConfigModel.ComponentModel model = new PageConfigModel.ComponentModel();
        model.setId(componentData.getId());
        model.setName(componentData.getName());
        model.setComponentType(componentData.getComponent());
        model.setDataProvider(componentData.getDataProvider());
        model.setDataProviderParams(componentData.getDataProviderParams());
        model.setTemplate(componentData.getTemplate());
        model.setTitleTag(componentData.getTitleTag());
        model.setComponentInfo(component);
        return model;
    }

    /**
     * 创建组件对象
     */
    private Component createComponent(PageConfigData.ComponentConfigData componentData) {
        if (StringUtils.isBlank(componentData.getComponent()) || StringUtils.isBlank(componentData.getConfig())) {
            return null;
        }

        ComponentType componentType = ComponentType.fromCode(componentData.getComponent());
        if (componentType == null) {
            return null;
        }

        try {
            Component component = JsonUtil.parseObject(componentData.getConfig(), componentType.getComponentClass());
            // 设置组件ID
            component.setId(componentData.getId());
            component.setSectionId(componentData.getId());
            component.setSectionName(componentData.getName());
            return component;
        } catch (Exception e) {
            log.error("组件配置解析失败: {}", componentData.getId(), e);
            return null;
        }
    }





    /**
     * 处理用户组
     */
    private List<PageConfigModel.GroupConfigModel> processUserGroups(PageConfigData pageConfigData) {
        if (CollectionUtils.isEmpty(pageConfigData.getGroupConfig())) {
            return Collections.emptyList();
        }

        return pageConfigData.getGroupConfig().stream()
                .filter(group -> StringUtils.isNotBlank(group.getStrategy()))
                .map(group -> {
                    PageConfigModel.GroupConfigModel model = new PageConfigModel.GroupConfigModel();
                    model.setStrategy(group.getStrategy());
                    model.setGroupName(group.getName());
                    model.setComponents(group.getList());
                    return model;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理实验
     */
    private List<PageConfigModel.ExperimentModel> processExperiments(
            SitePageConfig pageConfig,
            List<SiteExpConfig> experiments,
            List<SiteExpBuckets> expBuckets) {

        if (CollectionUtils.isEmpty(experiments)) {
            return Collections.emptyList();
        }

        String pageId = pageConfig.getPageId();
        Map<Long, List<SiteExpBuckets>> bucketMap = expBuckets.stream()
                .filter(bucket -> pageId.equals(bucket.getPageId()))
                .collect(Collectors.groupingBy(SiteExpBuckets::getExpId));

        return experiments.stream()
                .filter(exp -> pageId.equals(exp.getPageId()))
                .map(exp -> createExperimentModel(exp, bucketMap.get(exp.getId())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 创建实验模型
     */
    private PageConfigModel.ExperimentModel createExperimentModel(SiteExpConfig experiment,
                                                                 List<SiteExpBuckets> buckets) {
        if (experiment == null) {
            return null;
        }

        PageConfigModel.ExperimentModel model = new PageConfigModel.ExperimentModel();
        model.setExperimentId(String.valueOf(experiment.getId()));
        model.setName(experiment.getName());
        model.setStatus(experiment.getStatus());
        model.setStartTime(experiment.getStartTime());
        model.setEndTime(experiment.getEndTime());
        model.setConditionType(experiment.getConditionType());
        model.setConditionRule(experiment.getConditionRule());

        if (!CollectionUtils.isEmpty(buckets)) {
            List<PageConfigModel.BucketModel> bucketModels = buckets.stream()
                    .map(this::createBucketModel)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            ExperimentBucketUtil.generateBucketIdsForExperiment(bucketModels);
            model.setBuckets(bucketModels);
        }

        return model;
    }



    /**
     * 创建桶模型
     */
    private PageConfigModel.BucketModel createBucketModel(SiteExpBuckets bucket) {
        if (bucket == null) {
            return null;
        }

        PageConfigModel.BucketModel model = new PageConfigModel.BucketModel();
        model.setBucketId(bucket.getBucketId());
        model.setPageId(bucket.getPageId());
        model.setRatio(bucket.getRatio());

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> configMap = JsonUtil.parseObject(bucket.getConfig(), Map.class);
            if (configMap != null) {
                // 处理页面配置
                @SuppressWarnings("unchecked")
                Map<String, Object> pageConfig = (Map<String, Object>) configMap.get("page");
                if (pageConfig != null && !pageConfig.isEmpty()) {
                    model.setPageInfo(pageConfig);
                }

                // 处理组件配置
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> components = (List<Map<String, Object>>) configMap.get("components");
                if (!CollectionUtils.isEmpty(components)) {
                    List<Component> componentList = components.stream()
                            .map(ComponentType::createComponentFromData)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    model.setComponentInfoList(componentList);
                }
            }
        } catch (Exception e) {
            log.error("桶配置解析失败: {}", bucket.getBucketId(), e);
        }

        return model;
    }

}
