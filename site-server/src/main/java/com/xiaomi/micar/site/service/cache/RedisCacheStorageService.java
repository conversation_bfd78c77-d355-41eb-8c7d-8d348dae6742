package com.xiaomi.micar.site.service.cache;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.cache.CacheStorageService;
import com.xiaomi.micar.site.cache.util.CacheKeyUtil;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.component.ComponentConf;
import com.xiaomi.micar.site.model.PageResp;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.redis.SiteRedisKey;
import com.xiaomi.micar.site.utils.RedisUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis 缓存存储服务（REDIS_ONLY 策略）
 * 将页面快照以 Hash 形式写入 Redis
 */
@Slf4j
@Service("redisCacheStorageService")
public class RedisCacheStorageService implements CacheStorageService {

    @Resource
    private RedisUtil redisUtil;

    @Override
    public String savePageCache(String batchId, String pageId, Map<String, PageRespV2> responseCache, String configVersion) {
        if (responseCache == null || responseCache.isEmpty()) {
            log.warn("[RedisCacheStorageService] 响应缓存为空，跳过写入: pageId={}", pageId);
            return null;
        }
        try {
            int count = 0;
            for (Map.Entry<String, PageRespV2> entry : responseCache.entrySet()) {
                String combinationKey = entry.getKey();
                PageRespV2 pageResp = entry.getValue();

                // 写 V2
                String v2Field = CacheKeyUtil.addVersionPrefix(combinationKey, 2);
                String v2Json = JsonUtil.toJSONString(Result.success(pageResp));
                redisUtil.hashPut(SiteRedisKey.SITE_PAGE_CACHE_HASH, v2Field, v2Json, pageId);

                // 写 V1（兼容）
                PageResp v1 = convertV2ToV1(pageResp);
                String v1Field = CacheKeyUtil.addVersionPrefix(combinationKey, 1);
                String v1Json = JsonUtil.toJSONString(Result.success(v1));
                redisUtil.hashPut(SiteRedisKey.SITE_PAGE_CACHE_HASH, v1Field, v1Json, pageId);
                count += 2;
            }
            log.info("[RedisCacheStorageService] 页面缓存写入完成: pageId={}, fields={}, batchId={}", pageId, count, batchId);
            return batchId;
        } catch (Exception e) {
            log.error("[RedisCacheStorageService] 页面缓存写入异常: pageId={}", pageId, e);
            return null;
        }
    }

    @Override
    public Map<String, String> loadPageCache(String pageId, String batchId) {
        // REDIS_ONLY 不依赖批次落地到本地内存，此接口不实现
        return new HashMap<>();
    }

    /**
     * V2 -> V1 转换（与 MySQL 存储对齐）
     */
    private PageResp convertV2ToV1(PageRespV2 v2) {
        Map<String, Component> componentMap = new HashMap<>();
        java.util.ArrayList<String> moduleList = new java.util.ArrayList<>();

        List<ComponentConf> modules = v2.getModules();
        if (modules != null) {
            for (ComponentConf conf : modules) {
                componentMap.put(conf.getId(), conf.getConfig());
                moduleList.add(conf.getId());
            }
        }

        PageResp resp = new PageResp();
        resp.setPageId(v2.getPageId());
        resp.setExpId(v2.getExpId());
        if (v2.getPage() != null) {
            PageResp.PageInfo pi = new PageResp.PageInfo();
            pi.setTitle(v2.getPage().getTitle());
            pi.setGroupKey(v2.getPage().getGroupKey());
            resp.setPage(pi);
        }
        resp.setModules(componentMap);
        resp.setModuleList(moduleList);
        return resp;
    }
}

