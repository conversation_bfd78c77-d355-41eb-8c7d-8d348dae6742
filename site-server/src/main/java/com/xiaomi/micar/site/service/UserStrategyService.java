package com.xiaomi.micar.site.service;

import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.micar.site.up.model.UserRouteContext;
import com.xiaomi.micar.site.up.service.UpServiceManager;
import com.xiaomi.micar.site.cache.service.PageStrategyStateCache;
import com.xiaomi.micar.site.route.RouteContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户策略服务
 * 统一处理用户策略判断逻辑，避免代码重复
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserStrategyService {

    @Resource
    private UpServiceManager upServiceManager;

    @Resource
    private PageStrategyStateCache pageStrategyStateCache;

    /**
     * 根据页面策略状态确定用户策略
     * 如果页面关闭人群策略，直接返回默认策略；否则执行完整的用户策略判断
     *
     * @param pageId      页面ID
     * @param queryParams 查询参数
     * @param userId      用户ID
     * @return 用户策略
     */
    public String determineUserStrategyWithPageCheck(String pageId, Map<String, Object> queryParams, String userId) {
        // 检查页面是否开启人群策略
        boolean strategyEnabled = pageStrategyStateCache.isStrategyEnabled(pageId);

        if (strategyEnabled) {
            // 开启人群策略：执行用户策略判断
            String strategy = determineUserStrategy(queryParams, userId);
            log.debug("页面 {} 开启人群策略，用户 {} 使用策略: {}", pageId, userId, strategy);
            return strategy;
        } else {
            // 关闭人群策略：直接使用默认策略，跳过用户策略查询
            String defaultStrategy = UserStrategyEnum.ALL.getCode();
            log.debug("页面 {} 关闭人群策略，跳过用户策略查询，使用默认策略: {}", pageId, defaultStrategy);
            return defaultStrategy;
        }
    }

    /**
     * 确定用户策略
     * 根据用户的车辆绑定情况将用户分为三种类型：有车用户、新车主（绑定时间在三个月内）和无车用户
     *
     * @param queryParams 查询参数
     * @param userId      用户ID
     * @return 用户策略
     */
    public String determineUserStrategy(Map<String, Object> queryParams, String userId) {
        // 1. 如果查询参数中已经指定了策略，直接使用
        String strategyFromParams = getStrategyFromParams(queryParams);
        if (strategyFromParams != null) {
            return strategyFromParams;
        }

        // 2. 查询用户的车辆绑定情况
        try {
            if ("0".equals(userId)) {
                // 未登录用户直接返回
                return UserStrategyEnum.ALL.getCode();
            }

            return upServiceManager.getUpService().strategy(Long.parseLong(userId)).getCode();
        } catch (Exception e) {
            log.error("[UserStrategyService] Error determining user strategy for user {}: {}", userId, e.getMessage(), e);
            return UserStrategyEnum.ALL.getCode();
        }
    }

    /**
     * 从查询参数中获取策略
     *
     * @param queryParams 查询参数
     * @return 策略代码，如果不存在则返回null
     */
    private String getStrategyFromParams(Map<String, Object> queryParams) {
        try {
            if (queryParams != null && queryParams.containsKey("strategy") && queryParams.get("strategy") != null) {
                String strategy = queryParams.get("strategy").toString();
                log.debug("[UserStrategyService] Using strategy from query parameters: {}", strategy);
                return strategy;
            }
        } catch (Exception e) {
            log.error("[UserStrategyService] Error extracting strategy from query parameters: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 返回“多维度上下文”并结合页面开关进行适配：
     * - 若页面关闭人群策略，则将 UserStrategyEnum 强制为 [all]；
     * - 其他维度沿用 site-up 输出（保持职责边界）。
     */
    public RouteContext determineRouteContextWithPageCheck(String pageId, Map<String, Object> queryParams, String userId) {
        UserRouteContext upCtx;
        try {
            if ("0".equals(userId) || userId == null) {
                upCtx = UserRouteContext.of(Collections.singletonMap("UserStrategyEnum", Collections.singletonList(UserStrategyEnum.ALL.getCode())));
            } else {
                upCtx = upServiceManager.getUpService().routeContext(Long.parseLong(userId));
            }
        } catch (Exception e) {
            log.error("[UserStrategyService] Error fetching routeContext for user {}: {}", userId, e.getMessage(), e);
            upCtx = UserRouteContext.of(Collections.singletonMap("UserStrategyEnum", Collections.singletonList(UserStrategyEnum.ALL.getCode())));
        }

        Map<String, List<String>> dims = new HashMap<>();
        if (upCtx != null && upCtx.getDimValues() != null) {
            dims.putAll(upCtx.getDimValues());
        }

        boolean strategyEnabled = pageStrategyStateCache.isStrategyEnabled(pageId);
        if (!strategyEnabled) {
            dims.put("UserStrategyEnum", Collections.singletonList(UserStrategyEnum.ALL.getCode()));
        }
        return RouteContext.of(dims);
    }
}
