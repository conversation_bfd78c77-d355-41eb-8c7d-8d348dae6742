package com.xiaomi.micar.site.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 帖子详情响应
 */
@Data
public class PostDetailResponse {
    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private List<PostDetail> data;

    /**
     * 帖子详情
     */
    @Data
    @NoArgsConstructor
    public static class PostDetail {
        /**
         * 帖子ID
         */
        private String postId;
        
        /**
         * 帖子类型
         * {@link com.xiaomi.micar.site.model.CommunityPostType}
         */
        private Integer type;
        
        /**
         * 摘要列表
         */
        private List<SummaryItem> summary;
        
        /**
         * 显示摘要
         */
        private String displaySummary;
        
        /**
         * 标题
         */
        private String title;
        
        /**
         * 图片列表
         */
        private List<ImageInfo> imgList;
        
        /**
         * 视频列表
         */
        private List<VideoInfo> videoList;
        
        /**
         * 创建时间
         */
        private Long createTime;
        
        /**
         * 封面图片
         */
        private ImageInfo feedCover;

        /**
         * 浏览次数
         */
        private Integer viewCount;
    }

    /**
     * 摘要项
     */
    @Data
    @NoArgsConstructor
    public static class SummaryItem {
        /**
         * 类型
         */
        private String type;
        
        /**
         * 文本内容
         */
        private String txt;
    }

    /**
     * 图片信息
     */
    @Data
    @NoArgsConstructor
    public static class ImageInfo {
        /**
         * 图片URL
         */
        private String imageUrl;
        
        /**
         * 图片高度
         */
        private Integer height;
        
        /**
         * 图片宽度
         */
        private Integer width;
    }

    /**
     * 视频信息
     */
    @Data
    @NoArgsConstructor
    public static class VideoInfo {
        /**
         * 视频高度
         */
        private Integer height;
        
        /**
         * 视频宽度
         */
        private Integer width;
        
        /**
         * 封面图片URL
         */
        private String cover;
        
        /**
         * 视频时长（毫秒）
         */
        private Long duration;
    }
}
