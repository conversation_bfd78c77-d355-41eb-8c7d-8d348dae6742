package com.xiaomi.micar.site.service;

import com.google.common.collect.Lists;
import com.xiaomi.micar.site.model.PostDetailResponse;
import com.xiaomi.micar.site.redis.SiteRedisKey;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import com.xiaomi.micar.site.utils.RedisUtil;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 社区帖子缓存服务
 * 提供社区帖子详情的缓存管理功能
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@Service
public class CommunityPostCacheService {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    /**
     * 批量缓存帖子详情
     *
     * @param postDetails 帖子详情列表
     */
    public void cachePostDetails(List<PostDetailResponse.PostDetail> postDetails) {
        if (CollectionUtils.isEmpty(postDetails)) {
            log.warn("缓存帖子详情失败：帖子详情列表为空");
            return;
        }

        try {
            // 构建帖子ID到详情的映射
            Map<String, PostDetailResponse.PostDetail> postDetailMap = new HashMap<>(postDetails.size());
            for (PostDetailResponse.PostDetail detail : postDetails) {
                if (detail != null && StringUtils.isNotBlank(detail.getPostId())) {
                    postDetailMap.put(detail.getPostId(), detail);
                }
            }

            // 批量保存到Redis
            if (!postDetailMap.isEmpty()) {
                // 使用String类型，为每个帖子详情单独设置过期时间
                for (Map.Entry<String, PostDetailResponse.PostDetail> entry : postDetailMap.entrySet()) {
                    // 直接缓存 PostDetail 对象的 JSON 字符串，用于字符串拼接
                    String detailJson = JsonUtil.toJSONString(entry.getValue());
                    redisUtil.set(SiteRedisKey.COMMUNITY_POST_DETAIL_STRING, detailJson, entry.getKey());
                }
                log.info("成功缓存{}个帖子详情JSON字符串", postDetailMap.size());
            }
        } catch (Exception e) {
            log.error("缓存帖子详情异常", e);
        }
    }

    /**
     * 获取单个帖子详情
     *
     * @param postId 帖子ID
     * @return 帖子详情
     */
    public PostDetailResponse.PostDetail getPostDetail(String postId) {
        if (StringUtils.isBlank(postId)) {
            return null;
        }

        try {
            // 使用String类型获取帖子详情
            return redisUtil.getObject(
                    SiteRedisKey.COMMUNITY_POST_DETAIL_STRING,
                    PostDetailResponse.PostDetail.class,
                    postId);
        } catch (Exception e) {
            log.error("获取帖子详情缓存异常, postId={}", postId, e);
            return null;
        }
    }

    /**
     * 批量获取帖子详情
     *
     * @param postIds 帖子ID列表
     * @return 帖子详情列表
     */
    public List<PostDetailResponse.PostDetail> getPostDetails(List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }

        try {
            // 过滤有效的帖子ID
            List<String> validPostIds = postIds.stream()
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            if (validPostIds.isEmpty()) {
                return Collections.emptyList();
            }

            // 使用String类型获取帖子详情
            Map<String, PostDetailResponse.PostDetail> detailMap = redisUtil.multiGetObjects(
                    SiteRedisKey.COMMUNITY_POST_DETAIL_STRING,
                    validPostIds,
                    PostDetailResponse.PostDetail.class);

            // 记录缓存命中率
            if (!detailMap.isEmpty()) {
                log.info("从缓存中获取到{}个帖子详情，命中率: {}%",
                        detailMap.size(),
                        Math.round((float) detailMap.size() / validPostIds.size() * 100));
            }

            // 按照请求的顺序返回结果
            List<PostDetailResponse.PostDetail> result = new ArrayList<>(postIds.size());
            for (String postId : postIds) {
                PostDetailResponse.PostDetail detail = detailMap.get(postId);
                if (detail != null) {
                    result.add(detail);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("批量获取帖子详情缓存异常, postIds={}", postIds, e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量获取并缓存帖子详情
     * 从页面缓存中收集帖子ID，批量获取详情并缓存
     *
     * @param postIds 帖子ID集合
     * @return 获取到的帖子详情列表
     */
    public List<PostDetailResponse.PostDetail> fetchAndCachePostDetails(Set<String> postIds) {
        // 如果没有帖子ID，直接返回空列表
        if (CollectionUtils.isEmpty(postIds)) {
            log.info("没有需要获取的帖子ID");
            return Collections.emptyList();
        }

        log.info("从页面缓存中收集到{}个社区帖子ID，开始批量获取详情", postIds.size());

        // 将帖子ID列表分批处理，每批最多20个（API限制）
        List<String> postIdList = new ArrayList<>(postIds);
        final int BATCH_SIZE = 20;
        List<List<String>> batches = Lists.partition(postIdList, BATCH_SIZE);
        log.info("将{}个帖子ID分成{}批处理，每批最多{}个",
                postIdList.size(), batches.size(), BATCH_SIZE);

        // 收集所有获取到的帖子详情
        List<PostDetailResponse.PostDetail> allPostDetails = new ArrayList<>();

        // 批量处理帖子ID
        for (List<String> batch : batches) {
            try {
                // 将ID列表转换为逗号分隔的字符串
                String postIdsCsv = String.join(",", batch);
                log.info("正在获取第{}批帖子详情，IDs: {}",
                        batches.indexOf(batch) + 1, postIdsCsv);

                // 调用API获取帖子详情
                PostDetailResponse response = communityApiFeignClient.getPostDetailList(postIdsCsv);

                // 处理响应
                if (response != null && response.getCode() != null && response.getCode() == 200
                        && !CollectionUtils.isEmpty(response.getData())) {
                    allPostDetails.addAll(response.getData());
                    log.info("成功获取{}个帖子详情", response.getData().size());
                } else {
                    log.warn("获取帖子详情失败，响应: {}", response);
                }
            } catch (Exception e) {
                log.error("获取帖子详情异常", e);
            }
        }

        // 将获取到的帖子详情保存到Redis缓存
        if (!allPostDetails.isEmpty()) {
            log.info("成功获取{}个帖子详情，开始保存到Redis缓存", allPostDetails.size());
            cachePostDetails(allPostDetails);
        }

        return allPostDetails;
    }

    /**
     * 获取帖子详情Result字符串（用于前端API，字符串拼接优化版本）
     * 直接拼接JSON字符串，避免序列化/反序列化开销
     *
     * @param postIds 帖子ID列表
     * @return Result格式的JSON字符串
     */
    public String getPostDetailResultString(List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            // 返回空数据的Result
            return "{\"code\":0,\"message\":\"success\",\"success\":true,\"data\":[]}";
        }

        try {
            List<String> detailJsonList = new ArrayList<>();

            // 批量获取缓存的帖子详情JSON字符串
            for (String postId : postIds) {
                if (StringUtils.isNotBlank(postId)) {
                    String detailJson = redisUtil.get(SiteRedisKey.COMMUNITY_POST_DETAIL_STRING, postId);
                    if (StringUtils.isNotEmpty(detailJson)) {
                        detailJsonList.add(detailJson);
                    }
                }
            }

            int estimatedLength = 100 + detailJsonList.stream()
                .mapToInt(String::length)
                .sum();

            // 直接字符串拼接构建Result响应
            StringBuilder resultBuilder = new StringBuilder(estimatedLength);
            resultBuilder.append("{\"code\":0,\"message\":\"success\",\"success\":true,\"data\":[");

            // 拼接帖子详情JSON
            for (int i = 0; i < detailJsonList.size(); i++) {
                if (i > 0) {
                    resultBuilder.append(",");
                }
                resultBuilder.append(detailJsonList.get(i));
            }

            resultBuilder.append("]}");

            log.debug("成功拼接{}个帖子详情Result字符串", detailJsonList.size());
            return resultBuilder.toString();

        } catch (Exception e) {
            log.error("获取帖子详情Result字符串异常, postIds={}", postIds, e);
            // 异常时返回空数据的Result
            return "{\"code\":0,\"message\":\"success\",\"success\":true,\"data\":[]}";
        }
    }

    /**
     * 缓存ArticleElement JSON字符串列表用于分页查询
     * 使用Redis List结构，支持高效的LRANGE分页查询
     * 重新实现：避免RENAME操作，解决Redis集群CROSSSLOT问题
     *
     * @param cacheKey 分页缓存key
     * @param articleJsonList ArticleElement的JSON字符串列表
     */
    public void cacheArticleJsonListForPagination(String cacheKey, List<String> articleJsonList) {
        if (StringUtils.isBlank(cacheKey) || CollectionUtils.isEmpty(articleJsonList)) {
            log.warn("缓存分页文章JSON列表失败：缓存key为空或列表为空");
            return;
        }

        try {
            // 新的实现方式：直接删除旧数据，然后写入新数据
            // 避免使用RENAME操作，解决Redis集群CROSSSLOT问题

            // 1. 先删除旧的缓存数据（覆盖式缓存，清空历史数据）
            redisUtil.delete(SiteRedisKey.COMMUNITY_PAGINATION_LIST, cacheKey);

            // 2. 批量写入新数据到List（会自动设置过期时间）
            redisUtil.listRightPushAll(SiteRedisKey.COMMUNITY_PAGINATION_LIST, articleJsonList, cacheKey);

            log.info("成功缓存{}个文章JSON到Redis List用于分页查询，缓存key: {}", articleJsonList.size(), cacheKey);

        } catch (Exception e) {
            log.error("缓存分页文章JSON列表异常，缓存key: {}", cacheKey, e);
        }
    }

    /**
     * 缓存帖子ID列表用于分页查询
     * 使用Redis String结构，存储帖子ID的JSON数组
     *
     * @param cacheKey 分页缓存key
     * @param postIds 帖子ID列表
     */
    public void cachePostIdList(String cacheKey, List<String> postIds) {
        if (StringUtils.isBlank(cacheKey) || CollectionUtils.isEmpty(postIds)) {
            log.warn("缓存帖子ID列表失败：缓存key为空或ID列表为空");
            return;
        }

        try {
            // 将帖子ID列表序列化为JSON字符串
            String postIdsJson = JsonUtil.toJSONString(postIds);

            // 存储到Redis String结构
            redisUtil.set(SiteRedisKey.COMMUNITY_POST_IDS_LIST, postIdsJson, cacheKey);

            log.info("成功缓存{}个帖子ID到Redis String用于分页查询，缓存key: {}", postIds.size(), cacheKey);

        } catch (Exception e) {
            log.error("缓存帖子ID列表异常，缓存key: {}", cacheKey, e);
        }
    }

    /**
     * 获取缓存的帖子ID列表
     *
     * @param cacheKey 分页缓存key
     * @return 帖子ID列表
     */
    public List<String> getCachedPostIdList(String cacheKey) {
        if (StringUtils.isBlank(cacheKey)) {
            return Collections.emptyList();
        }

        try {
            String postIdsJson = redisUtil.get(SiteRedisKey.COMMUNITY_POST_IDS_LIST, cacheKey);

            if (StringUtils.isBlank(postIdsJson)) {
                return Collections.emptyList();
            }

            // 反序列化JSON数组为帖子ID列表
            @SuppressWarnings("unchecked")
            List<String> postIds = JsonUtil.parseObject(postIdsJson, List.class);

            if (!CollectionUtils.isEmpty(postIds)) {
                log.info("从缓存中获取到{}个帖子ID，缓存key: {}", postIds.size(), cacheKey);
                return postIds;
            }

            return Collections.emptyList();

        } catch (Exception e) {
            log.error("获取缓存帖子ID列表异常，缓存key: {}", cacheKey, e);
            return Collections.emptyList();
        }
    }

    /**
     * 检查帖子ID列表缓存是否存在
     *
     * @param cacheKey 缓存key
     * @return 是否存在缓存
     */
    public boolean hasPostIdListCache(String cacheKey) {
        if (StringUtils.isBlank(cacheKey)) {
            return false;
        }

        try {
            String postIdsJson = redisUtil.get(SiteRedisKey.COMMUNITY_POST_IDS_LIST, cacheKey);
            return StringUtils.isNotBlank(postIdsJson);
        } catch (Exception e) {
            log.error("检查帖子ID列表缓存异常，缓存key: {}", cacheKey, e);
            return false;
        }
    }

    /**
     * 获取分页缓存的文章JSON字符串列表
     * 直接从Redis List中获取，无需反序列化
     *
     * @param cacheKey 分页缓存key
     * @param offset 偏移量
     * @param limit 数量限制
     * @return 文章JSON字符串列表
     */
    public List<String> getPaginationArticleJsonList(String cacheKey, int offset, int limit) {
        if (StringUtils.isBlank(cacheKey)) {
            return Collections.emptyList();
        }

        try {
            // 使用LRANGE直接获取指定范围的JSON字符串，无需反序列化
            long start = offset;
            long end = offset + limit - 1;

            List<String> articleJsonList = redisUtil.listRange(SiteRedisKey.COMMUNITY_PAGINATION_LIST, start, end, cacheKey);

            if (!CollectionUtils.isEmpty(articleJsonList)) {
                log.info("从分页缓存中获取到{}个文章JSON，缓存key: {}, offset: {}, limit: {}",
                        articleJsonList.size(), cacheKey, offset, limit);
                return articleJsonList;
            }

            return Collections.emptyList();

        } catch (Exception e) {
            log.error("获取分页缓存文章JSON列表异常，缓存key: {}, offset: {}, limit: {}", cacheKey, offset, limit, e);
            return Collections.emptyList();
        }
    }

    /**
     * 检查分页缓存是否存在
     *
     * @param cacheKey 缓存key
     * @return 是否存在缓存
     */
    public boolean hasPaginationCache(String cacheKey) {
        if (StringUtils.isBlank(cacheKey)) {
            return false;
        }

        try {
            // 检查Redis List是否存在且不为空
            List<String> result = redisUtil.listRange(SiteRedisKey.COMMUNITY_PAGINATION_LIST, 0, 0, cacheKey);
            return !CollectionUtils.isEmpty(result);
        } catch (Exception e) {
            log.error("检查分页缓存异常，缓存key: {}", cacheKey, e);
            return false;
        }
    }

}
