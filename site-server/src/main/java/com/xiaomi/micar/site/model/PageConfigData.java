package com.xiaomi.micar.site.model;

import com.xiaomi.micar.site.component.Component;
import lombok.Data;

import java.util.List;

/**
 * 页面配置数据模型
 * 用于表示页面配置的JSON数据结构
 */
@Data
public class PageConfigData {
    /**
     * 页面标题
     */
    private String title;

    /**
     * 页面信息
     */
    private PageInfo pageInfo;

    /**
     * 页面组件列表
     */
    private List<ComponentConfigData> components;

    /**
     * 用户组ID
     */
    private String groupId;

    /**
     * 用户组配置
     */
    private List<GroupConfigData> groupConfig;

    /**
     * 组件数据模型
     */
    @Data
    public static class ComponentConfigData {
        /**
         * 组件ID
         */
        private String id;

        /**
         * 组件名称
         */
        private String name;

        /**
         * 组件类型
         */
        private String component;

        /**
         * 数据提供者
         */
        private String dataProvider;

        /**
         * 数据提供者参数，JSON字符串形式
         */
        private String dataProviderParams;

        /**
         * 组件配置，JSON字符串形式，可直接反序列化为Component的具体子类
         * {@link Component}
         */
        private String config;
        /**
         * 组件模板
         */
        private String template;
        /**
         * 标题标签
         */
        private String titleTag;
    }

    /**
     * 用户组配置数据模型
     */
    @Data
    public static class GroupConfigData {
        /**
         * 策略标识
         */
        private String strategy;

        /**
         * 用户组名称
         */
        private String name;

        /**
         * 组件列表
         */
        private List<String> list;
    }

    /**
     * 页面信息数据模型
     */
    @Data
    public static class PageInfo {
        /**
         * 页面标题
         */
        private String title;
    }

}
