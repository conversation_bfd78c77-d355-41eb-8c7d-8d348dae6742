package com.xiaomi.micar.site.service;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.component.BannerComponent;
import com.xiaomi.micar.site.component.element.MediaElement;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.service.provider.AbstractComponentDataProvider;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageRecordEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageRecordEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.DraftType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 内部数据provider
 */
@Component("recordDataProvider")
@Slf4j
public class RecordDataProvider extends AbstractComponentDataProvider {


    /**
     * 缓存键前缀
     */
    private static final String CACHE_KEY_PREFIX = "inner_record_list";

    @Resource
    private SitePageRecordEngine recordEngine;

    @Resource
    private SitePageConfigDraftEngine draftEngine;

    @Override
    protected boolean validateComponentType(com.xiaomi.micar.site.component.Component componentInfo) {
        return componentInfo instanceof BannerComponent;
    }

    @Override
    protected void doProcess(ResponseContext context, com.xiaomi.micar.site.component.Component componentInfo) {
        BannerComponent bannerComponent = (BannerComponent) componentInfo;

        Map<String, Object> queryParams = context.getQueryParams();

        log.info("处理RecordDataProvider组件， queryParams: {}, component={}", queryParams, componentInfo);

        if (queryParams == null) {
            throw new IllegalArgumentException("queryParams不能为空");
        }
        Assert.notNull(queryParams.get("pageId"), "pageId不能为空");
        Assert.notNull(queryParams.get("moduleId"), "moduleId不能为空");

        String pageId = queryParams.get("pageId").toString();
        String moduleId = queryParams.get("moduleId").toString();

        String draftId = queryParams.get("draftId") == null ? null : queryParams.get("draftId").toString();
        List<SitePageRecordEntity> result;

        if (StringUtils.isNotBlank(draftId)) {
            // 当draftId存在时，进行预览处理
            log.info("检测到draftId参数，进行预览处理: draftId={}, pageId={}, moduleId={}", draftId, pageId, moduleId);
            result = handlePreview(draftId, pageId, moduleId);
        } else {
            // 生成缓存键
            String cacheKey = generateCacheKey(CACHE_KEY_PREFIX, Optional.ofNullable(context.getQueryParams()).map(Object::hashCode).orElse(0));

            // 正常获取记录列表
            result = executeWithRetry(
                    cacheKey, () -> recordEngine.getExposeList(pageId, moduleId)
            );
        }

        try {
            if (result != null) {
                List<MediaElement> data = result.stream().map(record -> {
                    MediaElement mediaElement = JsonUtil.parseObject(record.getConfig(), MediaElement.class);
                    mediaElement.track(String.valueOf(record.getId()), record.getName());
                    mediaElement.setId(String.valueOf(record.getId()));
                    mediaElement.setName(record.getName());
                    return mediaElement;
                }).collect(Collectors.toList());
                bannerComponent.setData(data);
            }

        } catch (Exception e) {
            handleProcessingError(componentInfo, e);
        }
    }

    @Override
    protected void handleProcessingError(com.xiaomi.micar.site.component.Component componentInfo, Exception e) {
        log.error("Record组件处理失败，已设置空数据: {}", e.getMessage());
    }

    /**
     * 处理预览功能，将草稿表中的内容与recordEngine.getExposeList查询出来的内容进行合并
     *
     * @param draftIdStr 草稿ID
     * @param pageId     页面ID
     * @param moduleId   模块ID
     * @return 合并后的记录列表
     */
    private List<SitePageRecordEntity> handlePreview(String draftIdStr, String pageId, String moduleId) {
        try {
            // 将字符串转换为Long类型的draftId
            Long draftId = Long.parseLong(draftIdStr);

            // 获取草稿信息
            SitePageConfigDraftEntity draft = draftEngine.getById(draftId);
            if (draft == null) {
                log.warn("未找到对应的草稿记录, draftId={}", draftId);
                // 如果找不到草稿，则返回正常的记录列表
                return recordEngine.getExposeList(pageId, moduleId);
            }

            log.info("找到草稿记录: draftId={}, pageId={}, moduleId={}, type={}, status={}",
                    draft.getId(), draft.getPageId(), draft.getModuleId(), draft.getType(), draft.getStatus());

            // 获取当前记录列表
            List<SitePageRecordEntity> currentRecords = recordEngine.getAllExposed(pageId, moduleId);
            log.info("当前记录列表大小: {}", currentRecords.size());

            // 根据草稿类型和状态进行不同的处理
            if (draft.getType() != DraftType.RECORD_CONTENT.getCode()) {
                log.warn("草稿不是记录类型, draftId={}, type={}", draftId, draft.getType());
                throw new RuntimeException("invalid drfat type : " + draft.getType());
            }

            // 判断是否为下线审核
            boolean isOfflineAudit = draft.getStatus() == ConfigStatus.OFFLINE_AUDIT.getCode(); // ConfigStatus.OFFLINE_AUDIT.getCode()

            // 如果是下线审核，且releaseId存在，则从当前记录列表中移除该记录
            if (isOfflineAudit) {
                if (draft.getReleaseId() != null && draft.getReleaseId() > 0) {
                    log.info("下线审核预览，将从当前记录列表中移除记录: releaseId={}", draft.getReleaseId());
                    return currentRecords.stream()
                            .filter(record -> !record.getId().equals(draft.getReleaseId()))
                            .collect(Collectors.groupingBy(SitePageRecordEntity::getPriority)) // 按优先级分组
                            .values()
                            .stream()
                            .map(records -> records.stream().max(Comparator.comparing(SitePageRecordEntity::getUpdateTime)).get()) // 每个优先级组取第一个（即最近更新的）
                            .sorted(Comparator.comparing(SitePageRecordEntity::getPriority)) // 重新按优先级排序
                            .collect(Collectors.toList());
                }
                throw new IllegalArgumentException("isOffline but releaseId is null");
            }

            // 如果是新增或修改
            // 创建一个新的记录实体，用于预览
            SitePageRecordEntity previewRecord = new SitePageRecordEntity();

            // 如果是修改现有记录
            if (draft.getReleaseId() != null && draft.getReleaseId() > 0) {
                // 查找现有记录
                SitePageRecordEntity existingRecord = currentRecords.stream()
                        .filter(record -> record.getId().equals(draft.getReleaseId()))
                        .findFirst()
                        .orElse(null);

                if (existingRecord != null) {
                    log.info("修改现有记录预览, releaseId={}", draft.getReleaseId());
                    // 从当前记录列表中移除现有记录
                    currentRecords = currentRecords.stream()
                            .filter(record -> !record.getId().equals(draft.getReleaseId()))
                            .collect(Collectors.toList());
                    // 复制现有记录的属性
                    previewRecord.setId(existingRecord.getId());
                    previewRecord.setName(draft.getName() != null ? draft.getName() : existingRecord.getName());
                    previewRecord.setPageId(draft.getPageId());
                    previewRecord.setModuleId(draft.getModuleId() != null ? draft.getModuleId().toString() : null);
                    previewRecord.setRecordGroup(draft.getGroupKey());
                    previewRecord.setConfig(draft.getConfig());
                    previewRecord.setPriority(draft.getPriority() != null ? draft.getPriority() : existingRecord.getPriority());
                    previewRecord.setExposeFrom(draft.getExposeFrom() != null ? draft.getExposeFrom() : existingRecord.getExposeFrom());
                    previewRecord.setExposeTo(draft.getExposeTo() != null ? draft.getExposeTo() : existingRecord.getExposeTo());
                    previewRecord.setRemark(draft.getRemark());
                    previewRecord.setOperatorId(draft.getOperatorId());
                    previewRecord.setOperatorName(draft.getOperatorName());
                    previewRecord.setCreateTime(existingRecord.getCreateTime());
                    previewRecord.setUpdateTime(new Date());
                } else {
                    log.warn("未找到要修改的现有记录, releaseId={}", draft.getReleaseId());
                    throw new IllegalArgumentException("releaseId not found");
                }
            } else {
                // 新增记录
                log.info("新增记录预览");
                previewRecord.setId(draft.getId()); // 使用草稿ID作为临时ID
                previewRecord.setName(draft.getName());
                previewRecord.setPageId(draft.getPageId());
                previewRecord.setModuleId(draft.getModuleId() != null ? draft.getModuleId().toString() : null);
                previewRecord.setRecordGroup(draft.getGroupKey());
                previewRecord.setConfig(draft.getConfig());
                previewRecord.setPriority(draft.getPriority());
                previewRecord.setExposeFrom(draft.getExposeFrom());
                previewRecord.setExposeTo(draft.getExposeTo());
                previewRecord.setRemark(draft.getRemark());
                previewRecord.setOperatorId(draft.getOperatorId());
                previewRecord.setOperatorName(draft.getOperatorName());
                previewRecord.setCreateTime(new Date());
                previewRecord.setUpdateTime(new Date());
            }

            // 将预览记录添加到当前记录列表中
            currentRecords.add(previewRecord);

            // 重新分组排序
            currentRecords = currentRecords.stream()
                    .collect(Collectors.groupingBy(SitePageRecordEntity::getPriority)) // 按优先级分组
                    .values()
                    .stream()
                    .map(records -> records.stream().max(Comparator.comparing(SitePageRecordEntity::getUpdateTime)).get()) // 每个优先级组取第一个（即最近更新的）
                    .sorted(Comparator.comparing(SitePageRecordEntity::getPriority)) // 重新按优先级排序
                    .collect(Collectors.toList());

            log.info("预览处理完成，合并后的记录列表大小: {}", currentRecords.size());
            return currentRecords;

        } catch (NumberFormatException e) {
            log.error("解析草稿ID失败: {}, {}", draftIdStr, e.getMessage());
            // 如果解析草稿ID失败，则返回正常的记录列表
            return recordEngine.getExposeList(pageId, moduleId);
        } catch (Exception e) {
            log.error("处理预览时发生异常: {}", e.getMessage(), e);
            // 如果处理预览时发生异常，则返回正常的记录列表
            return recordEngine.getExposeList(pageId, moduleId);
        }
    }

}
