package com.xiaomi.micar.site.api.fe;

import com.xiaomi.micar.site.component.TabsComponent;
import com.xiaomi.micar.site.utils.HashFunctions;
import com.mi.car.iccc.user.permit.common.UserPermitRoleTagEnum;
import com.xiaomi.micar.site.api.model.GetSiteConfResp;
import com.xiaomi.micar.site.config.NacosSiteBizConfig;
import com.xiaomi.micar.site.metrics.aop.SiteMetrics;
import com.xiaomi.micar.site.up.service.UserPermitBiz;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.xiaomi.micar.site.api.fe.GatewayConstants.PARAM_MOBILE_ID;
import static com.xiaomi.micar.site.api.fe.GatewayConstants.PARAM_UID;
import static com.xiaomi.micar.site.api.fe.GatewayConstants.PARAM_VERSION;

/**
 * 站点服务接口
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Slf4j
@ApiModule(value = "站点服务接口", apiInterface = FeSiteService.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class FeSiteServiceImpl implements FeSiteService {

    @Resource
    private NacosSiteBizConfig nacosSiteBizConfig;

    @Resource
    private UserPermitBiz userPermitBiz;

    @Override
    @SiteMetrics(type = SiteMetrics.TYPE_SITE)
    public Result<GetSiteConfResp> getSiteConfig(Map<String, String> request) {

        String uid = RpcContext.getContext().getAttachment(PARAM_UID);
        String mobileId = RpcContext.getContext().getAttachment(PARAM_MOBILE_ID);
        String version = RpcContext.getContext().getAttachment(PARAM_VERSION);

        // 记录请求信息
        log.info("getSiteConfig request, uid={}, mobileId={}, version={}", uid, mobileId, version);

        GetSiteConfResp resp = new GetSiteConfResp();

        // 如果启用了版本控制并且客户端版本低于指定版本，直接使用旧版本的底部标签栏
        String minVersionForNewTabBar = nacosSiteBizConfig.getSiteConfig().getMinVersionForNewTabBar();
        boolean versionSupported = isVersionSupported(version, minVersionForNewTabBar);
        if (!versionSupported) {
            log.info("客户端版本过低，使用旧版本底部标签栏，当前版本: {}, 最低要求版本: {}", version, minVersionForNewTabBar);
            resp.setTabBar(nacosSiteBizConfig.getSiteConfig().getLegacyTabBar());
            return Result.success(resp);
        }

        // 检查用户是否在白名单中
        boolean isInWhitelist = checkIfUserInWhitelist(uid, mobileId);

        // 判断用户是否应该使用灰度配置
        boolean shouldUseGrayScale = determineIfUserShouldUseGrayScale(isInWhitelist, mobileId);

        // 根据灰度判断结果设置响应
        setResponseTabBar(uid, resp, shouldUseGrayScale);

        return Result.success(resp);
    }


    @Override
    public Result<TabsComponent<Void>> topTabBar(Map<String, String> request) {
        return Result.success(nacosSiteBizConfig.getSiteConfig().getTopTabBar());
    }


    /**
     * 检查用户是否在白名单中
     *
     * @param uid      用户ID
     * @param mobileId 设备ID
     * @return 是否在白名单中
     */
    private boolean checkIfUserInWhitelist(String uid, String mobileId) {
        if (StringUtils.isEmpty(uid) && StringUtils.isEmpty(mobileId)) {
            return false;
        }
        // 获取白名单列表
        List<String> whitelistIds = nacosSiteBizConfig.getSiteConfig().getWhileMidList();
        if (whitelistIds == null || whitelistIds.isEmpty()) {
            return false;
        }

        // 检查UID是否在白名单中
        if (StringUtils.isNotEmpty(uid) && whitelistIds.contains(uid)) {
            log.info("白名单灰度: 用户UID在白名单中, uid={}", uid);
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否应该使用灰度配置
     *
     * @param isInWhitelist 是否在白名单中
     * @param mobileId      设备ID
     * @return 是否应该使用灰度配置
     */
    private boolean determineIfUserShouldUseGrayScale(boolean isInWhitelist, String mobileId) {

        // 如果用户在白名单中，直接返回true
        if (isInWhitelist) {
            return true;
        }

        // 获取灰度配置百分比，默认为0（不开启灰度）
        Integer grayScalePercent = nacosSiteBizConfig.getSiteConfig().getGrayScalePercent();
        if (grayScalePercent == null) {
            grayScalePercent = 0;
        }

        // 如果灰度百分比为100%，直接返回true
        if (grayScalePercent == 100) {
            return true;
        }

        // 如果灰度百分比大于0且小于100，且mobileId不为空，则进行百分比灰度判断
        if (grayScalePercent > 0 && grayScalePercent < 100 && StringUtils.isNotEmpty(mobileId)) {
            int hash = HashFunctions.murmurHash3(mobileId);
            int hashPercent = Math.abs(hash) % 100;
            boolean isInGrayScale = hashPercent < grayScalePercent;
            log.info("百分比灰度判断: mobileId={}, hashPercent={}, grayScalePercent={}, isInGrayScale={}",
                    mobileId, hashPercent, grayScalePercent, isInGrayScale);
            return isInGrayScale;
        }

        return false;
    }

    /**
     * 检查客户端版本是否支持新版本的底部标签栏
     *
     * @param version    客户端版本号
     * @param minVersion 最低要求版本号
     * @return 是否支持
     */
    private boolean isVersionSupported(String version, String minVersion) {
        if (StringUtils.isEmpty(version)) {
            // 如果版本号为空，默认不支持
            return false;
        }

        // 如果最低版本号为空，默认支持
        if (StringUtils.isEmpty(minVersion)) {
            return true;
        }

        try {
            // 将版本号分解为主版本、次版本和补丁版本
            String[] versionParts = version.split("\\.");
            String[] minVersionParts = minVersion.split("\\.");

            // 比较主版本
            int majorVersion = Integer.parseInt(versionParts[0]);
            int minMajorVersion = Integer.parseInt(minVersionParts[0]);
            if (majorVersion > minMajorVersion) {
                return true;
            } else if (majorVersion < minMajorVersion) {
                return false;
            }

            // 主版本相同，比较次版本
            if (versionParts.length > 1 && minVersionParts.length > 1) {
                int minorVersion = Integer.parseInt(versionParts[1]);
                int minMinorVersion = Integer.parseInt(minVersionParts[1]);
                if (minorVersion > minMinorVersion) {
                    return true;
                } else if (minorVersion < minMinorVersion) {
                    return false;
                }
            }

            // 主版本和次版本相同，比较补丁版本
            if (versionParts.length > 2 && minVersionParts.length > 2) {
                int patchVersion = Integer.parseInt(versionParts[2]);
                int minPatchVersion = Integer.parseInt(minVersionParts[2]);
                return patchVersion >= minPatchVersion;
            }

            // 如果客户端版本号格式不完整，但前面的版本号相同，则认为支持
            return true;
        } catch (Exception e) {
            log.warn("版本号比较异常，当前版本: {}, 最低要求版本: {}, 异常: {}",
                    version, minVersion, e.getMessage());
            // 如果解析失败，默认不支持
            return false;
        }
    }

    /**
     * 根据灰度判断结果设置响应
     *
     * @param resp               响应对象
     * @param shouldUseGrayScale 是否应该使用灰度配置
     */
    private void setResponseTabBar(String uid, GetSiteConfResp resp, boolean shouldUseGrayScale) {
        if (shouldUseGrayScale) {
            log.info("用户在灰度范围内，返回灰度配置");
            // 获取用户身份角色
            UserPermitRoleTagEnum roleTagEnum = userPermitBiz.getUserRoleTag(Long.valueOf(uid));
            // 优先取角色对应配置，兜底走默认配置
            TabsComponent<Void> tabBarConfig = Optional.ofNullable(nacosSiteBizConfig.getSiteConfig().getTabBarMap())
                    .map(map -> roleTagEnum == null ? null : map.get(roleTagEnum.getValue()))
                    .orElse(nacosSiteBizConfig.getSiteConfig().getTabBar());
            resp.setTabBar(tabBarConfig);
        } else {
            resp.setTabBar(nacosSiteBizConfig.getSiteConfig().getLegacyTabBar());
        }
    }
}
