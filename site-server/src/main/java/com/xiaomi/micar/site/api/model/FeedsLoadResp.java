package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.component.element.ArticleElement;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Feed流加载响应
 * 对应接口：/mtop/carsite/community/feeds/load
 *
 * <AUTHOR>
 * @since 2025/06/24
 */
@Data
public class FeedsLoadResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前偏移量
     */
    private Integer offset;

    /**
     * 是否还有更多数据
     */
    private Boolean hasMore;

    /**
     * 记录列表
     */
    private List<ArticleElement> records;
}
