package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.component.TabFeedsComponent;
import com.xiaomi.micar.site.component.TabsComponent;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.config.SiteComponentProperties;
import com.xiaomi.micar.site.service.remote.RemoteCommunityDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 多标签精选Feed数据提供者
 */
@Slf4j
@org.springframework.stereotype.Component
public class TabFeedsDataProvider extends AbstractComponentDataProvider {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_LIMIT = 5;

    /**
     * 最小分页大小
     */
    private static final int MIN_LIMIT = 1;

    /**
     * 最大分页大小
     */
    private static final int MAX_LIMIT = 20;

    /**
     * 默认标签ID
     */
    private static final String DEFAULT_TAB_ID = "all";

    @Resource
    private SiteComponentProperties properties;

    @Resource
    private RemoteCommunityDataSource communityDataSource;


    @Override
    protected boolean validateComponentType(Component componentInfo) {
        return componentInfo instanceof TabFeedsComponent;
    }

    @Override
    protected void doProcess(ResponseContext context, Component componentInfo) {
        TabFeedsComponent tabFeeds = (TabFeedsComponent) componentInfo;

        // 从配置获取是否启用
        SiteComponentProperties.ComponentApiConfig config = properties.getApi().getTabFeeds();
        if (!config.isEnabled()) {
            log.warn("TabFeeds component is disabled via configuration");
            tabFeeds.setData(createEmptyFeedData(0));
            return;
        }

        // 获取当前选中的标签
        TabFeedsComponent.TabItem<Void> selectedTab = getSelectedTab(tabFeeds);
        String tabId = Optional.ofNullable(selectedTab)
                .map(TabFeedsComponent.TabItem::getTabId)
                .orElse("all");

        // 获取请求参数
        Map<String, Object> queryParams = Optional.ofNullable(context.getQueryParams())
                .map(HashMap::new)
                .orElseGet(HashMap::new);

        // 获取分页参数
        Integer after = getIntParam(queryParams, "after", 0);
        Integer limit = getIntParam(queryParams, "limit", DEFAULT_LIMIT);

        // 获取用户ID
        String userId = context.getUserId();

        // 从数据源获取Feed数据
        TabFeedsComponent.FeedData feedData = fetchFeedData(userId, tabId, after, limit, queryParams);

        // 设置组件数据
        tabFeeds.setData(feedData);
    }

    /**
     * 获取当前选中的标签
     *
     * @param tabFeeds 标签Feed组件
     * @return 选中的标签项，如果没有则返回null
     */
    private TabsComponent.TabItem<Void> getSelectedTab(TabFeedsComponent tabFeeds) {
        List<TabsComponent.TabItem<Void>> items = tabFeeds.getItems();
        if (items == null || items.isEmpty()) {
            return null;
        }

        // 查找选中的标签
        return items.stream()
                .filter(item -> Boolean.TRUE.equals(item.getSelected()))
                .findFirst()
                .orElse(items.get(0)); // 默认使用第一个标签
    }

    /**
     * 从API获取Feed数据
     *
     * @param userId      用户ID
     * @param tabId       标签ID
     * @param after       分页起始位置
     * @param limit       数量限制
     * @param queryParams 额外查询参数
     * @return Feed数据
     */
    private TabFeedsComponent.FeedData fetchFeedData(String userId, String tabId, Integer after, Integer limit, Map<String, Object> queryParams) {
        try {
            // 处理tabId
            String configTabId = properties.getApi().getTabFeeds().getTabId();
            String finalTabId = StringUtils.hasText(configTabId) ? configTabId
                    : StringUtils.hasText(tabId) ? tabId : DEFAULT_TAB_ID;

            // 处理limit
            Integer configLimit = properties.getApi().getTabFeeds().getLimit();
            Integer finalLimit = Optional.ofNullable(configLimit)
                    .orElse(Optional.ofNullable(limit)
                            .orElse(DEFAULT_LIMIT));

            // 验证limit范围
            if (finalLimit < MIN_LIMIT || finalLimit > MAX_LIMIT) {
                log.warn("Invalid limit: {}. Must be between {} and {}, using default limit {} ", finalLimit, MIN_LIMIT, MAX_LIMIT, DEFAULT_LIMIT);
                finalLimit = DEFAULT_LIMIT;
            }

            // 准备请求参数
            Map<String, Object> params = new HashMap<>(queryParams);
            params.put("after", Optional.ofNullable(after).orElse(0));
            params.put("limit", finalLimit);
            params.put("tabId", finalTabId);

            // 创建缓存键
            String cacheKey = generateCacheKey("tab_feeds", userId, finalTabId, after, finalLimit);

            // 使用重试机制获取TAB信息流数据
            try {
                TabFeedsComponent.FeedData feedData = executeWithRetry(cacheKey,
                        () -> communityDataSource.fetchTabFeeds(userId, params));

                log.debug("处理TAB信息流成功，总记录数: {}",
                        feedData.getRecords() != null ? feedData.getRecords().size() : 0);

                return feedData;
            } catch (Exception e) {
                log.error("获取TAB信息流数据失败: {}", e.getMessage(), e);
                // 返回空数据
                return createEmptyFeedData(Optional.ofNullable(after).orElse(0));
            }
        } catch (Exception e) {
            log.error("Error fetching tab feeds data: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 创建空的FeedData对象
     *
     * @param after 分页起始位置
     * @return 空的FeedData对象
     */
    private TabFeedsComponent.FeedData createEmptyFeedData(Integer after) {
        TabFeedsComponent.FeedData emptyData = new TabFeedsComponent.FeedData();
        emptyData.setOffset(Optional.ofNullable(after).orElse(0));
        emptyData.setRecords(Collections.emptyList());
        return emptyData;
    }

    @Override
    protected void handleProcessingError(Component componentInfo, Exception e) {
        if (componentInfo instanceof TabFeedsComponent) {
            TabFeedsComponent tabFeedsComponent = (TabFeedsComponent) componentInfo;
            // 设置空数据，确保前端不会因为数据缺失而崩溃
            tabFeedsComponent.setData(createEmptyFeedData(0));
            log.error("TabFeeds组件处理失败，已设置空数据: {}", e.getMessage());
        }
    }

}

