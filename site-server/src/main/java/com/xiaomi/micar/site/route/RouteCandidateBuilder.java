package com.xiaomi.micar.site.route;

import com.xiaomi.micar.site.route.config.DegradeConfigProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import java.util.HashMap;

/**
 * 候选生成器：同层替代 → 降维 → all。
 * 未配置时退回 RouteCodeUtil.degradeChain。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RouteCandidateBuilder {

    @Resource
    private DegradeConfigProperties cfg;

    /**
     * 新版：按照“主维度 → 次维度”的嵌套退化顺序生成候选。
     * 含义：
     * - 以 dimensionOrder[0] 作为“主维度”（例如 UserStrategyEnum）。
     * - 按主维度的取值顺序（由 ctx 值结合 cfg.order 决定）遍历：
     * - 读取 relations 中该主维度取值允许参与的“下层维度”集合；
     * - 对这些下层维度的可用值做组合（按各自 order 排序），产出“同层候选”；
     * - 然后退化到“仅主维度值”。
     * - 末尾确保追加 all。
     */
    public List<String> build(RouteContext ctx) {
        try {
            List<String> dimOrder = cfg.getDimensionOrder();
            if (dimOrder == null || dimOrder.isEmpty()) {
                return Collections.singletonList("all");
            }

            String primaryDim = dimOrder.get(0);
            // 预取各维度的“有序值列表”（仅限 ctx 提供的值）
            List<String> primaryVals = orderedValuesOf(ctx, primaryDim);
            if (primaryVals.isEmpty()) {
                // 若主维度缺失，直接兜底（按需求也可改为尝试其它维度）
                return Collections.singletonList("all");
            }

            List<String> subDims = dimOrder.size() > 1
                    ? dimOrder.subList(1, dimOrder.size())
                    : Collections.emptyList();

            LinkedHashSet<String> out = new LinkedHashSet<>();
            int maxCand = Math.max(1, cfg.getFallback().getMaxCandidates());

            for (String pv : primaryVals) {
                if (out.size() >= maxCand) {
                    break;
                }

                // 1) 计算该主值下“允许参与”的下层维度（按 dimOrder 顺序）
                List<String> allowedSubDims = filterAllowedSubDims(primaryDim, pv, subDims);

                // 2) 准备各下层维度的可用值（过滤空）
                List<List<String>> subDimVals = new ArrayList<>();
                for (String d : allowedSubDims) {
                    List<String> vals = orderedValuesOf(ctx, d);
                    if (vals != null && !vals.isEmpty()) {
                        subDimVals.add(vals);
                    }
                }

                // 3) 同层组合（主维度固定为 pv）
                if (!subDimVals.isEmpty()) {
                    cartesianAppendWithPrefix(pv, subDimVals, out, maxCand);
                }

                if (out.size() >= maxCand) {
                    break;
                }
                // 4) 降维到仅主维度
                out.add(pv);
            }

            // 确保 all 在末尾
            if (out.contains("all")) {
                out.remove("all");
            }
            out.add("all");
            return new ArrayList<>(out);
        } catch (Exception e) {
            log.warn("[RouteCandidateBuilder] build by context error: {}", e.getMessage());
            return Collections.singletonList("all");
        }
    }


    /**
     * 简单本地测试与基准统计：
     * 1) 示例：用户 A
     * 2) 压测：最复杂场景（多维多值、全部参与组合），统计耗时分布
     */
    public static void main(String[] args) {
        // 示例：用户 A（验证序列）
        exampleUserA();

        // 压测：最复杂场景
        worstCaseBenchmark();
    }

    private static void exampleUserA() {
        DegradeConfigProperties cfg = new DegradeConfigProperties();
        cfg.setDimensionOrder(Arrays.asList("UserStrategyEnum", "CarModelEnum"));

        Map<String, DegradeConfigProperties.DimOrder> order = new HashMap<>();
        DegradeConfigProperties.DimOrder userOrder = new DegradeConfigProperties.DimOrder();
        userOrder.setOrder(Arrays.asList("car_owner", "car_share", "all"));
        order.put("UserStrategyEnum", userOrder);
        DegradeConfigProperties.DimOrder carOrder = new DegradeConfigProperties.DimOrder();
        carOrder.setOrder(Arrays.asList("yu7", "su7Ultra", "su7"));
        order.put("CarModelEnum", carOrder);
        cfg.setOrder(order);

        Map<String, Map<String, DegradeConfigProperties.ValueRelation>> relations = new HashMap<>();
        Map<String, DegradeConfigProperties.ValueRelation> userRelations = new HashMap<>();
        DegradeConfigProperties.ValueRelation vrOwner = new DegradeConfigProperties.ValueRelation();
        vrOwner.setEnableDims(Arrays.asList("CarModelEnum"));
        userRelations.put("car_owner", vrOwner);
        DegradeConfigProperties.ValueRelation vrShare = new DegradeConfigProperties.ValueRelation();
        vrShare.setEnableDims(Arrays.asList("CarModelEnum"));
        userRelations.put("car_share", vrShare);
        DegradeConfigProperties.ValueRelation vrAll = new DegradeConfigProperties.ValueRelation();
        vrAll.setEnableDims(Collections.emptyList());
        userRelations.put("all", vrAll);
        relations.put("UserStrategyEnum", userRelations);
        cfg.setRelations(relations);

        DegradeConfigProperties.Fallback fb = new DegradeConfigProperties.Fallback();
        fb.setBeam(200);
        fb.setMaxCandidates(10);
        cfg.setFallback(fb);

        Map<String, List<String>> dimValues = new HashMap<>();
        dimValues.put("UserStrategyEnum", Arrays.asList("car_owner", "car_share", "all"));
        dimValues.put("CarModelEnum", Arrays.asList("yu7", "su7"));
        RouteContext ctx = RouteContext.of(dimValues);

        RouteCandidateBuilder builder = new RouteCandidateBuilder();
        builder.cfg = cfg;
        List<String> cands = builder.build(ctx);
        System.out.println("Example A candidates: " + cands);
    }

    private static void worstCaseBenchmark() {
        // 构造“复杂配置”：主维 + 3 个下层维度，全部参与
        DegradeConfigProperties cfg = new DegradeConfigProperties();
        cfg.setDimensionOrder(Arrays.asList("UserStrategyEnum", "CarModelEnum", "RegionEnum", "ChannelEnum"));

        Map<String, DegradeConfigProperties.DimOrder> order = new HashMap<>();
        DegradeConfigProperties.DimOrder userOrder = new DegradeConfigProperties.DimOrder();
        userOrder.setOrder(Arrays.asList("car_owner", "car_share", "vip", "all"));
        order.put("UserStrategyEnum", userOrder);

        // 生成较大的取值集合（顺序即优先级）
        DegradeConfigProperties.DimOrder carOrder = new DegradeConfigProperties.DimOrder();
        carOrder.setOrder(genSeq("model", 20));        // 20 种车型
        order.put("CarModelEnum", carOrder);

        DegradeConfigProperties.DimOrder regionOrder = new DegradeConfigProperties.DimOrder();
        regionOrder.setOrder(genSeq("region", 20));    // 20 个区域
        order.put("RegionEnum", regionOrder);

        DegradeConfigProperties.DimOrder channelOrder = new DegradeConfigProperties.DimOrder();
        channelOrder.setOrder(genSeq("ch", 10));       // 10 个渠道
        order.put("ChannelEnum", channelOrder);
        cfg.setOrder(order);

        // relations：all 不带下层；其余主值均允许 3 个下层维度
        Map<String, Map<String, DegradeConfigProperties.ValueRelation>> relations = new HashMap<>();
        Map<String, DegradeConfigProperties.ValueRelation> userRelations = new HashMap<>();
        for (String v : Arrays.asList("car_owner", "car_share", "vip")) {
            DegradeConfigProperties.ValueRelation vr = new DegradeConfigProperties.ValueRelation();
            vr.setEnableDims(Arrays.asList("CarModelEnum", "RegionEnum", "ChannelEnum"));
            userRelations.put(v, vr);
        }
        DegradeConfigProperties.ValueRelation vrAll = new DegradeConfigProperties.ValueRelation();
        vrAll.setEnableDims(Collections.emptyList());
        userRelations.put("all", vrAll);
        relations.put("UserStrategyEnum", userRelations);
        cfg.setRelations(relations);

        DegradeConfigProperties.Fallback fb = new DegradeConfigProperties.Fallback();
        fb.setBeam(200);
        fb.setMaxCandidates(200000); // 足够大，避免过早截断
        cfg.setFallback(fb);

        // 构造最复杂的 RouteContext：每个维度都携带全量取值
        Map<String, List<String>> dimValues = new HashMap<>();
        dimValues.put("UserStrategyEnum", Arrays.asList("car_owner", "car_share", "vip", "all"));
        dimValues.put("CarModelEnum", genSeq("model", 20));
        dimValues.put("RegionEnum", genSeq("region", 20));
        dimValues.put("ChannelEnum", genSeq("ch", 10));
        RouteContext ctx = RouteContext.of(dimValues);

        // 预估候选规模：3 个主值 × 20×20×10 = 12000，再加 3 个主值 + all ≈ 12004
        RouteCandidateBuilder builder = new RouteCandidateBuilder();
        builder.cfg = cfg;
        List<String> warm = builder.build(ctx);
        System.out.println("Worst-case candidate count (single run): " + warm.size());

        // micro benchmark：热身 20 次，采样 100 次
        int warmup = 20;
        int runs = 100;
        for (int i = 0; i < warmup; i++) {
            List<String> t = builder.build(ctx);
            if (t.isEmpty()) {
                throw new IllegalStateException("unexpected empty candidates");
            }
        }
        long[] ns = new long[runs];
        for (int i = 0; i < runs; i++) {
            long s = System.nanoTime();
            List<String> t = builder.build(ctx);
            long e = System.nanoTime();
            ns[i] = e - s;
            if (t.isEmpty()) {
                throw new IllegalStateException("unexpected empty candidates");
            }
        }
        Arrays.sort(ns);
        double avg = 0;
        long min = ns[0];
        long max = ns[ns.length - 1];
        for (long v : ns) {
            avg += v;
        }
        avg /= runs;
        long p50 = ns[(int) Math.floor(0.50 * (runs - 1))];
        long p95 = ns[(int) Math.floor(0.95 * (runs - 1))];
        long p99 = ns[(int) Math.floor(0.99 * (runs - 1))];

        System.out.println("Worst-case build timings (ms): ");
        System.out.println("  min=" + toMs(min) + ", p50=" + toMs(p50) + ", p95=" + toMs(p95) + ", p99=" + toMs(p99) + ", max=" + toMs(max) + ", avg=" + toMs((long) avg));
    }

    private static List<String> genSeq(String prefix, int n) {
        List<String> list = new ArrayList<>(n);
        for (int i = 1; i <= n; i++) {
            list.add(prefix + i);
        }
        return list;
    }


    private static String toMs(long ns) {
        double ms = ns / 1_000_000.0;
        return String.format("%.3f", ms);
    }

    

    private List<String> orderedValuesOf(RouteContext ctx, String dim) {
        if (ctx == null || dim == null) {
            return Collections.emptyList();
        }
        List<String> vals = ctx.valuesOf(dim);
        if (vals == null || vals.isEmpty()) {
            return Collections.emptyList();
        }
        DegradeConfigProperties.DimOrder dimCfg = cfg.getOrder() == null ? null : cfg.getOrder().get(dim);
        if (dimCfg == null || dimCfg.getOrder() == null || dimCfg.getOrder().isEmpty()) {
            return new ArrayList<>(new LinkedHashSet<>(vals));
        }
        LinkedHashSet<String> ordered = new LinkedHashSet<>();
        for (String v : dimCfg.getOrder()) {
            if (vals.contains(v)) {
                ordered.add(v);
            }
        }
        for (String v : vals) {
            if (!ordered.contains(v)) {
                ordered.add(v);
            }
        }
        return new ArrayList<>(ordered);
    }

    private List<String> filterAllowedSubDims(String primaryDim, String primaryVal, List<String> subDims) {
        if (subDims == null || subDims.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, Map<String, DegradeConfigProperties.ValueRelation>> relations = cfg.getRelations();
        if (relations == null) {
            return new ArrayList<>(subDims);
        }
        Map<String, DegradeConfigProperties.ValueRelation> byVal = relations.get(primaryDim);
        if (byVal == null) {
            return new ArrayList<>(subDims);
        }
        DegradeConfigProperties.ValueRelation vr = byVal.get(primaryVal);
        if (vr == null || vr.getEnableDims() == null) {
            return new ArrayList<>(subDims);
        }
        LinkedHashSet<String> allow = new LinkedHashSet<>(vr.getEnableDims());
        List<String> res = new ArrayList<>();
        for (String d : subDims) {
            if (allow.contains(d)) {
                res.add(d);
            }
        }
        return res;
    }

    private void cartesianAppendWithPrefix(String prefix,
                                           List<List<String>> subDimVals,
                                           LinkedHashSet<String> out,
                                           int maxCand) {
        // 迭代式生成，避免深递归
        List<String> acc = new ArrayList<>();
        dfsProduct(prefix, subDimVals, 0, acc, out, maxCand);
    }

    private void dfsProduct(String prefix,
                            List<List<String>> lists,
                            int idx,
                            List<String> acc,
                            LinkedHashSet<String> out,
                            int maxCand) {
        if (out.size() >= maxCand) {
            return;
        }
        if (idx >= lists.size()) {
            if (!acc.isEmpty()) {
                StringBuilder sb = new StringBuilder(prefix);
                for (String s : acc) {
                    sb.append('|').append(s);
                }
                out.add(sb.toString());
            }
            return;
        }
        List<String> cur = lists.get(idx);
        if (cur == null || cur.isEmpty()) {
            dfsProduct(prefix, lists, idx + 1, acc, out, maxCand);
            return;
        }
        for (String v : cur) {
            if (out.size() >= maxCand) {
                return;
            }
            acc.add(v);
            dfsProduct(prefix, lists, idx + 1, acc, out, maxCand);
            acc.remove(acc.size() - 1);
        }
    }

    
}
