package com.xiaomi.micar.site.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.dao.SitePageConfigDraftEngine;
import com.xiaomi.micar.site.dao.SitePageConfigEngine;
import com.xiaomi.micar.site.dao.SitePageModuleEngine;
import com.xiaomi.micar.site.dao.SitePageModuleStrategyEngine;
import com.xiaomi.micar.site.dao.SiteWhiteUserEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigDraftEntity;
import com.xiaomi.micar.site.dao.entity.SitePageConfigEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleEntity;
import com.xiaomi.micar.site.dao.entity.SitePageModuleStrategyEntity;
import com.xiaomi.micar.site.dao.entity.SiteTemplateEntity;
import com.xiaomi.micar.site.dao.entity.SiteWhiteUserEntity;
import com.xiaomi.micar.site.enums.ConfigStatus;
import com.xiaomi.micar.site.enums.DraftType;
import com.xiaomi.micar.site.excepiton.PreviewException;
import com.xiaomi.micar.site.metrics.aop.SiteMetrics;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import com.xiaomi.micar.site.service.assembly.SitePageAssemblyService;
import com.xiaomi.micar.site.up.enums.UserStrategyEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 页面预览服务
 * 提供页面预览相关功能，包括草稿预览、特定版本预览等
 *
 * 核心业务流程：
 * 1. 验证用户权限（白名单检查）
 * 2. 验证并加载草稿信息
 * 3. 准备页面配置数据
 * 4. 生成预览响应
 *
 * <AUTHOR>
 * @date 2025/5/6
 */
@Service
@Slf4j
public class PagePreviewService {

    @Resource
    private SiteWhiteUserEngine siteWhiteUserEngine;
    @Resource
    private SitePageConfigDraftEngine draftEngine;
    @Resource
    private SitePageConfigEngine configEngine;
    @Resource
    private SitePageAssemblyService sitePageAssemblyService;
    @Resource
    private PagePreviewService self;
    @Resource
    private SitePageModuleEngine moduleEngine;
    @Resource
    private SitePageModuleStrategyEngine moduleStrategyEngine;
    @Resource
    private PageConfigAggregationService pageConfigAggregationService;

    /**
     * 处理特定版本预览请求 - 主入口方法
     *
     * @param uid    用户ID
     * @param scv    特定版本ID（草稿ID）
     * @param pageId 页面ID（用于日志记录）
     * @return 页面响应结果
     */
    public Result<PageRespV2> handleSpecificVersionPreview(String uid, String scv, String pageId) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理特定版本预览请求, scv: {}, uid: {}, pageId: {}", scv, uid, pageId);

        try {
            // 1. 验证用户权限
            validateUserPermission(uid);

            // 2. 获取草稿信息
            SitePageConfigDraftEntity draft = getDraftById(scv);

            // 3. 生成预览响应
            PageRespV2 response = generatePreviewResponse(draft, uid);

            // 4. 记录成功日志
            long processingTime = System.currentTimeMillis() - startTime;
            log.info("特定版本预览请求处理成功, scv: {}, pageId: {}, 耗时: {}ms",
                    scv, draft.getPageId(), processingTime);

            return Result.success(response);

        } catch (PreviewException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.warn("预览请求失败: scv={}, error={}, 耗时: {}ms", scv, e.getMessage(), processingTime);
            return Result.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("处理特定版本预览请求时发生异常: scv={}, error={}, 耗时: {}ms",
                    scv, e.getMessage(), processingTime, e);
            return Result.fail(GeneralCodes.InternalError, "预览失败，系统异常");
        }
    }

    /**
     * 验证用户权限（白名单检查）
     */
    private void validateUserPermission(String uid) {
        if (StringUtils.isEmpty(uid)) {
            throw PreviewException.paramError("请求参数有误，用户未登录");
        }

        // 获取白名单用户
        SiteWhiteUserEntity whiteUser = siteWhiteUserEngine.getOne(
                new LambdaQueryWrapper<SiteWhiteUserEntity>()
                        .eq(SiteWhiteUserEntity::getMid, uid));

        log.info("用户白名单检查, uid: {}, whiteUser: {}", uid,
                whiteUser != null ? "存在" : "不存在");

        if (whiteUser == null) {
            throw PreviewException.permissionDenied("您没有预览权限，请联系管理员");
        }
    }

    /**
     * 获取草稿信息
     */
    private SitePageConfigDraftEntity getDraftById(String scv) {
        SitePageConfigDraftEntity draft = draftEngine.getById(scv);
        if (draft == null) {
            throw PreviewException.paramError("未找到指定的配置版本");
        }
        // 验证草稿类型
        DraftType.getByCode(draft.getType());
        return draft;
    }

    /**
     * 生成预览响应
     */
    private PageRespV2 generatePreviewResponse(SitePageConfigDraftEntity draft, String uid) {
        Integer type = draft.getType();

        if (type == DraftType.RECORD_CONTENT.getCode()) {
            return handleRecordPreview(draft, uid);
        } else if (type == DraftType.TOPIC_PAGE_CONTENT.getCode()) {
            return handleTopicPagePreview(draft, uid);
        } else if (type == DraftType.PAGE_STRATEGY_MODIFY.getCode()) {
            return handleModuleOrderPreview(draft, uid);
        } else {
            return handleModulePreview(draft, uid);
        }
    }

    /**
     * 处理记录类型预览
     */
    private PageRespV2 handleRecordPreview(SitePageConfigDraftEntity draft, String uid) {
        // 获取当前页面配置用于记录预览
        SitePageConfigEntity currentConfig = configEngine.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, draft.getPageId())
                .orderByDesc(SitePageConfigEntity::getVersion)
                .last("limit 1")
                .one();

        if (currentConfig == null) {
            throw PreviewException.paramError("当前页面配置不存在");
        }

        Map<String, String> queryParams = Maps.newConcurrentMap();
        queryParams.put("draftId", String.valueOf(draft.getId()));

        SitePageConfig previewConfig = createPreviewConfig(
                draft, currentConfig.getConfig(), currentConfig.getVersion());

        return self.getPageData(previewConfig, uid, queryParams);
    }

    /**
     * 处理模块类型预览
     */
    private PageRespV2 handleModulePreview(SitePageConfigDraftEntity draft, String uid) {
        // 根据状态决定预览配置
        String previewConfig = isOfflineAudit(draft) 
            ? getOfflinePreviewConfig(draft)
            : aggregatePageConfigWithDraftOverride(draft);
        
        // 生成预览
        SitePageConfig config = createPreviewConfig(draft, previewConfig, 1);
        String strategy = findModuleStrategy(draft.getPageId(), draft.getModuleId());
        
        return self.getPageDataWithStrategy(config, uid, Maps.newHashMap(), strategy);
    }

    /**
     * 判断是否为下线审核状态
     */
    private boolean isOfflineAudit(SitePageConfigDraftEntity draft) {
        return ConfigStatus.OFFLINE_AUDIT.getCode() == draft.getStatus();
    }

    /**
     * 获取下线预览配置
     */
    private String getOfflinePreviewConfig(SitePageConfigDraftEntity draft) {
        log.info("处理下线模块预览: draftId={}, pageId={}, moduleId={}", 
                draft.getId(), draft.getPageId(), draft.getModuleId());
        
        String previousConfig = findPreviousOnlineConfig(draft);
        if (previousConfig != null) {
            log.info("使用历史上线版本配置: draftId={}", draft.getId());
        } else {
            log.info("模块将彻底下线，从页面配置中移除: pageId={}, moduleId={}", 
                    draft.getPageId(), draft.getModuleId());
        }
        
        return aggregatePageConfigWithDraftOverride(draft, previousConfig);
    }

    /**
     * 查找之前的在线配置
     */
    private String findPreviousOnlineConfig(SitePageConfigDraftEntity currentDraft) {
        try {
            SitePageConfigDraftEntity previousOnlineVersion = draftEngine.lambdaQuery()
                    .eq(SitePageConfigDraftEntity::getPageId, currentDraft.getPageId())
                    .eq(SitePageConfigDraftEntity::getModuleId, currentDraft.getModuleId())
                    .eq(SitePageConfigDraftEntity::getStatus, ConfigStatus.DEPLOYED.getCode())
                    .ne(SitePageConfigDraftEntity::getId, currentDraft.getId())
                    .orderByDesc(SitePageConfigDraftEntity::getUpdateTime)
                    .last("LIMIT 1")
                    .one();

            return previousOnlineVersion != null ? previousOnlineVersion.getConfig() : null;
        } catch (Exception e) {
            log.error("查找历史上线版本失败: pageId={}, moduleId={}", 
                    currentDraft.getPageId(), currentDraft.getModuleId(), e);
            return null;
        }
    }


    /**
     * 处理专题页面配置预览
     * 用于整个页面的统一配置预览，使用默认策略显示所有内容
     */
    private PageRespV2 handleTopicPagePreview(SitePageConfigDraftEntity draft, String uid) {
        try {
            log.info("开始处理专题页面配置预览: draftId={}, pageId={}", draft.getId(), draft.getPageId());

            // 1. 获取当前页面的基础配置（可能不存在，比如新建页面）
            SitePageConfigEntity currentConfig = getCurrentPageConfigOrNull(draft.getPageId());

            // 2. 根据草稿配置生成预览用的页面配置
            String previewPageConfig = generatePreviewPageConfig(currentConfig, draft);

            // 3. 创建预览配置对象（版本号根据是否有基础配置来决定）
            Integer version = currentConfig != null ? currentConfig.getVersion() : 1;
            SitePageConfig previewConfig = createPreviewConfig(draft, previewPageConfig, version);

            // 4. 专题页面配置预览使用默认策略，显示所有内容
            String previewStrategy = UserStrategyEnum.ALL.getCode();
            log.info("专题页面配置预览使用默认策略: strategy={}", previewStrategy);

            return self.getPageDataWithStrategy(previewConfig, uid, Maps.newHashMap(), previewStrategy);

        } catch (Exception e) {
            log.error("专题页面配置预览处理失败: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            throw new RuntimeException("专题页面配置预览处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理模块排序预览
     * 用于调整模块在不同策略中的顺序，需要根据修改的策略来预览
     */
    private PageRespV2 handleModuleOrderPreview(SitePageConfigDraftEntity draft, String uid) {
        try {
            log.info("开始处理模块排序预览: draftId={}, pageId={}", draft.getId(), draft.getPageId());

            // 1. 获取当前页面的基础配置（可能不存在，比如新建页面）
            SitePageConfigEntity currentConfig = getCurrentPageConfigOrNull(draft.getPageId());

            // 2. 根据草稿配置生成预览用的页面配置
            String previewPageConfig = generatePreviewPageConfig(currentConfig, draft);

            // 3. 创建预览配置对象（版本号根据是否有基础配置来决定）
            Integer version = currentConfig != null ? currentConfig.getVersion() : 1;
            SitePageConfig previewConfig = createPreviewConfig(draft, previewPageConfig, version);

            // 4. 模块排序预览需要根据修改的策略来显示
            String previewStrategy = extractStrategyFromModuleOrderDraft(draft);
            log.info("模块排序预览使用策略: strategy={}", previewStrategy);

            return self.getPageDataWithStrategy(previewConfig, uid, Maps.newHashMap(), previewStrategy);

        } catch (Exception e) {
            log.error("模块排序预览处理失败: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            throw new RuntimeException("模块排序预览处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当前页面配置（可能为null，用于新建页面的情况）
     */
    private SitePageConfigEntity getCurrentPageConfigOrNull(String pageId) {
        SitePageConfigEntity config = configEngine.lambdaQuery()
                .eq(SitePageConfigEntity::getPageId, pageId)
                .orderByDesc(SitePageConfigEntity::getVersion)
                .last("limit 1")
                .one();
        
        if (config == null) {
            log.info("页面配置不存在，可能是新建页面: pageId={}", pageId);
        }
        
        return config;
    }

    /**
     * 获取当前页面配置（必须存在，否则抛异常）
     */
    private SitePageConfigEntity getCurrentPageConfig(String pageId) {
        SitePageConfigEntity config = getCurrentPageConfigOrNull(pageId);
        
        if (config == null) {
            throw PreviewException.paramError("当前页面配置不存在");
        }
        
        return config;
    }

    /**
     * 生成预览页面配置
     * 根据草稿中的配置内容生成预览用的页面配置
     */
    private String generatePreviewPageConfig(SitePageConfigEntity currentConfig, SitePageConfigDraftEntity draft) {
        try {
            String draftConfig = draft.getConfig();
            
            // 如果草稿配置为空，则需要根据情况处理
            if (StringUtils.isBlank(draftConfig)) {
                if (currentConfig != null) {
                    // 有基础配置：使用当前页面配置
                    log.warn("草稿配置为空，使用当前页面配置: pageId={}", draft.getPageId());
                    return currentConfig.getConfig();
                } else {
                    // 新建页面且草稿配置为空：返回空的页面配置
                    log.warn("新建页面且草稿配置为空，返回默认空配置: pageId={}", draft.getPageId());
                    return createEmptyPageConfig();
                }
            }
            
            // 验证草稿配置是否为有效的JSON
            try {
                JsonUtil.readTree(draftConfig);
                return draftConfig;
            } catch (Exception e) {
                if (currentConfig != null) {
                    // 有基础配置：使用当前页面配置
                    log.warn("草稿配置不是有效的JSON，使用当前页面配置: pageId={}, error={}", 
                            draft.getPageId(), e.getMessage());
                    return currentConfig.getConfig();
                } else {
                    // 新建页面且草稿配置无效：返回空的页面配置
                    log.warn("新建页面且草稿配置无效，返回默认空配置: pageId={}, error={}", 
                            draft.getPageId(), e.getMessage());
                    return createEmptyPageConfig();
                }
            }
            
        } catch (Exception e) {
            log.error("生成预览页面配置失败: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            throw new RuntimeException("生成预览页面配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建空的页面配置
     * 用于新建页面的预览
     */
    private String createEmptyPageConfig() {
        try {
            // 创建一个基本的空页面配置
            Map<String, Object> emptyConfig = new HashMap<>();
            emptyConfig.put("components", new ArrayList<>());
            emptyConfig.put("groupConfig", new ArrayList<>());
            emptyConfig.put("groupId", "1");
            
            return JsonUtil.toJSONString(emptyConfig);
        } catch (Exception e) {
            log.error("创建空页面配置失败: {}", e.getMessage(), e);
            // 如果连默认配置都创建失败，返回最简单的JSON
            return "{\"components\":[],\"groupConfig\":[],\"groupId\":\"1\"}";
        }
    }

    /**
     * 使用聚合逻辑生成预览配置
     * 复用 PageConfigAggregationService，但用草稿配置覆盖对应模块的配置
     *
     * @param draftEntity 草稿配置
     * @return 聚合后的配置
     */
    private String aggregatePageConfigWithDraftOverride(SitePageConfigDraftEntity draftEntity) {
        return aggregatePageConfigWithDraftOverride(draftEntity, draftEntity.getConfig());
    }

    /**
     * 使用聚合逻辑生成预览配置
     * 复用 PageConfigAggregationService，但用指定配置覆盖对应模块的配置
     *
     * @param draftEntity 草稿配置
     * @param overrideConfig 覆盖配置，为null时表示删除该模块
     * @return 聚合后的配置
     */
    private String aggregatePageConfigWithDraftOverride(SitePageConfigDraftEntity draftEntity, String overrideConfig) {
        String pageId = draftEntity.getPageId();
        Integer draftModuleId = draftEntity.getModuleId();
        String draftConfig = overrideConfig;

        log.info("开始聚合预览配置: pageId={}, draftModuleId={}", pageId, draftModuleId);

        try {
            // 1. 获取页面所有模块
            List<SitePageModuleEntity> modules = moduleEngine.lambdaQuery()
                    .eq(SitePageModuleEntity::getPageId, pageId)
                    .orderByAsc(SitePageModuleEntity::getId)
                    .list();

            if (modules.isEmpty()) {
                log.warn("页面没有配置任何模块: pageId={}", pageId);
                return pageConfigAggregationService.createEmptyPageConfig();
            }

            // 2. 用草稿配置覆盖对应模块的配置
            modules = overrideModuleWithDraft(modules, draftModuleId, draftConfig);

            // 3. 获取模块顺序策略
            List<SitePageModuleStrategyEntity> strategies = moduleStrategyEngine.getByPageId(pageId);

            // 4. 获取模板配置
            Map<String, SiteTemplateEntity> templateMap = pageConfigAggregationService.getTemplateConfigs(modules);

            // 5. 构建页面配置 - 直接复用 PageConfigAggregationService 的方法
            ObjectNode pageConfig = pageConfigAggregationService.buildPageConfig(modules, strategies, templateMap);

            String result = JsonUtil.toJSONString(pageConfig);
            log.info("预览配置聚合成功: pageId={}, draftModuleId={}, 模块数量={}, 策略数量={}, 模板数量={}",
                    pageId, draftModuleId, modules.size(), strategies.size(), templateMap.size());

            return result;

        } catch (Exception e) {
            log.error("预览配置聚合失败: pageId={}, draftModuleId={}, error={}", pageId, draftModuleId, e.getMessage(), e);
            throw new RuntimeException("预览配置聚合失败: " + e.getMessage(), e);
        }
    }




    /**
     * 创建预览配置对象
     *
     * @param draftEntity 草稿实体
     * @param mergedConfig 合并后的配置
     * @param version 版本号
     * @return 预览配置对象
     */
    private SitePageConfig createPreviewConfig(SitePageConfigDraftEntity draftEntity,
                                              String mergedConfig,
                                              Integer version) {
        SitePageConfig newConfig = new SitePageConfig();
        newConfig.setPageId(draftEntity.getPageId());
        newConfig.setPageName("预览页面"); // 预览时页面名称不重要
        newConfig.setConfig(mergedConfig);
        newConfig.setVersion(version);
        newConfig.setCreateTime(new Date());
        newConfig.setUpdateTime(new Date());
        return newConfig;
    }

    @SiteMetrics(type = "page", pageId = "#pageConfig.pageId")
    public PageRespV2 getPageData(SitePageConfig pageConfig, String userId, Map<String, String> queryParams) {
        return sitePageAssemblyService.getResponseForPreview(pageConfig, userId, new HashMap<>(queryParams));
    }

    /**
     * 带策略参数的页面数据获取方法
     * 用于预览时指定特定策略
     */
    @SiteMetrics(type = "page", pageId = "#pageConfig.pageId")
    public PageRespV2 getPageDataWithStrategy(SitePageConfig pageConfig, String userId, Map<String, String> queryParams, String strategy) {
        return sitePageAssemblyService.getResponseForPreviewWithStrategy(pageConfig, userId, new HashMap<>(queryParams), strategy);
    }

    /**
     * 用草稿配置覆盖对应模块的配置
     * @param modules 模块列表
     * @param draftModuleId 草稿模块ID
     * @param draftConfig 草稿配置，为null时表示删除该模块
     */
    private List<SitePageModuleEntity> overrideModuleWithDraft(List<SitePageModuleEntity> modules,
                                                              Integer draftModuleId,
                                                              String draftConfig) {
        return modules.stream()
                .filter(module -> {
                    // 如果是目标模块且配置为null，则过滤掉（删除模块）
                    if (module.getId().equals(draftModuleId.longValue()) && draftConfig == null) {
                        log.info("删除模块: moduleId={}, moduleName={}", module.getId(), module.getModuleName());
                        return false;
                    }
                    return true;
                })
                .map(module -> {
                    if (module.getId().equals(draftModuleId.longValue()) && draftConfig != null) {
                        // 创建新的模块实体，用草稿配置覆盖原配置
                        SitePageModuleEntity overriddenModule = new SitePageModuleEntity();
                        overriddenModule.setId(module.getId());
                        overriddenModule.setPageId(module.getPageId());
                        overriddenModule.setModuleName(module.getModuleName());
                        overriddenModule.setComponent(module.getComponent());
                        overriddenModule.setModuleId(module.getModuleId());
                        overriddenModule.setTemplate(module.getTemplate());
                        overriddenModule.setDataProvider(module.getDataProvider());
                        overriddenModule.setDataProviderParams(module.getDataProviderParams());
                        overriddenModule.setConfig(draftConfig); // 使用草稿配置
                        overriddenModule.setCreateTime(module.getCreateTime());
                        overriddenModule.setUpdateTime(module.getUpdateTime());
                        overriddenModule.setDeleted(module.getDeleted());
                        log.info("覆盖模块配置: moduleId={}, moduleName={}", module.getId(), module.getModuleName());
                        return overriddenModule;
                    }
                    return module;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查找模块所属的策略
     * 根据模块ID在策略配置中查找该模块属于哪个策略
     *
     * @param pageId   页面ID
     * @param moduleId 模块ID
     * @return 策略代码，如果未找到则返回默认策略 "all"
     */
    private String findModuleStrategy(String pageId, Integer moduleId) {
        try {
            log.info("查找模块策略: pageId={}, moduleId={}", pageId, moduleId);

            // 获取页面的所有策略配置
            List<SitePageModuleStrategyEntity> strategies = moduleStrategyEngine.getByPageId(pageId);
            if (strategies.isEmpty()) {
                log.info("页面没有策略配置，使用默认策略: pageId={}", pageId);
                return "all";
            }

            // 查找包含该模块的策略
            for (SitePageModuleStrategyEntity strategy : strategies) {
                String config = strategy.getConfig();
                if (config != null && !config.trim().isEmpty()) {
                    try {
                        // 解析策略配置，期望格式为 JSON 数组，如 [1, 2, 3]
                        JsonNode configNode = JsonUtil.readTree(config);
                        if (configNode.isArray()) {
                            ArrayNode configArray = (ArrayNode) configNode;
                            for (JsonNode moduleIdNode : configArray) {
                                String configModuleId = moduleIdNode.asText();
                                if (String.valueOf(moduleId).equals(configModuleId)) {
                                    log.info("找到模块所属策略: pageId={}, moduleId={}, strategy={}",
                                            pageId, moduleId, strategy.getStrategy());
                                    return strategy.getStrategy();
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("解析策略配置失败: strategy={}, config={}, error={}",
                                strategy.getStrategy(), config, e.getMessage());
                    }
                }
            }

            log.info("模块未在任何策略中找到，使用默认策略: pageId={}, moduleId={}", pageId, moduleId);
            return "all";

        } catch (Exception e) {
            log.error("查找模块策略失败: pageId={}, moduleId={}, error={}", pageId, moduleId, e.getMessage(), e);
            return "all";
        }
    }

    /**
     * 从模块排序草稿中提取策略信息
     * 模块排序草稿的配置通常包含策略信息，需要解析出来
     *
     * @param draft 模块排序草稿
     * @return 策略代码，如果无法提取则返回默认策略 "all"
     */
    private String extractStrategyFromModuleOrderDraft(SitePageConfigDraftEntity draft) {
        try {
            String config = draft.getConfig();
            if (config == null || config.trim().isEmpty()) {
                log.info("模块排序草稿配置为空，使用默认策略: draftId={}", draft.getId());
                return "all";
            }

            // 解析草稿配置，查找策略信息
            JsonNode configNode = JsonUtil.readTree(config);

            // 尝试从不同可能的字段中提取策略信息
            // 1. 直接的 strategy 字段
            if (configNode.has("strategy")) {
                String strategy = configNode.get("strategy").asText();
                if (strategy != null && !strategy.trim().isEmpty()) {
                    log.info("从草稿配置中提取到策略: draftId={}, strategy={}", draft.getId(), strategy);
                    return strategy;
                }
            }

            // 2. 从 groupConfig 中提取策略
            if (configNode.has("groupConfig") && configNode.get("groupConfig").isArray()) {
                ArrayNode groupConfigArray = (ArrayNode) configNode.get("groupConfig");
                for (JsonNode groupNode : groupConfigArray) {
                    if (groupNode.has("strategy")) {
                        String strategy = groupNode.get("strategy").asText();
                        if (strategy != null && !strategy.trim().isEmpty()) {
                            log.info("从groupConfig中提取到策略: draftId={}, strategy={}", draft.getId(), strategy);
                            return strategy;
                        }
                    }
                }
            }

            // 3. 如果是页面级别的配置，可能需要查看当前修改的是哪个策略的排序
            // 这里可以根据实际的草稿配置格式进行调整

            log.info("无法从模块排序草稿中提取策略，使用默认策略: draftId={}", draft.getId());
            return UserStrategyEnum.ALL.getCode();

        } catch (Exception e) {
            log.error("提取模块排序策略失败: draftId={}, error={}", draft.getId(), e.getMessage(), e);
            return UserStrategyEnum.ALL.getCode();
        }
    }
}
