package com.xiaomi.micar.site.route.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路由退化配置（可选）。
 * 未配置时，候选生成退回到“去尾降级”默认实现。
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "site.route.degrade")
public class DegradeConfigProperties {
    /**
     * 维度降级顺序（右端优先降维），例如：[UserStrategyEnum, CarModelEnum]
     */
    private List<String> dimensionOrder;

    /**
     * 各维度的排序与别名配置（同层替代）
     */
    private Map<String, DimOrder> order = new HashMap<>();

    /**
     * 维度间关系：声明在“上层维度的具体取值”下，哪些“下层维度”是允许参与组合的。
     * 例如：
     * relations["UserStrategyEnum"]["car_owner"].enableDims = ["CarModelEnum"]
     * relations["UserStrategyEnum"]["all"].enableDims      = []  // all 不带车型
     */
    private Map<String, Map<String, ValueRelation>> relations = new HashMap<>();

    /**
     * 候选规模控制（可选）
     */
    private Fallback fallback = new Fallback();

    @Data
    public static class DimOrder {
        /**
         * 同层优先序，靠前越优先
         */
        private List<String> order;
    }

    @Data
    public static class ValueRelation {
        /**
         * 在该取值下允许参与组合的“下层维度”名列表（必须与 dimensionOrder 中的名称一致）。
         * 为空或缺省表示“默认允许所有下层维度”。
         */
        private List<String> enableDims;
    }

    @Data
    public static class Fallback {
        private int beam = 200;           // 内部筛选上限
        private int maxCandidates = 10;   // 输出候选上限
    }
}
