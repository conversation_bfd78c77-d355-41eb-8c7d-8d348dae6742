package com.xiaomi.micar.site.service.assembly.provider;

import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 站点数据提供者工厂
 * 负责管理和获取不同类型的数据提供者
 */
@Slf4j
public class SiteDataProviderFactory {

    /**
     * 数据源类型
     */
    public enum DataSourceType {
        EVENT,
        CUSTOM
    }

    private final Map<DataSourceType, SiteDataProvider> providers = new HashMap<>();
    private DataSourceType defaultType = DataSourceType.CUSTOM;

    /**
     * 构造函数
     */
    public SiteDataProviderFactory() {
        log.info("Initializing SiteDataProviderFactory");
    }

    /**
     * 注册数据提供者
     * @param type 数据源类型
     * @param provider 数据提供者
     */
    public void put(DataSourceType type, SiteDataProvider provider) {
        if (provider != null) {
            providers.put(type, provider);
            log.info("Registered site data provider for type: {}", type);
        }
    }

    /**
     * 设置默认数据源类型
     * @param type 数据源类型
     */
    public void setDefaultType(DataSourceType type) {
        this.defaultType = type;
        log.info("Default data source type set to: {}", type);
    }

    /**
     * 获取指定类型的数据提供者
     * @param type 数据源类型
     * @return 数据提供者
     */
    public SiteDataProvider getProvider(DataSourceType type) {
        SiteDataProvider provider = providers.get(type);
        if (provider == null) {
            log.warn("No provider found for type: {}, using default", type);
            provider = providers.get(defaultType);
            if (provider == null) {
                log.error("No default provider found");
                throw new IllegalStateException("No data provider available");
            }
        }
        return provider;
    }

    /**
     * 获取默认数据源类型
     * @return 默认数据源类型
     */
    public DataSourceType getDefaultType() {
        return defaultType;
    }

    /**
     * 从指定数据源获取所有页面配置
     * @param type 数据源类型
     * @return 页面配置列表
     */
    public List<SitePageConfig> getAllPageConfigs(DataSourceType type) {
        return getProvider(type).getAllPageConfigs();
    }

    /**
     * 从指定数据源获取所有实验
     * @param type 数据源类型
     * @return 实验列表
     */
    public List<SiteExpConfig> getAllExperiments(DataSourceType type) {
        return getProvider(type).getAllExperiments();
    }

    /**
     * 从指定数据源获取所有实验桶
     * @param type 数据源类型
     * @return 实验桶列表
     */
    public List<SiteExpBuckets> getAllExperimentBuckets(DataSourceType type) {
        return getProvider(type).getAllExperimentBuckets();
    }

}
