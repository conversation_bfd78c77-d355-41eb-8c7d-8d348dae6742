package com.xiaomi.micar.site.metrics.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SiteMetrics {
    String TYPE_SITE = "site";
    String TYPE_PAGE = "page";
    String TYPE_ACTION = "action";

    String type();

    String pageId() default "";

    String action() default "";

}
