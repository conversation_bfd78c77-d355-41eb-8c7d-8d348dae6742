package com.xiaomi.micar.site.service;
import com.xiaomi.micar.site.service.assembly.provider.CustomSiteDataProvider;
import com.xiaomi.micar.site.dao.SiteExpBucketEngine;
import com.xiaomi.micar.site.dao.SiteExpConfigEngine;
import com.xiaomi.micar.site.dao.SitePageConfigEngine;
import com.xiaomi.micar.site.dao.SitePageInfoEngine;
import com.xiaomi.micar.site.dao.entity.SitePageConfigEntity;
import com.xiaomi.micar.site.dao.entity.SitePageInfoEntity;
import com.xiaomi.micar.site.enums.LoadTypeEnum;
import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 站点数据提供实现
 *
 * <AUTHOR>
 * @since 2025/03/18
 */
@Slf4j
@Component
public class DBSiteDataLoader implements CustomSiteDataProvider.DataLoader {

    @Resource
    private SitePageInfoEngine pageInfoEngine;
    @Resource
    private SitePageConfigEngine pageConfigEngine;
    @Resource
    private SiteExpConfigEngine expConfigEngine;
    @Resource
    private SiteExpBucketEngine expBucketEngine;
    @Resource
    private PageConfigAggregationService pageConfigAggregationService;

    @Override
    public List<SitePageConfig> loadPageConfigs() {
        log.info("开始加载页面配置，根据 load_type 决定配置加载方式");

        List<SitePageInfoEntity> pages = pageInfoEngine.list();
        log.info("获取到 {} 个页面信息", pages.size());

        return pages.stream()
                .filter(pageInfo -> {
                    // 过滤下线的页面（status=0 表示下线）
                    if (pageInfo.getStatus() != null && pageInfo.getStatus() == 0) {
                        log.debug("过滤下线页面: pageId={}, status={}", pageInfo.getPageId(), pageInfo.getStatus());
                        return false;
                    }
                    return true;
                })
                .map(pageInfo -> {
                    try {
                        Integer loadType = pageInfo.getLoadType();
                        log.info("开始加载页面配置: pageId={}, pageName={}, loadType={}",
                                pageInfo.getPageId(), pageInfo.getPageName(), loadType);

                        if (LoadTypeEnum.isPageDirect(loadType)) {
                            // load_type = 1: 直接从 site_page_config 获取配置
                            return loadFromPageConfig(pageInfo);
                        } else {
                            // load_type = 2 或 null: 从 site_page_module 聚合配置
                            return loadFromModuleAggregation(pageInfo);
                        }
                    } catch (Exception e) {
                        log.error("加载页面配置失败: pageId={}, pageName={}, error={}",
                                pageInfo.getPageId(), pageInfo.getPageName(), e.getMessage(), e);

                        // 如果加载失败，回退到原有方式
                        log.warn("回退到原有配置方式: pageId={}", pageInfo.getPageId());
                        return fallbackToOriginalConfig(pageInfo);
                    }
                })
                .filter(config -> config != null) // 过滤掉已下线的页面(config为null)
                .collect(Collectors.toList());
    }

    /**
     * 从 site_page_config 表直接获取配置
     */
    private SitePageConfig loadFromPageConfig(SitePageInfoEntity pageInfo) {
        log.info("从 site_page_config 表加载配置: pageId={}", pageInfo.getPageId());

        SitePageConfigEntity entity = pageConfigEngine.getByPageAndVersion(pageInfo.getPageId(), pageInfo.getVersion());
        if (entity == null || entity.getConfig() == null) {
            log.warn("未找到页面配置或配置为空(页面已下线): pageId={}, version={}", pageInfo.getPageId(), pageInfo.getVersion());
            return null; // 返回 null 表示页面已下线，在上层过滤
        }

        SitePageConfig config = new SitePageConfig();
        config.setId(entity.getId());
        config.setPageId(entity.getPageId());
        config.setPageName(entity.getPageName());
        config.setConfig(entity.getConfig());
        config.setVersion(entity.getVersion());
        config.setStrategyState(pageInfo.getStrategyState()); // 传递人群策略状态
        config.setCreateTime(entity.getCreateTime());
        config.setUpdateTime(entity.getUpdateTime());

        log.info("成功从 site_page_config 加载配置: pageId={}, configLength={}",
                pageInfo.getPageId(), entity.getConfig() != null ? entity.getConfig().length() : 0);

        return config;
    }

    /**
     * 从 site_page_module 表聚合配置
     */
    private SitePageConfig loadFromModuleAggregation(SitePageInfoEntity pageInfo) {
        log.info("从 site_page_module 表聚合配置: pageId={}", pageInfo.getPageId());

        // 使用 PageConfigAggregationService 实时聚合页面配置
        // 注意：聚合过程中会自动过滤掉 config 为 null 的模块，但不影响整个页面
        String aggregatedConfig = pageConfigAggregationService.aggregatePageConfig(pageInfo.getPageId());

        // 构建 SitePageConfig 对象
        SitePageConfig config = new SitePageConfig();
        config.setId(null); // 实时聚合的配置没有固定ID
        config.setPageId(pageInfo.getPageId());
        config.setPageName(pageInfo.getPageName());
        config.setConfig(aggregatedConfig);
        config.setVersion(pageInfo.getVersion());
        config.setStrategyState(pageInfo.getStrategyState()); // 传递人群策略状态
        config.setCreateTime(new Date());
        config.setUpdateTime(new Date());

        log.info("成功聚合页面配置: pageId={}, configLength={}",
                pageInfo.getPageId(), aggregatedConfig != null ? aggregatedConfig.length() : 0);

        return config;
    }

    /**
     * 回退到原有的配置加载方式
     */
    private SitePageConfig fallbackToOriginalConfig(SitePageInfoEntity pageInfo) {
        try {
            SitePageConfigEntity entity = pageConfigEngine.getByPageAndVersion(pageInfo.getPageId(), pageInfo.getVersion());
            if (entity == null || entity.getConfig() == null) {
                log.warn("未找到页面配置或配置为空(页面已下线): pageId={}, version={}", pageInfo.getPageId(), pageInfo.getVersion());
                return null; // 返回 null 表示页面已下线，在上层过滤
            }

            SitePageConfig config = new SitePageConfig();
            config.setId(entity.getId());
            config.setPageId(entity.getPageId());
            config.setPageName(entity.getPageName());
            config.setConfig(entity.getConfig());
            config.setVersion(entity.getVersion());
            config.setStrategyState(pageInfo.getStrategyState()); // 传递人群策略状态
            config.setCreateTime(entity.getCreateTime());
            config.setUpdateTime(entity.getUpdateTime());
            return config;
        } catch (Exception e) {
            log.error("回退配置加载也失败: pageId={}, error={}", pageInfo.getPageId(), e.getMessage(), e);
            return null; // 异常情况下也返回 null，表示页面不可用
        }
    }

    /**
     * 创建空的页面配置
     * 注意：当前未使用，但保留以备将来需要
     */
    @SuppressWarnings("unused")
    private SitePageConfig createEmptyPageConfig(SitePageInfoEntity pageInfo) {
        SitePageConfig config = new SitePageConfig();
        config.setId(null);
        config.setPageId(pageInfo.getPageId());
        config.setPageName(pageInfo.getPageName());
        config.setConfig("{}"); // 空的JSON配置
        config.setVersion(pageInfo.getVersion());
        config.setStrategyState(pageInfo.getStrategyState()); // 传递人群策略状态
        config.setCreateTime(new Date());
        config.setUpdateTime(new Date());
        return config;
    }

    @Override
    public List<SiteExpConfig> loadExperiments() {
        return expConfigEngine.getActiveExpConfigList()
                .stream()
                .map(entity -> {
                    SiteExpConfig config = new SiteExpConfig();
                    config.setId(entity.getId());
                    config.setPageId(entity.getPageId());
                    config.setName(entity.getName());
                    config.setConditionType(entity.getConditionType());
                    config.setConditionRule(entity.getConditionRule());
                    config.setStartTime(entity.getStartTime());
                    config.setEndTime(entity.getEndTime());
                    config.setVersion(entity.getVersion());
                    config.setStatus(entity.getStatus());
                    config.setCreateTime(entity.getCreateTime());
                    config.setUpdateTime(entity.getUpdateTime());
                    return config;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<SiteExpBuckets> loadExperimentBuckets() {
        return expBucketEngine.list()
                .stream()
                .map(entity -> {
                    SiteExpBuckets buckets = new SiteExpBuckets();
                    buckets.setId(entity.getId());
                    buckets.setPageId(entity.getPageId());
                    buckets.setExpId(entity.getExpId());
                    buckets.setBucketId(entity.getBucketId());
                    buckets.setRatio(entity.getRatio());
                    buckets.setConfig(entity.getConfig());
                    buckets.setStatus(entity.getStatus());
                    buckets.setCreateTime(entity.getCreateTime());
                    buckets.setUpdateTime(entity.getUpdateTime());
                    return buckets;
                })
                .collect(Collectors.toList());
    }

}
