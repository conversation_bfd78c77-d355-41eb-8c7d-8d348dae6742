package com.xiaomi.micar.site.metrics;

import com.google.common.collect.Maps;
import com.mi.car.iccc.starter.metrics.util.Metrics;
import org.apache.commons.lang.StringUtils;

import java.time.Duration;
import java.util.Map;

import static com.xiaomi.micar.site.metrics.aop.SiteMetrics.TYPE_ACTION;
import static com.xiaomi.micar.site.metrics.aop.SiteMetrics.TYPE_PAGE;
import static com.xiaomi.micar.site.metrics.aop.SiteMetrics.TYPE_SITE;

/**
 * 站点组件监控指标
 *
 * <AUTHOR>
 * @since 2025/04/23
 */
public class SiteServerMetrics {

    public static final String METRICS_SITE_REQUESTS_DURATION = "iccc.site.requests.duration";
    public static final String METRICS_SITE_REQUESTS_TOTAL = "iccc.site.requests.total";

    public static void recordSite(Duration duration, String status) {
        record(TYPE_SITE, null, null, duration, status, null);
    }

    public static void recordPage(String pageId, Duration duration, String status) {
        record(TYPE_PAGE, pageId, null, duration, status, null);
    }

    public static void recordAction(String pageId, String action, Duration duration, String status) {
        record(TYPE_ACTION, pageId, action, duration, status, null);
    }

    private static void record(String type, String pageId, String action, Duration duration, String status, Map<String, String> extraTags) {
        Map<String, String> tags = extraTags == null ? Maps.newHashMap() : Maps.newHashMap(extraTags);
        tags.put("type", type);
        if (StringUtils.isNotBlank(pageId)) {
            tags.put("pageId", pageId);
        } else {
            tags.put("pageId", "-");
        }
        if (StringUtils.isNotBlank(action)) {
            tags.put("action", action);
        } else {
            tags.put("action", "-");
        }
        Metrics.timer(METRICS_SITE_REQUESTS_DURATION, tags, duration);

        if (StringUtils.isNotBlank(status)) {
            tags.put("error", status);
        } else {
            tags.put("error", "ok");
        }
        Metrics.counter(METRICS_SITE_REQUESTS_TOTAL, tags);

    }
}
