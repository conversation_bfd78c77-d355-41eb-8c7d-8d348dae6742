package com.xiaomi.micar.site.model.response.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义反序列化器，处理Community API返回的data字段
 * 当API返回空字符串""时，转换为空列表
 * 
 * <AUTHOR>
 * @since 2025/06/28
 */
public class PostIdsDataDeserializer extends JsonDeserializer<List<String>> {
    
    @Override
    public List<String> deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        JsonToken token = parser.getCurrentToken();
        
        if (token == JsonToken.VALUE_STRING) {
            String value = parser.getValueAsString();
            // 如果是空字符串，返回空列表
            if (value == null || value.trim().isEmpty()) {
                return new ArrayList<>();
            }
            // 如果是非空字符串，这可能是个错误，但我们仍返回空列表
            return new ArrayList<>();
        }
        
        if (token == JsonToken.START_ARRAY) {
            // 正常的数组，使用默认反序列化
            List<String> result = new ArrayList<>();
            while (parser.nextToken() != JsonToken.END_ARRAY) {
                if (parser.getCurrentToken() == JsonToken.VALUE_STRING) {
                    result.add(parser.getValueAsString());
                }
            }
            return result;
        }
        
        if (token == JsonToken.VALUE_NULL) {
            return new ArrayList<>();
        }
        
        // 其他情况返回空列表
        return new ArrayList<>();
    }
}