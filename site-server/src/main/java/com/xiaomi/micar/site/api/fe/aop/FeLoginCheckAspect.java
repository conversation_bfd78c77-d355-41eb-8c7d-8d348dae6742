package com.xiaomi.micar.site.api.fe.aop;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

import static com.xiaomi.micar.site.api.fe.GatewayConstants.PARAM_UID;


/**
 * 用户检查切面
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Component
@Aspect
@Slf4j
public class FeLoginCheckAspect {

    @Around(value = "@annotation(loginCheck)")
    public Object doAround(final ProceedingJoinPoint joinPoint, FeLoginCheck loginCheck) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object argument = args[0];

        Field field = ReflectionUtils.findField(argument.getClass(), loginCheck.field());
        if (field == null) {
            throw new RuntimeException("[FeLoginCheck]unknow field : " + loginCheck.field());
        }

        String uid = RpcContext.getContext().getAttachment(PARAM_UID);
        if (loginCheck.required()) {
            Assert.isTrue(StringUtils.isNotBlank(uid), "用户未登录");
        }

        field.setAccessible(true);
        field.set(argument, StringUtils.isEmpty(uid) ? null : uid);

        return joinPoint.proceed();

    }
}
