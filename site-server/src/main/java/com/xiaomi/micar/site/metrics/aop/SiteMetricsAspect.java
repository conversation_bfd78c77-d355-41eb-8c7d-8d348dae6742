package com.xiaomi.micar.site.metrics.aop;

import com.mi.car.iccc.iccccommonutil.util.IcccCommonDateUtil;
import com.mi.car.iccc.iccccommonutil.util.SpelUtil;
import com.xiaomi.micar.site.metrics.SiteServerMetrics;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
@Aspect
@Slf4j
public class SiteMetricsAspect {

    @Around(value = "@annotation(siteMetrics)")
    public Object doAround(final ProceedingJoinPoint joinPoint, SiteMetrics siteMetrics) throws Throwable {
        long startTime = IcccCommonDateUtil.getCurrentTimeMillis();
        Throwable throwable = null;

        try {
            return joinPoint.proceed();
        } catch (Throwable e) {
            throwable = e;
            log.error("SiteMetricsAspect error", e);
            throw e;
        } finally {
            Duration duration = Duration.ofMillis(IcccCommonDateUtil.getCurrentTimeMillis() - startTime);
            String status = throwable == null ? null : throwable.getClass().getSimpleName();

            if (SiteMetrics.TYPE_SITE.equals(siteMetrics.type())) {
                SiteServerMetrics.recordSite(duration, status);
            } else if (SiteMetrics.TYPE_PAGE.equals(siteMetrics.type())) {
                String pageId = SpelUtil.generateKeyBySpEL(siteMetrics.pageId(), joinPoint);
                SiteServerMetrics.recordPage(pageId, duration, status);
            } else if (SiteMetrics.TYPE_ACTION.equals(siteMetrics.type())) {
                SiteServerMetrics.recordAction(siteMetrics.pageId(), siteMetrics.action(), duration, status);
            }
        }
    }

}
