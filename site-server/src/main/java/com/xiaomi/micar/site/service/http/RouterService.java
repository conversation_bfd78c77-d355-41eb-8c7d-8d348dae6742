package com.xiaomi.micar.site.service.http;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 路由工具
 *
 * <AUTHOR>
 * @since 2025/03/26
 */
public class RouterService {

    private static final Pattern POST_ID_PATTERN = Pattern.compile("[?&]postId=([^&]+)");
    private static final Pattern ACT_ID_PATTERN = Pattern.compile("[?&]actId=([^&]+)");

    @Value("${iccc.site.router.community.domain:}")
    private String communityDomain;


    @SneakyThrows
    @SuppressWarnings("MagicNumber")
    public String getCommunityCommentUrl(String postId, boolean isVideo
            , String expId, String source) {
        return getCommunityPostUrl(postId, isVideo, false, expId, source);
    }

    public String getCommunityCommentUrl(String postId, boolean isVideo,boolean isComment
            , String expId, String source) {
        return getCommunityPostUrl(postId, isVideo, isComment, expId, source);
    }


    @SneakyThrows
    @SuppressWarnings("MagicNumber")
    public String getCommunityPostUrl(String postId, boolean isVideo
            , String expId, String source) {
        return getCommunityPostUrl(postId, isVideo, false, expId, source);
    }

    @SneakyThrows
    @SuppressWarnings("MagicNumber")
    public String getCommunityPostUrl(String postId, boolean isVideo, boolean isComment
            , String expId, String source) {
        StringBuilder webUrl = new StringBuilder();
        webUrl.append(communityDomain);
        webUrl.append(isVideo ? "/videoDetail" : "/detail");
        webUrl.append("?postId=").append(postId);
        if (isComment) {
            webUrl.append("&isComment=1");
        }
        if (StringUtils.isNotEmpty(expId)) {
            webUrl.append("&expId=").append(expId);
        }
        if (StringUtils.isNotEmpty(source)) {
            webUrl.append("&source=").append(source);
        }
        String url = getCommunityFullStackUrl(webUrl.toString());
        if (isVideo) {
            url += "&transition_background_color=%23000000";
        }
        return url;
    }

    public String getCommunityTopicUrl(String topicId) {
        return getCommunityFullStackUrl(communityDomain + "/topic?topicId=" + topicId);
    }


    public String getCommunityUserUrl(String eUserId) {
        return getCommunityFullStackUrl(communityDomain + "/user?eUserId=" + eUserId);
    }

    @SneakyThrows
    public String getCommunityFullStackUrl(String webUrl) {
        if (StringUtils.isEmpty(webUrl)) {
            return webUrl;
        }
        return "https://www.xiaomiev.com/communityWeb?_rt=ft&url=" + URLEncoder.encode(webUrl, "UTF-8");
    }

    @SneakyThrows
    public String getCommunityPostFullStackUrl(String postId, boolean isVideo) {
        if (StringUtils.isEmpty(postId)) {
            return postId;
        }
        String webUrl = getCommunityPostUrl(postId, isVideo, false, null, "explore");
        return "https://www.xiaomiev.com/communityWeb?_rt=ft&url=" + URLEncoder.encode(webUrl, "UTF-8");
    }

    /**
     * 获取帖子ID
     *
     * @param webUrl
     * @return
     */
    public String getPostIdFromWebUrl(String webUrl) {
        if (StringUtils.isEmpty(webUrl)) {
            return null;
        }
        if (!webUrl.contains("community.car.miui.com")) {
            return null;
        }
        //提取postId  https://web.community.car.miui.com/videoDetail?postId=295511092120012280&asdasd=adsa
        Matcher matcher = POST_ID_PATTERN.matcher(webUrl);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 获取活动ID
     *
     * @param webUrl
     * @return
     */
    public String getActIdFromWebUrl(String webUrl) {
        if (StringUtils.isEmpty(webUrl)) {
            return null;
        }
        if (!webUrl.contains("community.car.miui.com")) {
            return null;
        }
        //提取actId https://web.community.car.miui.com/activity/universal?actId=469362269&siteChannel=1&skipLocal=true
        Matcher matcher = ACT_ID_PATTERN.matcher(webUrl);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
