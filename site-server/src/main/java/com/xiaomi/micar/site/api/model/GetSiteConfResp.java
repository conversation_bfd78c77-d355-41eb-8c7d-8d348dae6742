package com.xiaomi.micar.site.api.model;

import com.xiaomi.micar.site.component.TabsComponent;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 站点配置响应
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
public class GetSiteConfResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户分组策略
     */
    private String strategy;

    /**
     * 站点tabbar配置
     */
    private TabsComponent<Void> tabBar;

    /**
     * 扩展字段
     */
    private Map<String, Object> extend;

}