package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.component.LoadMoreInfo;
import com.xiaomi.micar.site.component.NewsComponent;
import com.xiaomi.micar.site.component.element.ArticleElement;

import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.model.PostDetailWithAuthorResponse;
import com.xiaomi.micar.site.model.response.PostIdsResponse;
import com.xiaomi.micar.site.service.CommunityPostCacheService;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import com.xiaomi.micar.site.service.http.RouterService;
import com.xiaomi.micar.site.convert.CommunityDataConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;

/**
 * News组件数据提供者
 */
@Slf4j
@org.springframework.stereotype.Component("newsProvider")
public class NewsProvider extends AbstractComponentDataProvider {

    /**
     * API成功响应码
     */
    private static final int SUCCESS_CODE = 200;

    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    @Resource
    private CommunityPostCacheService communityPostCacheService;

    @Resource
    private RouterService routerService;

    /**
     * News数据类型枚举
     */
    private enum NewsDataType {
        POST_ID("postId", "帖子ID"),
        TAG("tag", "标签"),
        AUTHOR("author", "作者");

        private final String code;
        private final String description;

        NewsDataType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static NewsDataType fromCode(String code) {
            if (code == null) {
                return null;
            }
            for (NewsDataType type : NewsDataType.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    @Data
    private static class ArticleResult {
        private final List<ArticleElement> articles;
        private final LoadMoreInfo loadMore;
    }

    @Override
    protected boolean validateComponentType(Component componentInfo) {
        return componentInfo instanceof NewsComponent;
    }

    @Override
    protected void doProcess(ResponseContext context, Component componentInfo) {
        NewsComponent news = (NewsComponent) componentInfo;
        processNews(news, context, componentInfo);
    }

    private void processNews(NewsComponent news, ResponseContext context, Component component) {
        try {
            Map<String, Object> params = context.getQueryParams();
            if (params == null) {
                news.setData(Collections.emptyList());
                return;
            }

            NewsParams newsParams = parseParams(params);
            List<String> postIds = getPostIds(newsParams.type, newsParams.ids, newsParams.limit);
            if (CollectionUtils.isEmpty(postIds)) {
                log.warn("获取帖子ID失败: type={}, ids={}", newsParams.type, newsParams.ids);
                news.setData(Collections.emptyList());
                return;
            }

            ArticleResult result = getArticlesWithLoadMore(postIds, newsParams, context, component);
            news.setData(result.getArticles());
            news.setLoadMore(result.getLoadMore());

        } catch (Exception e) {
            log.error("处理News组件异常", e);
            news.setData(Collections.emptyList());
        }
    }

    private static class NewsParams {
        final String type;
        final List<String> ids;
        final int limit;
        final int pageSize;
        final boolean loadMore;
        final int offset;

        NewsParams(String type, List<String> ids, Integer limit, Integer pageSize, Boolean loadMore, Integer offset) {
            this.type = type;
            this.ids = ids;
            this.limit = limit != null ? limit : 10;
            this.pageSize = pageSize != null ? pageSize : (limit != null ? limit : 10);
            this.loadMore = loadMore != null && loadMore;
            this.offset = offset != null ? offset : 0;
        }
    }

    private NewsParams parseParams(Map<String, Object> params) {
        String type = (String) params.get("type");
        @SuppressWarnings("unchecked")
        List<String> ids = (List<String>) params.get("ids");
        Integer limit = (Integer) params.get("limit");
        Integer pageSize = (Integer) params.get("pageSize");
        Boolean loadMore = (Boolean) params.get("loadMore");
        Integer offset = (Integer) params.get("offset");

        return new NewsParams(type, ids, limit, pageSize, loadMore, offset);
    }

    private List<String> getPostIds(String type, List<String> ids, Integer limit) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        try {
            NewsDataType dataType = NewsDataType.fromCode(type);
            if (dataType == null) {
                log.warn("不支持的type类型: {}", type);
                return Collections.emptyList();
            }

            // 如果type是postId，直接返回ids，无需调用API查询
            if (dataType == NewsDataType.POST_ID) {
                log.debug("type为{}，直接返回帖子ID列表: {}", dataType.getDescription(), ids);
                return new ArrayList<>(ids);
            }

            String operaTags = "";
            String userIds = "";

            if (dataType == NewsDataType.TAG) {
                operaTags = String.join(",", ids);
            } else if (dataType == NewsDataType.AUTHOR) {
                userIds = String.join(",", ids);
            }

            PostIdsResponse response = communityApiFeignClient.getPostIds(operaTags, userIds, limit);

            if (response == null || response.getCode() != SUCCESS_CODE || CollectionUtils.isEmpty(response.getData())) {
                log.warn("获取帖子ID失败: type={}, response={}", type, response);
                return Collections.emptyList();
            }

            return response.getData();

        } catch (Exception e) {
            log.error("获取帖子ID异常: type={}, ids={}", type, ids, e);
            return Collections.emptyList();
        }
    }

    private ArticleResult getArticlesWithLoadMore(List<String> postIds, NewsParams params, ResponseContext context, Component component) {
        try {
            // 统一的文章获取逻辑：都只获取第一页详情
            List<ArticleElement> articles = getArticlesWithOptionalCaching(postIds, params, context, component);

            LoadMoreInfo loadMoreInfo = buildLoadMoreInfo(articles, params, context);
            return new ArticleResult(articles, loadMoreInfo);

        } catch (Exception e) {
            log.error("获取文章异常", e);
            return new ArticleResult(Collections.emptyList(), null);
        }
    }

    /**
     * 获取文章详情（统一逻辑）
     * - 如果需要loadMore，则缓存所有帖子ID用于后续分页
     * - 无论哪种情况，都只获取第一页的文章详情
     */
    private List<ArticleElement> getArticlesWithOptionalCaching(List<String> postIds, NewsParams params, ResponseContext context, Component component) {
        // 1. 如果需要loadMore功能，缓存完整的帖子ID列表
        if (params.loadMore) {
            cachePostIdList(postIds, context, component);
            log.info("缓存了{}个帖子ID用于后续分页查询", postIds.size());
        }

        // 2. 统一只获取第一页的文章详情（优化性能）
        int firstPageSize = Math.min(params.pageSize, postIds.size());
        List<String> firstPagePostIds = postIds.subList(0, firstPageSize);
        List<ArticleElement> firstPageArticles = getArticlesDirectlyFromAPI(firstPagePostIds);

        log.info("获取第一页{}个文章详情", firstPageArticles.size());

        return firstPageArticles;
    }


    /**
     * 缓存帖子ID列表（新缓存策略）
     * 只缓存帖子ID，不缓存详情，减少内存占用
     */
    private void cachePostIdList(List<String> postIds, ResponseContext context, Component component) {
        try {
            if (CollectionUtils.isEmpty(postIds)) {
                return;
            }

            String cacheKey = buildCacheKeyWithoutSnapshot(context, component);

            communityPostCacheService.cachePostIdList(cacheKey, postIds);

            log.debug("缓存帖子ID列表成功: cacheKey={}, size={}", cacheKey, postIds.size());
        } catch (Exception e) {
            log.error("缓存帖子ID列表异常", e);
        }
    }

    /**
     * 构建不包含 dataSnapshot 的缓存键
     */
    private String buildCacheKeyWithoutSnapshot(ResponseContext context, Component component) {
        String pageId = context.getPageId();

        String moduleId = getModuleIdFromParams(context);
        if (StringUtils.isBlank(moduleId)) {
            moduleId = component != null && StringUtils.isNotBlank(component.getId())
                    ? component.getId() : "default";
        }

        String tabId = getTabIdFromParams(context);

        if (StringUtils.isNotBlank(tabId)) {
            return String.format("pagination:%s:%s:%s:news", pageId, moduleId, tabId);
        } else {
            return String.format("pagination:%s:%s:news", pageId, moduleId);
        }
    }

    private LoadMoreInfo buildLoadMoreInfo(List<ArticleElement> articles, NewsParams params, ResponseContext context) {
        if (!params.loadMore) {
            return null;
        }

        int currentOffset = params.offset;
        int currentPageSize = articles.size();
        boolean hasMore = currentPageSize >= params.pageSize && (currentOffset + params.pageSize) < params.limit;

        if (hasMore) {
            String dataSnapshot = getDataSnapshotFromContext(context);
            return LoadMoreInfo.enabled(dataSnapshot);
        } else {
            return LoadMoreInfo.disabled();
        }
    }

    private String getDataSnapshotFromContext(ResponseContext context) {
        Map<String, Object> queryParams = context.getQueryParams();
        if (queryParams != null) {
            return (String) queryParams.get("dataSnapshot");
        }
        return null;
    }

    private String getTabIdFromParams(ResponseContext context) {
        Map<String, Object> params = context.getQueryParams();
        if (params != null) {
            return (String) params.get("tabId");
        }
        return null;
    }

    private String getModuleIdFromParams(ResponseContext context) {
        Map<String, Object> params = context.getQueryParams();
        if (params != null) {
            return (String) params.get("moduleId");
        }
        return null;
    }

    /**
     * 直接从新API获取文章列表（不经过PostDetail转换）
     */
    private List<ArticleElement> getArticlesDirectlyFromAPI(List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }

        final int BATCH_SIZE = 20;
        List<List<String>> batches = Lists.partition(postIds, BATCH_SIZE);
        List<ArticleElement> allArticles = new ArrayList<>();

        for (List<String> batch : batches) {
            try {
                String postIdsCsv = String.join(",", batch);
                log.info("使用新接口 detailWithAuthor 获取帖子详情: {}", postIdsCsv);

                // 使用新接口获取帖子详情
                PostDetailWithAuthorResponse response = communityApiFeignClient.getPostDetailWithAuthor(postIdsCsv);

                if (response != null && response.getCode() == 200 && !CollectionUtils.isEmpty(response.getData())) {
                    // 直接将 OfficeNewsRecord 转换为 ArticleElement，传入 RouterService 以生成跳转链接
                    List<ArticleElement> convertedArticles = CommunityDataConverter.convertFromOfficeNewsRecords(response.getData(), routerService);
                    allArticles.addAll(convertedArticles);
                    log.info("成功获取并转换{}个文章", convertedArticles.size());
                } else {
                    log.warn("获取帖子详情失败，响应: {}", response);
                }
            } catch (Exception e) {
                log.error("获取帖子详情异常: batch={}", batch, e);
            }
        }

        return allArticles;
    }

    @Override
    protected void handleProcessingError(Component componentInfo, Exception e) {
        log.error("NewsProvider处理失败", e);

        if (componentInfo instanceof NewsComponent) {
            ((NewsComponent) componentInfo).setData(Collections.emptyList());
        }
    }
}
