package com.xiaomi.micar.site.model;

import lombok.Data;

import java.util.List;

/**
 * 活动列表响应模型
 */
@Data
public class ActivityResponse {

    /**
     * 筛选条件
     */
    private List<Filter> filters;

    /**
     * 活动数据
     */
    private ActivityData data;

    /**
     * 筛选条件
     */
    @Data
    public static class Filter {
        /**
         * 筛选名称
         */
        private String name;

        /**
         * 筛选键
         */
        private String key;

        /**
         * 筛选值列表
         */
        private List<FilterValue> values;
    }

    /**
     * 筛选值
     */
    @Data
    public static class FilterValue {
        /**
         * 筛选值名称
         */
        private String name;

        /**
         * 筛选值
         */
        private String value;

        /**
         * 是否选中
         */
        private Boolean selected;
    }

    /**
     * 活动数据
     */
    @Data
    public static class ActivityData {
        /**
         * 总数
         */
        private String total;

        /**
         * 是否有更多页
         */
        private Boolean hasMorePage;

        /**
         * 每页大小
         */
        private String limit;

        /**
         * 下一页起点
         */
        private String after;

        /**
         * 活动记录列表
         */
        private List<ActivityRecord> records;
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActivityRecord {
        /**
         * 活动ID
         */
        private String id;

        /**
         * 是否付费
         */
        private Boolean isPay;

        /**
         * 活动标题
         */
        private String title;

        /**
         * 活动副标题
         */
        private String subTitle;

        /**
         * 活动封面图
         */
        private String cover;

        /**
         * 折叠屏封面图
         */
        private String coverFold;

        /**
         * 城市
         */
        private String city;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;

        /**
         * 标签列表
         */
        private List<Tag> tags;

        /**
         * 活动状态
         */
        private Integer status;

        /**
         * 费用信息
         */
        private Fee fee;

        /**
         * 操作信息
         */
        private Action action;
    }

    /**
     * 标签
     */
    @Data
    public static class Tag {
        /**
         * 标签名称
         */
        private String name;

        /**
         * 标签代码
         */
        private String code;
    }

    /**
     * 费用信息
     */
    @Data
    public static class Fee {
        /**
         * 价格信息
         */
        private Price price;

        /**
         * 积分信息
         */
        private Point point;
    }

    /**
     * 价格信息
     */
    @Data
    public static class Price {
        /**
         * 市场价格
         */
        private String marketPrice;
    }

    /**
     * 积分信息
     */
    @Data
    public static class Point {
        /**
         * 可抵扣金额
         */
        private String deductAmountPrice;

        /**
         * 可抵扣积分
         */
        private Integer deductAmount;
    }

    /**
     * 操作信息
     */
    @Data
    public static class Action {
        /**
         * 操作类型
         */
        private String type;

        /**
         * 操作文本
         */
        private String text;

        /**
         * 链接URL
         */
        private String linkUrl;

        /**
         * 提示文本
         */
        private String toast;
    }
} 
