package com.xiaomi.micar.site.service.assembly.provider;


import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;

import java.util.List;

/**
 * 站点数据提供者接口
 * 负责提供站点页面配置、实验、实验桶和用户组数据
 * 使用者可以实现此接口来自定义数据获取逻辑
 */
public interface SiteDataProvider {

    /**
     * 获取所有页面配置
     *
     * @return 页面配置列表
     */
    List<SitePageConfig> getAllPageConfigs();

    /**
     * 获取所有实验
     *
     * @return 实验列表
     */
    List<SiteExpConfig> getAllExperiments();

    /**
     * 获取所有实验桶
     *
     * @return 实验桶列表
     */
    List<SiteExpBuckets> getAllExperimentBuckets();
}
