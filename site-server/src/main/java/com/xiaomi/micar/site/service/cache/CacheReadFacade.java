package com.xiaomi.micar.site.service.cache;

import com.xiaomi.micar.site.cache.SitePageCacheService;
import com.xiaomi.micar.site.cache.policy.CachePolicy;
import com.xiaomi.micar.site.cache.policy.ReadPlan;
import com.xiaomi.micar.site.cache.util.CacheKeyUtil;
import com.xiaomi.micar.site.config.CachePolicyProperties;
import com.xiaomi.micar.site.config.PagePolicyRule;
import com.xiaomi.micar.site.redis.SiteRedisKey;
import com.xiaomi.micar.site.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 按策略读取页面快照（内存 / Redis）
 */
@Slf4j
@Service
public class CacheReadFacade {

    @Resource
    private SitePageCacheService sitePageCacheService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CachePolicyResolver cachePolicyResolver;


    public String get(String pageId, String cand, String combinationKey) {
        CachePolicy policy = cachePolicyResolver.resolve(pageId, cand);
        ReadPlan.Mode mode = policy.getReadPlan().getMode();
        switch (mode) {
            case MEMORY_ONLY:
                return sitePageCacheService.getCombinationResponse(pageId, combinationKey);
            case REDIS_ONLY:
                return redisUtil.hashGet(SiteRedisKey.SITE_PAGE_CACHE_HASH, combinationKey, pageId);
            case MYSQL_ONLY:
                // 读 MySQL 未实现（当前无需）
                log.warn("[CacheReadFacade] MYSQL_ONLY 模式未实现: pageId={}, key={}", pageId, combinationKey);
                return null;
            default:
                return null;
        }
    }


}

