package com.xiaomi.micar.site.api.debug;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.api.fe.FeSiteService;
import com.xiaomi.micar.site.api.model.FeedsLoadReq;
import com.xiaomi.micar.site.model.PostDetailResponse;
import com.xiaomi.micar.site.model.PostDetailWithAuthorResponse;
import com.xiaomi.micar.site.model.RnVersionRequest;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import com.xiaomi.micar.site.service.CommunityPostCacheService;
import com.xiaomi.micar.site.service.FeedsLoadService;
import com.xiaomi.micar.site.service.PagePreviewService;
import com.xiaomi.micar.site.service.assembly.HistoricalSnapshotPageAssemblyService;
import com.xiaomi.micar.site.service.assembly.PageCachePreloadService;
import com.xiaomi.micar.site.service.assembly.SitePageAssemblyService;
import com.xiaomi.micar.site.up.service.UpServiceManager;
import com.xiaomi.micar.site.api.model.GetSiteConfResp;
import com.xiaomi.youpin.infra.rpc.Result;
import static com.xiaomi.micar.site.api.fe.GatewayConstants.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;



@Slf4j
@RestController
@RequestMapping("/debug")
public class DebugController {

    @Resource
    private SitePageAssemblyService sitePageAssemblyService;
    @Resource
    private CommunityPostCacheService communityPostCacheService;
    @Resource
    private UpServiceManager upServiceManager;
    @Resource
    private PagePreviewService pagePreviewService;
    @Resource
    private PageCachePreloadService pageCachePreloadService;
    @Resource
    private FeedsLoadService feedsLoadService;

    @Resource
    private HistoricalSnapshotPageAssemblyService historicalSnapshotPageAssemblyService;
    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    @Resource
    private FeSiteService feSiteService;

    
    @GetMapping("/page/data")
    public String pageData(@RequestParam String pageId,
                           @RequestParam String userId,
                           @RequestParam(required = false) String rnVersion) {
        // 创建 RnVersionRequest 对象
        RnVersionRequest enhancedRequest = createPageDataV2Request(pageId, userId, rnVersion);

        // 调用 getPageDataV2Enhanced 并返回结果
        return historicalSnapshotPageAssemblyService.getPageDataV2Enhanced(enhancedRequest, 2, userId);
    }

    @GetMapping("/system/cache/refresh")
    public String refreshCache() {
        pageCachePreloadService.preloadAllPageCombinations();
        return "ok";
    }

    @GetMapping("/system/cache/visualize")
    public String visualizeCacheState() {
        return sitePageAssemblyService.visualizeCacheState();
    }

    /**
     * 测试社区帖子缓存功能
     *
     * @param postIds 帖子ID，多个用逗号分隔
     * @return 帖子详情
     */
    @GetMapping("/community/post/detail")
    public String getPostDetail(@RequestParam String postIds) {
        // 将逗号分隔的帖子ID转换为列表
        List<String> postIdList = Arrays.asList(postIds.split(","));

        // 从缓存中获取帖子详情
        List<PostDetailResponse.PostDetail> cachedList = communityPostCacheService.getPostDetails(postIdList);

        // 构造响应
        return JsonUtil.toJSONString(cachedList);
    }

    /**
     * 强制刷新帖子缓存
     * 使用缓存后置处理器获取帖子详情并缓存
     *
     * @param postIds 帖子ID，多个用逗号分隔
     * @return 刷新结果
     */
    @GetMapping("/community/post/cache")
    public String refreshPostCache(@RequestParam String postIds) {
        // 将逗号分隔的帖子ID转换为集合
        Set<String> postIdSet = new HashSet<>(Arrays.asList(postIds.split(",")));

        // 使用缓存后置处理器获取帖子详情并缓存
        List<PostDetailResponse.PostDetail> details = communityPostCacheService.fetchAndCachePostDetails(postIdSet);

        return JsonUtil.toJSONString(details);
    }

    /**
     * 测试获取帖子详情（带作者信息）
     *
     * @param postIds 帖子ID，多个用逗号分隔，最大数量20
     * @return 帖子详情（包含作者信息）
     */
    @GetMapping("/community/post/detail-with-author")
    public String getPostDetailWithAuthor(@RequestParam String postIds) {
        try {
            // 直接调用 Feign 客户端
            PostDetailWithAuthorResponse response = communityApiFeignClient.getPostDetailWithAuthor(postIds);

            // 构造调试信息
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("requestPostIds", postIds);
            debugInfo.put("responseCode", response != null ? response.getCode() : null);
            debugInfo.put("responseMessage", response != null ? response.getMessage() : null);
            debugInfo.put("dataSize", response != null && response.getData() != null ? response.getData().size() : 0);
            debugInfo.put("response", response);

            return JsonUtil.toJSONString(debugInfo);
        } catch (Exception e) {
            // 错误处理
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("requestPostIds", postIds);
            errorInfo.put("error", e.getMessage());
            errorInfo.put("errorType", e.getClass().getSimpleName());

            return JsonUtil.toJSONString(errorInfo);
        }
    }

    @GetMapping("/up/local/reload")
    public String reloadLocalUpData() {
        upServiceManager.getUpService(UpServiceManager.LOCAL_UP_SERVICE).loadAll();
        return "ok";
    }

    /**
     * 测试分页加载功能
     * 测试 Redis 写入和缓存 key 的正确性
     */
    @PostMapping("/feeds/load")
    public String testFeedsLoad(@RequestBody FeedsLoadReq request) {
        return feedsLoadService.loadFeeds(request);
    }

    /**
     * 检查分页缓存是否存在
     */
    @GetMapping("/pagination/cache/check")
    public String checkPaginationCache(@RequestParam String cacheKey) {
        boolean exists = communityPostCacheService.hasPaginationCache(cacheKey);
        Map<String, Object> result = new HashMap<>();
        result.put("cacheKey", cacheKey);
        result.put("exists", exists);
        return JsonUtil.toJSONString(result);
    }

    /**
     * 测试分页缓存的读取
     */
    @GetMapping("/pagination/cache/get")
    public String getPaginationCache(@RequestParam String cacheKey,
                                     @RequestParam(defaultValue = "0") int offset,
                                     @RequestParam(defaultValue = "10") int limit) {
        List<String> data = communityPostCacheService.getPaginationArticleJsonList(cacheKey, offset, limit);
        Map<String, Object> result = new HashMap<>();
        result.put("cacheKey", cacheKey);
        result.put("offset", offset);
        result.put("limit", limit);
        result.put("dataSize", data.size());
        result.put("data", data);
        return JsonUtil.toJSONString(result);
    }


    /**
     * 检查指定用户的分组策略
     *
     * @param strategy 上行服务策略名称，默认值为"composite"
     * @param mid      用户mid
     * @return 返回检查结果，具体类型由upServiceManager实现决定
     */
    @GetMapping("/up/strategy")
    public Object upStrategy(@RequestParam(defaultValue = "composite") String strategy, @RequestParam Long mid) {
        return upServiceManager.getUpService(strategy).strategy(mid).getCode();
    }

    /**
     * 创建 RnVersionRequest 对象
     */
    private RnVersionRequest createPageDataV2Request(String pageId, String userId, String rnVersion) {
        RnVersionRequest enhancedRequest = new RnVersionRequest();
        enhancedRequest.setPageId(pageId);
        enhancedRequest.setMid(userId);
        enhancedRequest.setRnVersion(rnVersion);

        // 设置空的查询参数
        enhancedRequest.setQueryParams(new HashMap<>());

        return enhancedRequest;
    }
    @GetMapping("/getSiteConfig")
    public void TestTabAdjust(){
        //手动设置好Attachment
        RpcContext.getContext().setAttachment(PARAM_UID,"227852026");
        RpcContext.getContext().setAttachment(PARAM_MOBILE_ID,"17702978452");
        RpcContext.getContext().setAttachment(PARAM_VERSION,"2.0");

        Result<GetSiteConfResp> result = feSiteService.getSiteConfig(new HashMap<>());
        for (int i = 0; i < 10; i++) {
            log.info("-------result结果：{}",result.getData());
        }
    }
}
