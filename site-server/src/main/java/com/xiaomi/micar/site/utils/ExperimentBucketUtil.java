// CHECKSTYLE:OFF
package com.xiaomi.micar.site.utils;

import com.xiaomi.micar.site.model.PageConfigModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 实验桶工具类.
 *
 * <p>用于处理实验桶的计算和用户哈希定位。</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public final class ExperimentBucketUtil {

    /**
     * 桶总量，默认为1000.
     */
    public static final int BUCKET_VOLUME = 1000;

    /**
     * 私有构造函数，防止实例化.
     */
    private ExperimentBucketUtil() {
        // 工具类不应被实例化
    }

    /**
     * 根据流量比例生成桶ID列表.
     *
     * @param ratio 流量比例
     * @param totalQuota 总配额
     * @param currentBucketStart 当前桶的起始位置
     * @return 桶ID列表
     */
    public static List<String> generateBucketIdsByQuota(
            final String ratio,
            final double totalQuota,
            final int currentBucketStart) {
        List<String> bucketIds = new ArrayList<>();
        if (StringUtils.isBlank(ratio)) {
            return bucketIds;
        }

        try {
            double bucketRatio = Double.parseDouble(ratio);
            int bucketCount = (int) (BUCKET_VOLUME * bucketRatio / totalQuota);

            for (int i = 0; i < bucketCount; i++) {
                int bucketId = currentBucketStart + i;
                if (bucketId < BUCKET_VOLUME) {
                    bucketIds.add(String.valueOf(bucketId));
                }
            }
        } catch (NumberFormatException e) {
            log.error("解析桶比例失败: {}", ratio, e);
        }

        return bucketIds;
    }

    /**
     * 为实验中的所有桶生成桶ID.
     *
     *
     * 注意此方法会做归一化处理。
     *
     * @param bucketModels 桶模型列表
     * @return 是否成功生成桶ID
     */
    public static boolean generateBucketIdsForExperiment(
            final List<PageConfigModel.BucketModel> bucketModels) {
        if (bucketModels == null || bucketModels.isEmpty()) {
            return false;
        }

        // 计算总配额
        double totalQuota = bucketModels.stream()
                .filter(bucket -> StringUtils.isNotBlank(bucket.getRatio()))
                .mapToDouble(bucket -> {
                    try {
                        return Double.parseDouble(bucket.getRatio());
                    } catch (NumberFormatException e) {
                        log.error("解析桶比例失败: {}", bucket.getRatio(), e);
                        return 0.0;
                    }
                })
                .sum();

        if (totalQuota <= 0) {
            log.error("总配额必须大于0");
            return false;
        }

        // 为每个桶生成桶ID
        int currentBucketStart = 0;
        for (PageConfigModel.BucketModel bucketModel : bucketModels) {
            if (StringUtils.isNotBlank(bucketModel.getRatio())) {
                List<String> bucketIds = generateBucketIdsByQuota(
                        bucketModel.getRatio(), totalQuota, currentBucketStart);
                bucketModel.setBucketIds(bucketIds);
                currentBucketStart += bucketIds.size();
            }
        }

        return true;
    }

}
