package com.xiaomi.micar.site.service.remote;

import com.xiaomi.micar.site.component.TabFeedsComponent;
import com.xiaomi.micar.site.component.element.ActionElement;
import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.component.element.ButtonElement;
import com.xiaomi.micar.site.component.element.ImageElement;
import com.xiaomi.micar.site.component.element.VideoElement;
import com.xiaomi.micar.site.component.model.PostTopicInfo;
import com.xiaomi.micar.site.component.model.PublishInfo;
import com.xiaomi.micar.site.component.model.TabFeedsResponse;
import com.xiaomi.micar.site.config.SiteComponentProperties;
import com.xiaomi.micar.site.service.http.RouterService;
import com.xiaomi.micar.site.model.CommunityPostType;
import com.xiaomi.micar.site.model.OfficeNewsResponse;
import com.xiaomi.micar.site.model.PostActionResponse;
import com.xiaomi.micar.site.model.PostDetailResponse;
import com.xiaomi.micar.site.model.PostRelationData;
import com.xiaomi.micar.site.model.PostRelationResponse;
import com.xiaomi.micar.site.utils.ResultHelper;
import com.xiaomi.micar.site.convert.CommunityDataConverter;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 社区数据源远程API实现
 * 负责通过HTTP请求加载社区相关的远程数据
 */
@Slf4j
@Component
public class RemoteCommunityDataSource {
    //internal 发现-探索   https://xiaomi.f.mioffice.cn/docx/doxk4k8dievYYmI8wFRrUIWPxRg
    //internal 发现-活动

    private static final int HTTP_STATUS_OK = 200;
    private static final int HTTP_STATUS_BAD_REQUEST = 400;
    private static final int HTTP_STATUS_SERVER_ERROR = 500;



    /**
     * 每个Feed最多显示的图片数量
     */
    private static final int MAX_IMAGES_PER_FEED = 3;

    // 30分钟

    @Resource
    private RouterService routerService;
    @Resource
    private SiteComponentProperties properties;
    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    public List<ArticleElement> fetchOfficeNews(String userId, Map<String, Object> params) {
        try {
            // 获取API配置
            SiteComponentProperties.ComponentApiConfig config = properties.getApi().getOfficeNews();
            if (!config.isEnabled()) {
                log.warn("OfficeNews API is disabled via configuration");
                return Collections.emptyList();
            }

            // 准备请求参数
            Map<String, Object> requestParams = prepareRequestParams(params, userId);

            // 使用 Feign Client 调用
            log.debug("Calling getOfficeNews API with params: {}", requestParams);
            OfficeNewsResponse response = communityApiFeignClient.getOfficeNews(requestParams);
            log.debug("Received response from getOfficeNews API");

            // 验证响应并转换数据
            if (isSuccessResponse(response)) {
                // 将OfficeNewsResponse转换为List<ArticleComponent>
                return convertToArticleComponents(response);
            } else {
                log.error("Failed to fetch office news based on response code. Response: {}", response);
                return Collections.emptyList();
            }
        } catch (Exception e) { // Catch FeignException or other runtime exceptions
            log.error("Error fetching office news data", e);
            return Collections.emptyList();
        }
    }

    /**
     * 将OfficeNewsResponse转换为List<ArticleComponent>
     *
     * @param response API响应
     * @return 文章组件列表
     */
    private List<ArticleElement> convertToArticleComponents(OfficeNewsResponse response) {
        if (response == null || response.getData() == null) {
            return Collections.emptyList();
        }

        List<OfficeNewsResponse.OfficeNewsRecord> records = Optional.ofNullable(response.getData().getRecords())
                .orElse(Collections.emptyList());

        return records.stream()
                .map(this::createNewsItem)
                .collect(Collectors.toList());
    }

    /**
     * 创建新闻条目
     *
     * @param record 新闻记录
     * @return 新闻条目
     */
    private ArticleElement createNewsItem(OfficeNewsResponse.OfficeNewsRecord record) {
        // 使用统一的转换工具类进行完整转换（包括 action 和 buttons）
        return CommunityDataConverter.convertFromOfficeNewsRecord(record, routerService);
    }




    public TabFeedsComponent.FeedData fetchTabFeeds(String userId, Map<String, Object> params) {
        try {
            // 获取API配置
            SiteComponentProperties.ComponentApiConfig config = properties.getApi().getTabFeeds();
            if (!config.isEnabled()) {
                String errorMsg = "TabFeeds API is disabled via configuration";
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            String tabId = Optional.ofNullable(params.get("tabId"))
                    .map(String::valueOf)
                    .orElseThrow(() -> new RuntimeException("TabId is required"));

            // 使用 Feign Client 调用
            log.debug("Calling getTabFeeds API with params: {}", params);
            TabFeedsResponse response = communityApiFeignClient.getTabFeeds(params);
            log.debug("Received response from getTabFeeds API");

            // 验证响应
            if (isSuccessResponse(response)) {
                return processTabFeedsResponse(response, params);
            } else {
                String errorMsg = String.format("Failed to fetch tab feeds based on response code. TabId: %s, Response: %s", tabId, response);
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
        } catch (Exception e) { // Catch FeignException or other runtime exceptions
            String errorMsg = String.format("Error fetching tab feeds data for params: %s, error: %s", params, e.getMessage());
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 处理TabFeeds响应数据，转换为FeedData对象
     *
     * @param response API响应
     * @param params   请求参数
     * @return FeedData对象
     */
    private TabFeedsComponent.FeedData processTabFeedsResponse(TabFeedsResponse response, Map<String, Object> params) {
        if (response.getData() == null) {
            String errorMsg = "TabFeeds response data is null";
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        TabFeedsComponent.FeedData feedData = new TabFeedsComponent.FeedData();
        feedData.setOffset(getOffset(params));

        // 获取记录列表并转换为FeedItem对象列表
        List<TabFeedsComponent.FeedItem> feedItems = Optional.ofNullable(response.getData().getRecords())
                .orElseThrow(() -> {
                    String errorMsg = "TabFeeds response records is null";
                    log.error(errorMsg);
                    return new RuntimeException(errorMsg);
                })
                .stream()
                .map(this::createFeedItem)
                .collect(Collectors.toList());

        // 设置记录列表
        feedData.setRecords(feedItems);
        feedData.setHasMore(org.apache.commons.lang3.StringUtils.isNoneBlank(response.getData().getAfter()));
        return feedData;
    }

    /**
     * 从API响应记录创建FeedItem对象
     *
     * @param record API响应记录
     * @return FeedItem对象
     */
    private TabFeedsComponent.FeedItem createFeedItem(TabFeedsResponse.FeedRecord record) {
        TabFeedsComponent.FeedItem feedItem = new TabFeedsComponent.FeedItem();

        // 帖子id 设置数据主键 id 为帖子 id，RN用于打点使用
        feedItem.setId(record.getPostId());

        // 设置发布信息
        feedItem.setPublishInfo(createPublishInfo(record));

        // 设置内容摘要
        feedItem.setSummary(extractSummary(record));

        // 处理图片列表
        processImageList(feedItem, record);

        // 设置交互操作
        feedItem.setAction(createAction(record));

        // 设置交互操作
        feedItem.setButtons(createButtons(record));

        // 设置标签列表
        feedItem.setDisplayTags(record.getDisplayTags());

        // 话题
        if (!CollectionUtils.isEmpty(record.getTopicList())) {
            feedItem.setTopicList(record.getTopicList().stream()
                    .map(topic -> new PostTopicInfo(topic.getTopicId(), topic.getTopicName(), routerService.getCommunityTopicUrl(topic.getTopicId())))
                    .collect(Collectors.toList()));
        }

        return feedItem;
    }

    /**
     * 创建发布信息
     *
     * @param record 信息流记录
     * @return 发布信息
     */
    private PublishInfo createPublishInfo(TabFeedsResponse.FeedRecord record) {
        PublishInfo publishInfo = new PublishInfo();

        Optional.ofNullable(record.getAuthor()).ifPresent(author -> {
            publishInfo.setAuthor(author.getUserName());
            publishInfo.setAvatar(author.getIcon());
            publishInfo.setIsEmployee(author.getIsEmployee());
            publishInfo.setEUserId(author.getEUserId());
            publishInfo.setIpRegion(record.getIpRegion());
            if (StringUtils.hasLength(record.getAuthor().getEUserId())) {
                publishInfo.setLinkUrl(routerService.getCommunityUserUrl(record.getAuthor().getEUserId()));
            }
            publishInfo.setHeaderFrame(author.getHeaderFrame());
            publishInfo.setIdentityList(author.getIdentityList());
            publishInfo.setPinBadge(author.getPinBadge());
        });

        // 设置发布时间
        /**
         * @description
         * <60s: 刚刚
         * 60s<=n<1小时: xx分钟前
         * 1小时<=n<24小时: 展示时间，精确到分钟，hh:mm
         * ≥24小时，发帖日期在今年（自然年）1月1日及之后: 展示日期，mm-dd
         * 发表日期早于今年（自然年）1月1日之前的: 展示年份及日期，yyyy-mm-dd
         * @param {*} timestamp 时间戳
         * @returns
         */
        Optional.ofNullable(record.getCreateTime()).ifPresent(createTime -> {
            publishInfo.setTime(formatPublishTime(createTime));
        });

        return publishInfo;
    }

    /**
     * 根据规则格式化发布时间
     * <60s: 刚刚
     * 60s<=n<1小时: xx分钟前
     * 1小时<=n<24小时: 展示时间，精确到分钟，hh:mm
     * ≥24小时，发帖日期在今年（自然年）1月1日及之后: 展示日期，mm-dd
     * 发表日期早于今年（自然年）1月1日之前的: 展示年份及日期，yyyy-mm-dd
     *
     * @param timestamp 时间戳
     * @return 格式化的时间字符串
     */
    @SuppressWarnings("MagicNumber")
    private String formatPublishTime(long timestamp) {
        // 转换时间戳为Instant
        Instant publishInstant = Instant.ofEpochMilli(timestamp);
        Instant currentInstant = Instant.now();

        // 转换为LocalDateTime以便进行日期比较
        LocalDateTime publishDateTime = LocalDateTime.ofInstant(publishInstant, ZoneId.systemDefault());
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentInstant, ZoneId.systemDefault());

        // 计算时间差
        long secondsDiff = ChronoUnit.SECONDS.between(publishInstant, currentInstant);
        long minutesDiff = ChronoUnit.MINUTES.between(publishInstant, currentInstant);
        long hoursDiff = ChronoUnit.HOURS.between(publishInstant, currentInstant);

        // 当前年份的第一天
        LocalDate firstDayOfYear = LocalDate.of(currentDateTime.getYear(), 1, 1);

        // 应用规则
        if (secondsDiff < 60) {
            // 少于60秒，显示"刚刚"
            return "刚刚";
        } else if (minutesDiff < 60) {
            // 60秒至1小时，显示"xx分钟前"
            return minutesDiff + "分钟前";
        } else if (hoursDiff < 24) {
            // 1小时至24小时，显示时间，精确到分钟，hh:mm
            return publishDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
        } else {
            // 大于等于24小时
            // 判断发布日期是否在今年
            if (publishDateTime.toLocalDate().isAfter(firstDayOfYear.minusDays(1))) {
                // 今年（自然年）1月1日及之后: 展示日期，mm-dd
                return publishDateTime.format(DateTimeFormatter.ofPattern("MM-dd"));
            } else {
                // 早于今年（自然年）1月1日之前的: 展示年份及日期，yyyy-mm-dd
                return publishDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        }
    }

    /**
     * 提取摘要内容
     *
     * @param record 信息流记录
     * @return 摘要内容
     */
    private String extractSummary(TabFeedsResponse.FeedRecord record) {
        // 优先使用displaySummary
        if (record.getDisplaySummary() != null) {
            return record.getDisplaySummary();
        }

        // 如果没有displaySummary，则合并summary列表
        if (record.getSummary() != null && !record.getSummary().isEmpty()) {
            return record.getSummary().stream()
                    .filter(item -> "txt".equals(item.getType()) && item.getTxt() != null)
                    .map(TabFeedsResponse.SummaryItem::getTxt)
                    .collect(Collectors.joining());
        }

        return "";
    }

    /**
     * 处理图片列表
     *
     * @param feedItem Feed项
     * @param record   信息流记录
     */
    private void processImageList(TabFeedsComponent.FeedItem feedItem, TabFeedsResponse.FeedRecord record) {

        feedItem.setType(CommunityPostType.VIDEO_ARTICLE.getCode().equals(record.getType()) ? "video" : "image");

        // 图片
        List<TabFeedsResponse.ImageInfo> imgList = record.getImgList();
        if (!CollectionUtils.isEmpty(imgList)) {
            List<ImageElement> images = imgList.stream()
                    .limit(MAX_IMAGES_PER_FEED)
                    .map(img -> {
                        ImageElement feedImage = new ImageElement();
                        feedImage.setSrc(img.getImageUrl());
                        feedImage.setWidth(img.getWidth());
                        feedImage.setHeight(img.getHeight());
                        return feedImage;
                    })
                    .collect(Collectors.toList());

            feedItem.setImageList(images);
            feedItem.setImageCount(imgList.size());
        }

        // 视频
        List<TabFeedsResponse.VideoInfo> videoInfoList = record.getVideoList();
        if (!CollectionUtils.isEmpty(videoInfoList)) {
            feedItem.setVideoList(videoInfoList.stream()
                    .map(videoInfo -> {
                        VideoElement video = new VideoElement();
                        video.setCover(videoInfo.getCover());
                        video.setHeight(videoInfo.getHeight());
                        video.setWidth(videoInfo.getWidth());
                        video.setDuration(videoInfo.getDuration());
                        return video;
                    })
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 创建交互操作
     *
     * @param record 信息流记录
     * @return 交互操作
     */
    private ActionElement createAction(TabFeedsResponse.FeedRecord record) {
        boolean isVideo = CommunityPostType.VIDEO_ARTICLE.getCode().equals(record.getType());
        ActionElement action = new ActionElement();
        action.setType("link");
        action.setLinkUrl(routerService.getCommunityPostUrl(record.getPostId(), isVideo, null, null));
        return action;
    }

    /**
     * 创建交互操作
     *
     * @param record 信息流记录
     * @return 交互操作
     */
    private List<ButtonElement> createButtons(TabFeedsResponse.FeedRecord record) {
        boolean isVideo = CommunityPostType.VIDEO_ARTICLE.getCode().equals(record.getType());
        List<ButtonElement> buttons = new ArrayList<>();

        // 点赞操作
        ButtonElement likeAction = new ButtonElement();
        likeAction.setType("like");
        likeAction.setText(String.valueOf(record.getLikeCnt()));
        likeAction.setStatus(Boolean.TRUE.equals(record.getLike()) ? 1 : 0);
        buttons.add(likeAction);

        // 评论操作
        ButtonElement commentAction = new ButtonElement();
        commentAction.setType("comment");
        commentAction.setText(String.valueOf(record.getCommentCnt()));
        commentAction.setLinkUrl(routerService.getCommunityCommentUrl(record.getPostId(), isVideo, true,null, null));
        buttons.add(commentAction);

        return buttons;
    }

    public PostActionResponse likePost(String userId, String postId) {
        return executeFeignPostAction(userId, postId, "like", communityApiFeignClient::likePost);
    }

    public PostActionResponse unlikePost(String userId, String postId) {
        return executeFeignPostAction(userId, postId, "unlike", communityApiFeignClient::unlikePost);
    }

    /**
     * 执行点赞或取消点赞操作 using Feign
     *
     * @param userId     用户ID
     * @param postId     帖子ID
     * @param actionType 操作类型描述（用于日志）
     * @param feignCall  The Feign client method reference (e.g., communityApiFeignClient::likePost)
     * @return 操作响应，包含状态码和消息
     */
    private PostActionResponse executeFeignPostAction(String userId, String postId, String actionType,
                                                      Function<Map<String, Object>, Map<String, Object>> feignCall) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(postId)) {
            log.error("{} post failed: userId or postId is empty", actionType);
            return PostActionResponse.fail(HTTP_STATUS_BAD_REQUEST, "用户ID或帖子ID为空");
        }
        try {
            // 准备请求体
            Map<String, Object> requestBody = new HashMap<>(2);
            requestBody.put("userId", userId);
            requestBody.put("postId", postId);

            // 调用 Feign Client
            log.debug("Calling {} API for postId: {}", actionType, postId);
            Map<String, Object> response = feignCall.apply(requestBody);
            log.debug("Received response from {} API", actionType);

            // 验证响应
            if (response == null) {
                log.error("Failed to {} post: {}. Response is null", actionType, postId);
                return PostActionResponse.fail(HTTP_STATUS_SERVER_ERROR, "服务器响应为空");
            }

            // 获取响应中的code和message
            Integer code = response.containsKey("code")
                    ? (response.get("code") instanceof Integer
                    ? (Integer) response.get("code")
                    : Integer.parseInt(response.get("code").toString()))
                    : HTTP_STATUS_SERVER_ERROR;

            String message = response.containsKey("message")
                    ? response.get("message").toString()
                    : "未知错误";

            // 判断是否成功
            boolean success = code == HTTP_STATUS_OK;

            if (success) {
                log.info("Successfully {}d post: {}", actionType, postId);
                return PostActionResponse.builder()
                        .code(code)
                        .message(message)
                        .success(true)
                        .build();
            } else {
                log.error("Failed to {} post: {}. Response: {}", actionType, postId, response);
                return PostActionResponse.builder()
                        .code(code)
                        .message(message)
                        .success(false)
                        .build();
            }
        } catch (Exception e) {
            log.error("Error {}ing post: {}", actionType, postId, e);
            return PostActionResponse.fail(HTTP_STATUS_SERVER_ERROR, "服务调用异常: " + e.getMessage());
        }
    }

    /**
     * 准备请求参数 (Keep this method as it's used by fetchOfficeNews)
     *
     * @param params 基础参数
     * @param userId 用户ID
     * @return 完整的请求参数
     */
    private Map<String, Object> prepareRequestParams(Map<String, Object> params, String userId) {
        Map<String, Object> requestParams = params != null ? new HashMap<>(params) : new HashMap<>();

        // 处理userId
        if (StringUtils.hasText(userId)) {
            requestParams.put("userId", userId);
        }

        return requestParams;
    }

    /**
     * 检查响应是否成功 (根据 Feign 行为调整)
     * 注意: Feign 默认在 HTTP 状态码非 2xx 时抛出 FeignException。
     * 如果使用了自定义 ErrorDecoder 来阻止抛出异常，则此方法仍然需要检查 code。
     * 如果依赖默认行为，那么能进入此方法的响应理论上已经是 2xx 了。
     * 但为了与原始逻辑保持一致（检查业务 code），我们保留检查。
     *
     * @param response 响应对象
     * @param <T>      响应类型
     * @return 是否成功 (业务 code == 200)
     */
    private <T> boolean isSuccessResponse(T response) {
        if (response == null) {
            log.warn("Received null response from API");
            return false;
        }

        Integer code = null;
        if (response instanceof OfficeNewsResponse) {
            code = ((OfficeNewsResponse) response).getCode();
        } else if (response instanceof TabFeedsResponse) {
            code = ((TabFeedsResponse) response).getCode();
        } else if (response instanceof PostRelationResponse) {
            code = ((PostRelationResponse) response).getCode();
        } else if (response instanceof PostDetailResponse) {
            code = ((PostDetailResponse) response).getCode();
        }
        // Note: For like/unlike which return Map, the code check happens in executeFeignPostAction

        // If code couldn't be extracted (e.g., for Map responses handled elsewhere), assume check passed here.
        if (code == null) {
            return true; // Let the specific handlers decide for types like Map
        }

        boolean success = code == HTTP_STATUS_OK;
        if (!success) {
            log.warn("API response indicates failure with business code: {}", code);
        }
        return success;
    }

    /**
     * 从参数中获取offset值 (Keep this method as it's used by processTabFeedsResponse)
     *
     * @param params 请求参数
     * @return offset值
     */
    private Integer getOffset(Map<String, Object> params) {
        return Optional.ofNullable(params)
                .map(p -> p.get("after"))
                .map(after -> {
                    if (after instanceof Integer) {
                        return (Integer) after;
                    } else if (after instanceof String) {
                        try {
                            return Integer.parseInt((String) after);
                        } catch (NumberFormatException e) {
                            return 0;
                        }
                    }
                    return 0;
                })
                .orElse(0);
    }

    public Result<List<PostRelationData>> getPostRelation(String userId, String postIds) {
        // 使用 Feign Client 调用
        log.debug("Calling getPostRelation API with userId: {}, postIds: {}", userId, postIds);
        PostRelationResponse response = communityApiFeignClient.getPostRelation(userId, postIds);
        log.debug("Received response from getPostRelation API");

        // 验证响应并返回数据
        if (isSuccessResponse(response)) {
            return Result.success(response.getData());
        }
        return ResultHelper.fail(response.getCode(), response.getMessage());
    }

}
