package com.xiaomi.micar.site.service.experiment;

import com.xiaomi.micar.site.constants.SiteComponentConstants;
import com.xiaomi.micar.site.enums.ExperimentStatus;
import com.xiaomi.micar.site.model.ExperimentResult;

import java.util.HashMap;
import java.util.Map;

import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.component.ComponentConf;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.utils.CombinationKeyGenerate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 负责实验相关的逻辑，如确定用户的实验桶
 * 处理实验结果的应用
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class ExperimentHelper {

    /**
     * 确定适用的实验
     *
     * @param pageConfig 页面配置模型
     * @param strategy 用户组ID
     * @param userId 用户ID
     * @return 实验结果对象，包含适用的实验列表
     */
    public ExperimentResult determineApplicableExperiments(
            PageConfigModel pageConfig,
            String strategy,
            String userId) {

        if (pageConfig == null || strategy == null || userId == null) {
            log.warn("[ExperimentHelper] Cannot determine experiments, missing required parameters: pageConfig={}, strategy={}, userId={}",
                pageConfig != null, strategy != null, userId != null);
            return ExperimentResult.empty();
        }

        String pageId = pageConfig.getPageId();
        log.debug("[ExperimentHelper] Determining applicable experiments for pageId: {}, strategy: {}, userId: {}",
            pageId, strategy, userId);

        Map<String, List<String>> relevantExperimentBuckets = getRelevantExperimentBuckets(pageConfig);

        if (relevantExperimentBuckets.isEmpty()) {
            log.info("[ExperimentHelper] No relevant experiment buckets found for pageId: {}", pageId);
            return ExperimentResult.empty();
        }

        log.debug("[ExperimentHelper] Found {} relevant experiments for pageId: {}: {}",
            relevantExperimentBuckets.size(), pageId, relevantExperimentBuckets.keySet());

        List<Map<String, String>> applicableExperiments = relevantExperimentBuckets.entrySet().stream()
                .map(entry -> {
                    String experimentId = entry.getKey();
                    List<String> availableBucketIds = entry.getValue();

                    Optional<PageConfigModel.ExperimentModel> experimentOpt = pageConfig.getExperiments().stream()
                            .filter(e -> experimentId.equals(e.getExperimentId()))
                            .findFirst();

                    if (!experimentOpt.isPresent()) {
                        log.warn("[ExperimentHelper] Experiment {} not found in page config for pageId: {}",
                            experimentId, pageId);
                        return null;
                    }

                    PageConfigModel.ExperimentModel experiment = experimentOpt.get();

                    // 检查实验条件类型和条件规则
                    if (!isUserEligibleForExperiment(experiment)) {
                        log.debug("[ExperimentHelper] User {} is not eligible for experiment {} on page {}",
                            userId, experimentId, pageId);
                        return null;
                    }

                    String bucketId = determineBucketForUser(experiment, userId, availableBucketIds);
                    if (bucketId == null) {
                        log.debug("[ExperimentHelper] Could not determine bucket for user {} in experiment {} on page {}",
                            userId, experimentId, pageId);
                        return null;
                    }

                    log.info("[ExperimentHelper] User {} assigned to bucket {} for experiment {} on page {}",
                        userId, bucketId, experimentId, pageId);

                    Map<String, String> applicableExperiment = new HashMap<>();
                    applicableExperiment.put(SiteComponentConstants.Experiment.EXPERIMENT_ID, experimentId);
                    applicableExperiment.put(SiteComponentConstants.Bucket.BUCKET_ID, bucketId);
                    return applicableExperiment;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("[ExperimentHelper] Found {} applicable experiments for pageId: {}, userId: {}",
            applicableExperiments.size(), pageId, userId);

        return new ExperimentResult(applicableExperiments);
    }

    /**
     * 获取相关的实验桶。
     * @param pageConfig 页面配置模型
     * @return 实验ID到桶ID列表的映射
     */
    public Map<String, List<String>> getRelevantExperimentBuckets(final PageConfigModel pageConfig) {
        Map<String, List<String>> relevantBuckets = new HashMap<>();

        if (pageConfig == null) {
            log.error("[ExperimentHelper] Page config is null when getting relevant experiment buckets");
            return relevantBuckets;
        }

        String pageId = pageConfig.getPageId();

        if (pageConfig.getExperiments() == null) {
            log.info("[ExperimentHelper] No experiments found for pageId: {}", pageId);
            return relevantBuckets;
        }

        // 获取当前时间戳
        long currentTime = System.currentTimeMillis();
        log.debug("[ExperimentHelper] Processing experiments for pageId: {}, found {} experiments",
            pageId, pageConfig.getExperiments().size());

        for (PageConfigModel.ExperimentModel experiment : pageConfig.getExperiments()) {
            String experimentId = experiment.getExperimentId();
            if (StringUtils.isBlank(experimentId) || experiment.getBuckets() == null) {
                log.warn("[ExperimentHelper] Invalid experiment found for pageId: {}, experimentId: {}, buckets: {}",
                    pageId, experimentId, experiment.getBuckets() != null);
                continue;
            }

            // 检查实验状态
            ExperimentStatus status = ExperimentStatus.fromCode(experiment.getStatus());
            if (!status.isActive()) {
                log.debug("[ExperimentHelper] Experiment {} is not active for pageId: {}, status: {}",
                    experimentId, pageId, status);
                continue;
            }

            // 检查实验时间范围
            try {
                // 检查开始时间
                if (experiment.getStartTime() != null) {
                    long startTime = experiment.getStartTime();
                    if (currentTime < startTime) {
                        log.debug("[ExperimentHelper] Experiment {} has not started yet for pageId: {}, current: {}, start: {}",
                                experimentId, pageId, currentTime, startTime);
                        continue;
                    }
                }

                // 检查结束时间
                if (experiment.getEndTime() != null) {
                    long endTime = experiment.getEndTime();
                    if (currentTime > endTime) {
                        log.debug("[ExperimentHelper] Experiment {} has ended for pageId: {}, current: {}, end: {}",
                                experimentId, pageId, currentTime, endTime);
                        continue;
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("[ExperimentHelper] Invalid time format for experiment {} on pageId: {}: startTime={}, endTime={}",
                        experimentId, pageId, experiment.getStartTime(), experiment.getEndTime());
                continue;
            }

            List<String> bucketIds = experiment.getBuckets().stream()
                    .map(PageConfigModel.BucketModel::getBucketId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (!bucketIds.isEmpty()) {
                relevantBuckets.put(experimentId, bucketIds);
                log.debug("[ExperimentHelper] Added experiment {} with {} buckets for pageId: {}",
                    experimentId, bucketIds.size(), pageId);
            } else {
                log.warn("[ExperimentHelper] Experiment {} has no valid buckets for pageId: {}",
                    experimentId, pageId);
            }
        }

        log.info("[ExperimentHelper] Found {} relevant experiments for pageId: {}",
            relevantBuckets.size(), pageId);
        return relevantBuckets;
    }

    /**
     * 确定用户的实验桶。
     * @param experiment 实验模型
     * @param userId 用户ID
     * @param availableBucketIds 可用的桶ID列表
     * @return 确定的桶ID
     */
    public String determineBucketForUser(
            final PageConfigModel.ExperimentModel experiment,
            final String userId,
            final List<String> availableBucketIds) {

        if (experiment == null || userId == null || availableBucketIds == null || availableBucketIds.isEmpty()) {
            log.error("[ExperimentHelper] Cannot determine bucket, missing required parameters: experiment={}, userId={}, availableBucketIds={}",
                experiment != null, userId != null, availableBucketIds != null && !availableBucketIds.isEmpty());
            return null;
        }

        String experimentId = experiment.getExperimentId();
        log.debug("[ExperimentHelper] Determining bucket for user: {}, experiment: {}, available buckets: {}",
            userId, experimentId, availableBucketIds);

        List<PageConfigModel.BucketModel> buckets = experiment.getBuckets();
        if (buckets == null || buckets.isEmpty()) {
            log.warn("[ExperimentHelper] No buckets found for experiment: {}", experimentId);
            return null;
        }

        // 尝试使用 bucketIds 进行匹配
        Optional<PageConfigModel.BucketModel> matchingBucket = determineBucketIdForUser(buckets, userId, experimentId);
        if (matchingBucket.isPresent() && availableBucketIds.contains(matchingBucket.get().getBucketId())) {
            String bucketId = matchingBucket.get().getBucketId();
            log.debug("[ExperimentHelper] Matched user {} to bucket {} for experiment {}",
                userId, bucketId, experimentId);
            return bucketId;
        }

        log.debug("[ExperimentHelper] No matching bucket found for user {} in experiment {}",
            userId, experimentId);

        return null;
    }

    /**
     * 确定用户的桶。
     * @param buckets 桶列表
     * @param userId 用户ID
     * @param experimentId 实验ID
     * @return 确定的桶ID
     */
    private Optional<PageConfigModel.BucketModel> determineBucketIdForUser(
            final List<PageConfigModel.BucketModel> buckets,
            final String userId,
            final String experimentId) {

        if (buckets == null || buckets.isEmpty() || userId == null || experimentId == null) {
            log.error("[ExperimentHelper] Cannot determine bucket ID, missing required parameters");
            return Optional.empty();
        }

        // 生成用户的桶ID
        int userBucketId = CombinationKeyGenerate.generateBucketIdForUser(userId, experimentId);
        if (userBucketId == -1) {
            log.warn("[ExperimentHelper] Failed to generate bucket ID for user: {}, experiment: {}",
                userId, experimentId);
            return Optional.empty();
        }

        log.debug("[ExperimentHelper] Generated bucket ID {} for user {} in experiment {}",
            userBucketId, userId, experimentId);

        // 查找包含用户桶ID的实验桶
        return buckets.stream()
                .filter(bucket -> bucket.getBucketIds() != null && CollectionUtils.isNotEmpty(bucket.getBucketIds()))
                .filter(bucket -> bucket.getBucketIds().contains(String.valueOf(userBucketId)))
                .findFirst();
    }

    /**
     * 应用实验结果。
     * @param pageConfig 页面配置模型
     * @param experimentResult 实验结果
     * @param pageResp 页面响应
     */
    public void applyExperimentCombination(final PageConfigModel pageConfig, final ExperimentResult experimentResult, final PageRespV2 pageResp) {
        if (experimentResult == null || experimentResult.getApplicableExperiments() == null) {
            log.debug("[ExperimentHelper] Experiment result or applicable experiments is null, nothing to apply");
            return;
        }

        List<Map<String, String>> applicableExperiments = experimentResult.getApplicableExperiments();
        log.info("[ExperimentHelper] Applying {} experiment combinations for pageId: {}",
            applicableExperiments.size(), pageConfig != null ? pageConfig.getPageId() : "unknown");

        applyExperimentCombinationInternal(pageConfig, applicableExperiments, pageResp);
    }

    /**
     * 应用实验组合。
     * @param pageConfig 页面配置模型
     * @param combination 组合
     * @param pageResp 页面响应
     */
    public void applyExperimentCombinationInternal(
            final PageConfigModel pageConfig,
            final List<Map<String, String>> combination,
            final PageRespV2 pageResp) {
        if (pageConfig == null || combination == null || combination.isEmpty()
                || pageConfig.getExperiments() == null) {
            log.debug("[ExperimentHelper] Cannot apply experiment combination, missing required parameters");
            return;
        }

        String pageId = pageConfig.getPageId();
        log.debug("[ExperimentHelper] Applying {} experiment combinations for pageId: {}",
            combination.size(), pageId);

        for (Map<String, String> experiment : combination) {
            String experimentId = experiment.get(SiteComponentConstants.Experiment.EXPERIMENT_ID);
            String bucketId = experiment.get(SiteComponentConstants.Bucket.BUCKET_ID);

            if (StringUtils.isBlank(experimentId) || StringUtils.isBlank(bucketId)) {
                log.warn("[ExperimentHelper] Invalid experiment combination: experimentId={}, bucketId={}",
                    experimentId, bucketId);
                continue;
            }

            Optional<PageConfigModel.ExperimentModel> experimentModel = pageConfig.getExperiments().stream()
                    .filter(e -> experimentId.equals(e.getExperimentId()))
                    .findFirst();

            if (!experimentModel.isPresent() || experimentModel.get().getBuckets() == null) {
                log.warn("[ExperimentHelper] Experiment {} not found or has no buckets for pageId: {}",
                    experimentId, pageId);
                continue;
            }

            Optional<PageConfigModel.BucketModel> bucketModel = experimentModel.get().getBuckets().stream()
                    .filter(b -> bucketId.equals(b.getBucketId()))
                    .findFirst();

            if (!bucketModel.isPresent()) {
                log.warn("[ExperimentHelper] Bucket {} not found for experiment {} on pageId: {}",
                    bucketId, experimentId, pageId);
                continue;
            }

            log.info("[ExperimentHelper] Applying experiment {}, bucket {} for pageId: {}",
                experimentId, bucketId, pageId);

            // 应用页面信息
            if (bucketModel.get().getPageInfo() != null && !bucketModel.get().getPageInfo().isEmpty()) {
                applyPageInfo(bucketModel.get().getPageInfo(), pageResp);
                log.debug("[ExperimentHelper] Applied page info for experiment {}, bucket {} on pageId: {}",
                    experimentId, bucketId, pageId);
            }

            // 应用组件信息列表
            if (bucketModel.get().getComponentInfoList() != null && !bucketModel.get().getComponentInfoList().isEmpty()) {
                applyComponentInfoList(bucketModel.get().getComponentInfoList(), pageResp);
                log.debug("[ExperimentHelper] Applied component info for experiment {}, bucket {} on pageId: {}",
                    experimentId, bucketId, pageId);
            }
        }
    }

    /**
     * 应用页面信息。
     * @param pageInfo 页面信息
     * @param pageResp 页面响应
     */
    private void applyPageInfo(final Map<String, Object> pageInfo, final PageRespV2 pageResp) {
        if (pageInfo == null || pageInfo.isEmpty() || pageResp.getPage() == null) {
            return;
        }

        // 应用页面标题
        if (pageInfo.containsKey("title") && pageInfo.get("title") != null) {
            pageResp.getPage().setTitle(pageInfo.get("title").toString());
        }

        // 可以在这里添加更多页面级别的配置项
    }

    /**
     * 应用组件信息列表。
     * @param componentInfoList 组件信息列表
     * @param pageResp 页面响应
     */
    private void applyComponentInfoList(final List<Component> componentInfoList, final PageRespV2 pageResp) {
        if (componentInfoList == null || componentInfoList.isEmpty() || pageResp.getModules() == null) {
            return;
        }

        List<ComponentConf> modules = pageResp.getModules();
        Map<String, Integer> componentIndexMap = new HashMap<>();

        // 建立组件ID与其在列表中索引的映射
        for (int i = 0; i < modules.size(); i++) {
            ComponentConf conf = modules.get(i);
            if (conf != null && conf.getId() != null) {
                componentIndexMap.put(conf.getId(), i);
            }
        }

        for (Component componentInfo : componentInfoList) {
            if (componentInfo == null || componentInfo.getId() == null) {
                continue;
            }

            // 如果模块中已存在该组件，则更新它
            Integer index = componentIndexMap.get(componentInfo.getId());
            if (index != null) {
                ComponentConf existingConf = modules.get(index);
                existingConf.setConfig(componentInfo);

            }
        }
    }

    /**
     * 确定用户的实验桶。
     * @param experiment 实验模型
     * @return 确定的桶ID
     */
    public boolean isUserEligibleForExperiment(
            final PageConfigModel.ExperimentModel experiment) {

        if (experiment == null) {
            log.error("[ExperimentHelper] Cannot check eligibility for null experiment");
            return false;
        }

        String experimentId = experiment.getExperimentId();

        // 如果没有设置条件类型，默认所有用户都符合条件
        if (StringUtils.isBlank(experiment.getConditionType())) {
            log.debug("[ExperimentHelper] No condition type set for experiment {}, defaulting to eligible",
                experimentId);
            return true;
        }

        String conditionType = experiment.getConditionType();
        log.debug("[ExperimentHelper] Checking eligibility for experiment {}, condition type: {}",
            experimentId, conditionType);

        // 条件类型：0全部，1百分比，2文件人群包上传，3人群包id
        switch (conditionType) {
            case "0": // 全部用户
                log.debug("[ExperimentHelper] Condition type 0 (all users) for experiment {}, user is eligible",
                    experimentId);
                return true;
            default:
                log.warn("[ExperimentHelper] Unknown condition type: {} for experiment: {}",
                    conditionType, experimentId);
                return false;
        }
    }
}
