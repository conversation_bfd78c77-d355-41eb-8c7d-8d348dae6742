package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.component.NewsComponent;
import com.xiaomi.micar.site.component.TabNewsComponent;
import com.xiaomi.micar.site.component.TabsComponent;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.component.Component;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TabNews组件数据提供者
 * 专门处理TabNewsComponent，内部调用NewsProvider处理每个Tab的NewsComponent
 */
@Slf4j
@org.springframework.stereotype.Component("tabNewsProvider")
public class TabNewsProvider extends AbstractComponentDataProvider {

    @Resource
    private NewsProvider newsProvider;

    @Override
    protected boolean validateComponentType(Component componentInfo) {
        return componentInfo instanceof TabNewsComponent;
    }

    @Override
    protected void doProcess(ResponseContext context, Component componentInfo) {
        TabNewsComponent tabNews = (TabNewsComponent) componentInfo;
        processTabNews(tabNews, context, componentInfo);
    }

    /**
     * 处理TabNews组件
     */
    private void processTabNews(TabNewsComponent tabNews, ResponseContext context, Component parentComponent) {
        List<TabNewsComponent.TabItem<NewsComponent>> items = tabNews.getItems();
        if (CollectionUtils.isEmpty(items)) {
            log.warn("TabNews组件没有标签项");
            return;
        }

        for (TabNewsComponent.TabItem<NewsComponent> item : items) {
            if (item.getContent() != null) {
                // 为每个item设置特定的查询参数（如果有dataProviderParams）
                ResponseContext itemContext = prepareItemContext(context, item, parentComponent);

                // 使用NewsProvider处理每个Tab的NewsComponent
                newsProvider.process(itemContext, item.getContent());
            }
        }
        log.info("TabNews组件处理完成，处理了{}个标签项", items.size());
    }

    /**
     * 为TabItem准备上下文，合并item的dataProviderParams到查询参数中
     */
    private ResponseContext prepareItemContext(ResponseContext originalContext, TabsComponent.TabItem<NewsComponent> item, Component parentComponent) {
        try {
            Map<String, Object> queryParams = copyQueryParams(originalContext);

            // 添加 moduleId 到查询参数中
            if (parentComponent != null && StringUtils.isNotBlank(parentComponent.getId())) {
                queryParams.put("moduleId", parentComponent.getId());
            }

            // 合并 dataProviderParams
            Object dataProviderParams = item.getDataProviderParams();
            if (dataProviderParams != null) {
                mergeDataParams(queryParams, dataProviderParams, item.getTabId());
            }

            return new ResponseContext.Builder()
                    .pageId(originalContext.getPageId())
                    .groupId(originalContext.getGroupId())
                    .userId(originalContext.getUserId())
                    .pageConfig(originalContext.getPageConfig())
                    .isWhiteUser(originalContext.isWhiteUser())
                    .applicableExperiments(originalContext.getApplicableExperiments())
                    .queryParams(queryParams)
                    .build();

        } catch (Exception e) {
            log.warn("Failed to prepare context for TabNews item {}: {}", item.getTabId(), e.getMessage());
            return originalContext;
        }
    }

    /**
     * 复制查询参数
     */
    private Map<String, Object> copyQueryParams(ResponseContext context) {
        return context.getQueryParams() != null
                ? new HashMap<>(context.getQueryParams())
                : new HashMap<>();
    }

    /**
     * 合并数据提供者参数
     */
    private void mergeDataParams(Map<String, Object> queryParams, Object dataProviderParams, String tabId) {
        if (dataProviderParams instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) dataProviderParams;
            queryParams.putAll(paramsMap);

            // 添加tabId到查询参数中，用于缓存key构建
            queryParams.put("tabId", tabId);

            log.debug("Merged dataProviderParams for TabNews item {}: {}", tabId, paramsMap);
        }
    }

    @Override
    protected void handleProcessingError(Component componentInfo, Exception e) {
        log.error("TabNewsProvider处理失败", e);
        
        if (componentInfo instanceof TabNewsComponent) {
            TabNewsComponent tabNews = (TabNewsComponent) componentInfo;
            List<TabNewsComponent.TabItem<NewsComponent>> items = tabNews.getItems();
            if (!CollectionUtils.isEmpty(items)) {
                items.forEach(item -> {
                    if (item.getContent() != null) {
                        item.getContent().setData(java.util.Collections.emptyList());
                    }
                });
            }
        }
    }
}
