package com.xiaomi.micar.site.service.assembly;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.cache.SitePageCacheService;
import com.xiaomi.micar.site.model.ExperimentResult;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import com.xiaomi.micar.site.service.UserStrategyService;
import com.xiaomi.micar.site.route.RouteCacheFetcher;
import com.xiaomi.micar.site.service.config.PageConfigService;
import com.xiaomi.micar.site.route.RouteContext;
import com.xiaomi.micar.site.route.RouteContextBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * 页面装配服务
 * 负责处理前端用户的页面请求，包括缓存查询和实时页面生成
 *
 * 主要功能：
 * 1. 处理优化的页面数据请求（从缓存获取）
 * 2. 处理实时页面生成请求（用于预览等场景）
 * 3. 用户策略确定和实验分配
 * 4. 组合键生成和缓存查询
 * 5. 缓存状态查询
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Component
public class SitePageAssemblyService {

    @Resource
    private SitePageCacheService cacheService;
    @Resource
    private PageConfigService pageConfigService;
    @Resource
    private PageResponseBuilder pageResponseBuilder;
    @Resource
    private UserStrategyService userStrategyService;
    @Resource
    private RouteCacheFetcher routeCacheFetcher;
    @Resource
    private RouteContextBuilder routeContextBuilder;

    /**
     * 优化的页面数据获取接口（带预确定的用户策略）
     * 用于避免重复的用户策略查询
     *
     * @param pageId      页面ID
     * @param version     API版本 (1=V1, 2=V2)
     * @param userId      用户ID
     * @param queryParams 查询参数
     * @param userGroup    预确定的用户策略
     * @return JSON格式的页面响应字符串
     * @throws IllegalArgumentException 如果参数无效
     * @throws RuntimeException         如果未找到页面
     */
    public String pageDataV2OptimizedWithStrategy(
            String pageId,
            Integer version,
            String userId,
            Map<String, Object> queryParams,
            String userGroup) {

        // 参数验证
        validateRequestParameters(pageId, queryParams);

        // 基于用户给定的人群策略构建 RouteContext（保留 queryParams 可选的其他维度）
        RouteContext baseCtx = routeContextBuilder.resolve(pageId, queryParams, userId);
        java.util.Map<String, java.util.List<String>> dims = baseCtx.getDimValues() == null
                ? new java.util.HashMap<>()
                : new java.util.HashMap<>(baseCtx.getDimValues());
        dims.put(RouteContextBuilder.DIM_USER_STRATEGY, java.util.Collections.singletonList(userGroup));
        RouteContext finalCtx = RouteContext.of(dims);

        String cachedResponse = routeCacheFetcher.fetch(pageId, version, finalCtx);
        if (cachedResponse != null) {
            return cachedResponse;
        }
        throw new RuntimeException(String.format("页面缓存未找到: pageId=%s, ", pageId));
    }

    /**
     * 实时页面生成接口
     * 主要用于预览、测试等场景，不依赖缓存，实时生成页面数据
     *
     * @param pageConfig  页面配置
     * @param userId      用户ID
     * @param queryParams 查询参数
     * @return 页面响应对象
     * @throws IllegalArgumentException 如果参数无效
     * @throws RuntimeException         如果生成页面响应时发生错误
     */
    public PageRespV2 getResponseForPreview(
            SitePageConfig pageConfig,
            String userId,
            Map<String, Object> queryParams) {

        String pageId = pageConfig.getPageId();
        long startTime = System.currentTimeMillis();

        try {
            log.info("[SitePageAssemblyService] 开始实时生成页面响应: pageId={}, userId={}", pageId, userId);

            // 确定用户策略（考虑页面的strategy_state）
            String strategy = userStrategyService.determineUserStrategyWithPageCheck(pageId, queryParams, userId);
            log.debug("[SitePageAssemblyService] 用户策略确定为: {} (页面: {})", strategy, pageId);

            // 创建页面配置模型
            PageConfigModel pageConfigModel = pageConfigService.createPageConfigObject(
                    pageConfig, Collections.emptyList(), Collections.emptyList());

            // 使用简化的PageResponseBuilder直接构建响应
            PageRespV2 pageResp = pageResponseBuilder.buildPageResponse(
                    pageConfigModel,
                    pageId,
                    strategy,
                    userId,
                    queryParams,
                    ExperimentResult.empty(),
                    true
            );

            // 记录性能指标
            long processingTime = System.currentTimeMillis() - startTime;
            log.info("[SitePageAssemblyService] 页面响应生成完成: pageId={}, userId={}, 耗时={}ms",
                    pageId, userId, processingTime);

            return pageResp;

        } catch (Exception e) {
            log.error("[SitePageAssemblyService] 生成页面响应时发生错误: pageId={}, userId={}, error={}",
                    pageId, userId, e.getMessage(), e);
            throw new RuntimeException("生成页面响应时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 带指定策略的实时页面生成接口
     * 主要用于预览场景，使用指定的策略而不是根据用户身份判断策略
     *
     * @param pageConfig  页面配置
     * @param userId      用户ID
     * @param queryParams 查询参数
     * @param strategy    指定的策略
     * @return 页面响应对象
     * @throws IllegalArgumentException 如果参数无效
     * @throws RuntimeException         如果生成页面响应时发生错误
     */
    public PageRespV2 getResponseForPreviewWithStrategy(
            SitePageConfig pageConfig,
            String userId,
            Map<String, Object> queryParams,
            String strategy) {

        String pageId = pageConfig.getPageId();
        long startTime = System.currentTimeMillis();

        try {
            log.info("[SitePageAssemblyService] 开始实时生成页面响应(指定策略): pageId={}, userId={}, strategy={}", pageId, userId, strategy);

            // 创建页面配置模型
            PageConfigModel pageConfigModel = pageConfigService.createPageConfigObject(
                    pageConfig, Collections.emptyList(), Collections.emptyList());

            // 使用指定的策略构建响应
            PageRespV2 pageResp = pageResponseBuilder.buildPageResponse(
                    pageConfigModel,
                    pageId,
                    strategy,
                    userId,
                    queryParams,
                    ExperimentResult.empty(),
                    true
            );

            // 记录性能指标
            long processingTime = System.currentTimeMillis() - startTime;
            log.info("[SitePageAssemblyService] 页面响应生成完成(指定策略): pageId={}, userId={}, strategy={}, 耗时={}ms",
                    pageId, userId, strategy, processingTime);

            return pageResp;

        } catch (Exception e) {
            log.error("[SitePageAssemblyService] 生成页面响应时发生错误(指定策略): pageId={}, userId={}, strategy={}, error={}",
                    pageId, userId, strategy, e.getMessage(), e);
            throw new RuntimeException("生成页面响应时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 获取缓存状态的可视化表示
     *
     * @return 格式化的缓存状态字符串
     */
    public String visualizeCacheState() {
        Map<String, Integer> cacheStats = cacheService.getPageCacheStats();
        if (cacheStats.isEmpty()) {
            return "Cache is empty";
        }
        return JsonUtil.toJSONString(cacheStats);
    }

    /**
     * 验证请求参数
     */
    private void validateRequestParameters(String pageId, Map<String, Object> queryParams) {
        if (pageId == null || pageId.isEmpty()) {
            log.error("[SitePageAssemblyService] 页面ID不能为空");
            throw new IllegalArgumentException("页面ID不能为空");
        }

        if (queryParams == null) {
            log.error("[SitePageAssemblyService] 查询参数不能为空: pageId={}", pageId);
            throw new IllegalArgumentException("查询参数不能为空");
        }
    }

}
