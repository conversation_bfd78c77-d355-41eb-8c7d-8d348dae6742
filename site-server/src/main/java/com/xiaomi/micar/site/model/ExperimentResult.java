package com.xiaomi.micar.site.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 实验结果类
 * 封装适用于特定用户的实验和桶信息
 */
public class ExperimentResult {
    
    /**
     * 适用的实验列表
     */
    private final List<Map<String, String>> applicableExperiments;
    
    /**
     * 构造函数
     * 
     * @param applicableExperiments 适用的实验列表
     */
    public ExperimentResult(List<Map<String, String>> applicableExperiments) {
        this.applicableExperiments = applicableExperiments != null 
                ? applicableExperiments 
                : new ArrayList<>();
    }
    
    /**
     * 获取适用的实验列表
     *
     * @return 适用的实验列表
     */
    public List<Map<String, String>> getApplicableExperiments() {
        return applicableExperiments;
    }

    /**
     * 创建空的实验结果
     *
     * @return 空的实验结果
     */
    public static ExperimentResult empty() {
        return new ExperimentResult(new ArrayList<>());
    }
}
