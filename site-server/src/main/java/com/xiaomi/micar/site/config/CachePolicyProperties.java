package com.xiaomi.micar.site.config;

import com.xiaomi.micar.site.cache.policy.CachePolicy;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 缓存策略配置，支持在 yml 中声明默认与按页面/策略的规则
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "site.cache.policies")
public class CachePolicyProperties {
    /** 默认策略，保留现状：MYSQL_BROADCAST_MEMORY */
    private CachePolicy defaultPolicy = CachePolicy.MYSQL_BROADCAST_MEMORY;

    /** 规则列表 */
    private List<PagePolicyRule> rules = new ArrayList<>();
}

