package com.xiaomi.micar.site.service.processor;

import com.xiaomi.micar.site.cache.CachePostProcessor;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.service.CommunityPostCacheService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 社区帖子缓存处理器
 * 在缓存构建完成后，收集所有页面中的帖子ID，并批量获取和缓存帖子详情
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@Component
public class CommunityPostCacheProcessor implements CachePostProcessor {

    @Resource
    @Setter
    private CommunityPostCacheService communityPostCacheService;

    @Override
    public void process(Map<String, PageRespV2> cacheState) {
        log.info("[CommunityPostCacheProcessor] 开始处理社区帖子缓存");

        // 获取页面的社区帖子ID并去重
        Set<String> allPostIds = collectPostIds(cacheState);

        // 使用社区帖子缓存服务批量获取并缓存帖子详情
        if (!allPostIds.isEmpty()) {
            log.info("[CommunityPostCacheProcessor] 从页面缓存中收集到{}个社区帖子ID，调用缓存服务获取详情", allPostIds.size());
            communityPostCacheService.fetchAndCachePostDetails(allPostIds);
        } else {
            log.info("[CommunityPostCacheProcessor] 未从页面缓存中收集到社区帖子ID，跳过处理");
        }

        log.info("[CommunityPostCacheProcessor] 社区帖子缓存处理完成");
    }

    @Override
    public String getName() {
        return "CommunityPostCacheProcessor";
    }

    @Override
    public int getOrder() {
        return 100; // 设置一个较低的优先级，确保在其他处理器之后执行
    }

    /**
     * 从缓存状态中收集所有帖子ID
     *
     * @param cacheState 缓存状态
     * @return 帖子ID集合
     */
    private Set<String> collectPostIds(Map<String, PageRespV2> cacheState) {
        Set<String> allPostIds = new HashSet<>();

        cacheState.forEach((s, pageRespV2) -> {
            List<String> postIds = pageRespV2.getPostIds();
            if (!CollectionUtils.isEmpty(postIds)) {
                allPostIds.addAll(postIds);
            }
        });

        log.info("[CommunityPostCacheProcessor] 从{}个页面中收集到{}个社区帖子ID",
                cacheState.size(), allPostIds.size());

        return allPostIds;
    }
}
