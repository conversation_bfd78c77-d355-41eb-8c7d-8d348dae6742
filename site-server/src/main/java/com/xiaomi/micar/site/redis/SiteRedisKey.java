package com.xiaomi.micar.site.redis;

import lombok.Getter;

import java.util.concurrent.TimeUnit;

/**
 * 站点服务Redis键枚举
 * 定义了站点服务使用的所有Redis键
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Getter
public enum SiteRedisKey {

    /**
     * 社区帖子详情缓存
     * 格式: {site:community:post}:detail:{postId}
     * 使用String结构，value为序列化后的PostDetail对象
     * 使用{}包裹前缀确保相关键被路由到同一个Redis节点，支持mget批量获取
     */
    COMMUNITY_POST_DETAIL_STRING("{site:community:post}:detail:%s", 1, TimeUnit.DAYS),

    /**
     * 社区帖子分页缓存 - 使用List结构
     * 格式: {site:community:pagination}:list:{cacheKey}
     * 使用List结构，每个元素为ArticleElement的JSON字符串
     * 支持LRANGE直接分页查询，用于专题页分页查询，缓存1小时
     */
    COMMUNITY_PAGINATION_LIST("{site:community:pagination}:list:%s", 1, TimeUnit.HOURS),

    /**
     * 社区帖子ID列表缓存 - 使用String结构
     * 格式: {site:community:pagination}:ids:{cacheKey}
     * 使用String结构，value为帖子ID列表的JSON数组
     * 用于存储帖子ID列表，支持按需分页获取详情，缓存1小时
     */
    COMMUNITY_POST_IDS_LIST("{site:community:pagination}:ids:%s", 1, TimeUnit.HOURS),

    /**
     * 页面快照缓存（Hash）
     * 格式: {site:page:cache}:{pageId}
     * field: 带版本前缀的组合键 (V1_/V2_ + combinationKey)
     * value: JSON字符串（与MySQL存储一致）
     */
    SITE_PAGE_CACHE_HASH("{site:page:cache}:%s", 1, TimeUnit.DAYS)
    ;
    /**
     * Redis键前缀
     */
    private final String prefix;

    /**
     * 过期时间
     */
    private final long expire;

    /**
     * 时间单位
     */
    private final TimeUnit timeUnit;

    SiteRedisKey(String prefix, long expire, TimeUnit timeUnit) {
        this.prefix = prefix;
        this.expire = expire;
        this.timeUnit = timeUnit;
    }

    /**
     * 格式化Redis键
     *
     * @param args 参数
     * @return 格式化后的Redis键
     */
    public String toFormat(String... args) {
        if (args == null || args.length == 0) {
            return prefix;
        }
        return String.format(prefix, (Object[]) args);
    }
}
