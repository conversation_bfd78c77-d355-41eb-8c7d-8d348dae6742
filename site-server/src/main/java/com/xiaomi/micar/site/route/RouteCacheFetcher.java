package com.xiaomi.micar.site.route;

import com.xiaomi.micar.site.cache.util.CacheKeyUtil;
import com.xiaomi.micar.site.service.cache.CacheReadFacade;
import com.xiaomi.micar.site.utils.CombinationKeyGenerate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * 路由过程的读取执行器：
 * - 生成候选（同层替代/降维），逐个解析策略并读取缓存；
 * - 命中即返回；全部未命中返回 null。
 */
@Slf4j
@Component
public class RouteCacheFetcher {

    @Resource
    private RouteCandidateBuilder routeCandidateBuilder;
    @Resource
    private CacheReadFacade cacheReadFacade;

    // 旧重载已移除：统一使用基于 RouteContext 的入口

    /**
     * 新版：仅基于 RouteContext 生成候选并读取缓存（无初始 routeCode）。
     */
    public String fetch(final String pageId,
                        final Integer version,
                        final RouteContext routeContext) {
        List<String> candidates = safeDedup(routeCandidateBuilder.build(routeContext));
        String from = candidates.isEmpty() ? "all" : candidates.get(0);
        return doFetch(pageId, version, candidates, from + " (by ctx)");
    }

    private String doFetch(final String pageId, final Integer version, final List<String> candidates, final String fromLabel) {
        if (pageId == null || pageId.isEmpty()) {
            return null;
        }
        if (version == null) {
            return null;
        }
        List<String> cands = (candidates == null || candidates.isEmpty())
                ? Collections.singletonList("all")
                : candidates;

        String firstKey = null;
        for (int idx = 0; idx < cands.size(); idx++) {
            String cand = cands.get(idx);
            if (cand == null || cand.isEmpty()) {
                continue;
            }
            String key = CombinationKeyGenerate.generateCombinationKey(pageId, cand);
            if (idx == 0) {
                firstKey = key;
            }
            String keyWithVersion = CacheKeyUtil.addVersionPrefix(key, version);
            String hit = cacheReadFacade.get(pageId, cand, keyWithVersion);
            if (hit != null) {
                if (idx > 0) {
                    log.info("[RouteCacheFetcher] 退化命中: pageId={}, from={}, to={}", pageId, fromLabel, cand);
                } else {
                    log.info("[RouteCacheFetcher] 缓存命中: pageId={}, key={}", pageId, keyWithVersion);
                }
                return hit;
            }
        }
        String firstKeyWithVersion = firstKey == null ? null : CacheKeyUtil.addVersionPrefix(firstKey, version);
        log.debug("[RouteCacheFetcher] 全部未命中: pageId={}, firstKey={}, from={}", pageId, firstKeyWithVersion, fromLabel);
        return null;
    }

    private List<String> safeDedup(List<String> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        LinkedHashSet<String> set = new LinkedHashSet<>();
        for (String s : list) {
            if (s != null && !s.isEmpty()) {
                set.add(s);
            }
        }
        return new ArrayList<>(set);
    }

}
