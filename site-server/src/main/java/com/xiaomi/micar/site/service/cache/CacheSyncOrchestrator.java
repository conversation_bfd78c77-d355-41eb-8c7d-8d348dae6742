package com.xiaomi.micar.site.service.cache;

import com.xiaomi.micar.site.cache.model.CacheState;
import com.xiaomi.micar.site.cache.policy.CachePolicy;
import com.xiaomi.micar.site.cache.service.CacheSyncService;
import com.xiaomi.micar.site.cache.CacheStorageService;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.xiaomi.micar.site.util.CombinationKeyParser;
import java.util.HashMap;
import java.util.Map;

/**
 * 预载同步编排：按策略拆分写入（MySQL+广播 / RedisOnly）
 */
@Slf4j
@Service
public class CacheSyncOrchestrator {

    @Resource
    private CachePolicyResolver policyResolver;

    @Resource
    private CacheSyncService cacheSyncService;

    @Resource(name = "redisCacheStorageService")
    private CacheStorageService redisCacheStorageService;

    /**
     * 按策略分流同步
     */
    public void syncByPolicy(CacheState cacheState, Map<String, Map<String, PageRespV2>> pageCacheMap) {
        if (pageCacheMap == null || pageCacheMap.isEmpty()) {
            log.warn("[CacheSyncOrchestrator] pageCacheMap 为空，跳过");
            return;
        }

        Map<String, Map<String, PageRespV2>> mysqlMap = new HashMap<>();
        Map<String, Map<String, PageRespV2>> redisMap = new HashMap<>();

        // 拆分
        pageCacheMap.forEach((pageId, combos) -> {
            if (combos == null || combos.isEmpty()) {
                return;
            }
            for (Map.Entry<String, PageRespV2> e : combos.entrySet()) {
                String combinationKey = e.getKey();
                String strategy = extractStrategy(combinationKey);
                CachePolicy policy = policyResolver.resolve(pageId, strategy);

                if (policy == CachePolicy.REDIS_ONLY) {
                    redisMap.computeIfAbsent(pageId, k -> new HashMap<>()).put(combinationKey, e.getValue());
                } else {
                    // 默认归于 MySQL+广播
                    mysqlMap.computeIfAbsent(pageId, k -> new HashMap<>()).put(combinationKey, e.getValue());
                }
            }
        });

        // MySQL + 广播（复用现有实现）
        if (!mysqlMap.isEmpty()) {
            int success = cacheSyncService.syncCacheToStorageWithConfigVersion(cacheState, mysqlMap);
            log.info("[CacheSyncOrchestrator] MySQL+广播 同步完成，页面={}，成功={}", mysqlMap.size(), success);
        } else {
            log.info("[CacheSyncOrchestrator] 无 MySQL+广播 页面");
        }

        // RedisOnly 写入
        if (!redisMap.isEmpty()) {
            for (Map.Entry<String, Map<String, PageRespV2>> entry : redisMap.entrySet()) {
                String pageId = entry.getKey();
                Map<String, PageRespV2> combos = entry.getValue();
                String batchId = cacheSyncService.generateBatchId();

                // 从 CacheState 获取配置版本
                String configVersion = null;
                try {
                    PageConfigModel cfg = cacheState.getPageConfig(pageId);
                    configVersion = cfg != null ? cfg.getVersion() : null;
                } catch (Exception ignore) {}

                try {
                    String result = redisCacheStorageService.savePageCache(batchId, pageId, combos, configVersion);
                    if (StringUtils.isNotBlank(result)) {
                        log.info("[CacheSyncOrchestrator] RedisOnly 写入成功: pageId={}, fields={} (x2 for V1/V2)", pageId, combos.size());
                    } else {
                        log.warn("[CacheSyncOrchestrator] RedisOnly 写入失败: pageId={}", pageId);
                    }
                } catch (Exception ex) {
                    log.error("[CacheSyncOrchestrator] RedisOnly 写入异常: pageId={}", pageId, ex);
                }
            }
        } else {
            log.info("[CacheSyncOrchestrator] 无 RedisOnly 页面");
        }
    }

    /**
     * 从组合键提取策略（格式：pageId|strategy|...）
     */
    private String extractStrategy(String combinationKey) {
        try {
            String rc = CombinationKeyParser.extractRouteCode(combinationKey);
            return rc == null || rc.trim().isEmpty() ? "all" : rc;
        } catch (Exception e) {
            return "all";
        }
    }
}
