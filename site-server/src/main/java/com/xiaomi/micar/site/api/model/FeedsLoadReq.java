package com.xiaomi.micar.site.api.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Feed流加载请求
 * 对应接口：/mtop/carsite/community/feeds/load
 *
 * <AUTHOR>
 * @since 2025/06/24
 */
@Data
public class FeedsLoadReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页面标识
     */
    private String pageId;

    /**
     * 模块标识
     */
    private String moduleId;

    /**
     * 标签ID（可选，多tab作品时使用）
     */
    private String tabId;

    /**
     * 过滤的帖子ID列表，用逗号分隔
     */
    private String filterIds;

    /**
     * 偏移量，默认0
     */
    private Integer offset;
}
