package com.xiaomi.micar.site.api.fe;

import com.xiaomi.micar.site.api.model.FeedsLoadReq;
import com.xiaomi.micar.site.api.model.GetPostDetailReq;
import com.xiaomi.micar.site.api.model.GetPostRelationReq;
import com.xiaomi.micar.site.api.model.LoadFeedsReq;
import com.xiaomi.micar.site.api.model.PostLikeReq;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 社区服务接口
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
public interface FeCommunityService {

    /**
     * 加载活动精彩瞬间
     *
     * @param request 请求参数
     * @return 返回结果
     */
    Result<?> loadActivityFeeds(LoadFeedsReq request);


    /**
     * 获取帖子点赞等关系
     *
     * @param request 请求参数
     * @return 返回结果
     */
    Result<?> getPostRelation(GetPostRelationReq request);


    /**
     * 帖子点赞
     *
     * @param request 请求参数
     * @return 返回结果
     */
    Result<Void> postLike(PostLikeReq request);

    /**
     * 帖子取消点赞
     *
     * @param request 请求参数
     * @return 返回结果
     */
    Result<Void> postCancelLike(PostLikeReq request);

    /**
     * 获取帖子详情
     * 支持批量查询帖子详情，最多20个
     *
     * @param request 请求参数，包含帖子ID列表
     * @return 返回帖子详情列表的JSON字符串（Result格式）
     */
    String getPostDetail(GetPostDetailReq request);


    /**
     * 加载Feed流数据
     * 支持专题页动态数据的分页加载，直接返回JSON字符串，无反序列化开销
     *
     * @param request 分页加载请求
     * @return JSON字符串响应
     */
    String loadFeeds(FeedsLoadReq request);
}
