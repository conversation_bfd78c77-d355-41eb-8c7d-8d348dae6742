package com.xiaomi.micar.site.config;

import com.mi.car.iccc.iccccommonutil.thread.IcccThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * 线程池配置
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Configuration
public class ThreadPoolConfig {

    public static final String EXECUTOR_EVENT_PUBLISH = "eventPublishExecutor";
    public static final String EXECUTOR_ROBOT_NOTIFY = "robotNotifyExecutor";

    @Bean(name = EXECUTOR_EVENT_PUBLISH)
    public Executor eventPublishExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("club-event-publish-");
        executor.initialize();
        return executor;
    }

    @Bean(name = EXECUTOR_ROBOT_NOTIFY)
    public ThreadPoolExecutor robotNotifyExecutor() {
        return new ThreadPoolExecutor(5,
                10,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(1000),
                IcccThreadFactory.create("club-notify-robot"));
    }

}
