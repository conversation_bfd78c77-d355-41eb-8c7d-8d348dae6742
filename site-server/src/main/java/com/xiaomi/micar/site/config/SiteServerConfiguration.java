package com.xiaomi.micar.site.config;

import com.xiaomi.micar.site.service.assembly.provider.SiteDataProviderFactory;
import com.xiaomi.micar.site.service.config.PageConfigService;
import com.xiaomi.micar.site.service.http.RouterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 站点服务器配置类
 * 负责注册从 site-component 迁移过来的服务
 *
 * <AUTHOR>
 * @since 2025/01/20
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(SiteComponentProperties.class)
public class SiteServerConfiguration {

    @Resource
    private SiteComponentConfiguration siteComponentConfiguration;

    /**
     * 注册页面配置服务
     *
     * @return 页面配置服务
     */
    @Bean
    @ConditionalOnMissingBean(PageConfigService.class)
    public PageConfigService pageConfigService() {
        log.info("Registering PageConfigService in site-server module");
        return new PageConfigService();
    }

    /**
     * 注册路由服务
     *
     * @return 路由服务
     */
    @Bean
    @ConditionalOnMissingBean(RouterService.class)
    public RouterService routerService() {
        log.info("Registering RouterService in site-server module");
        return new RouterService();
    }

    /**
     * 注册站点数据提供者工厂
     *
     * @return 站点数据提供者工厂
     */
    @Bean
    @ConditionalOnMissingBean(SiteDataProviderFactory.class)
    public SiteDataProviderFactory siteDataProviderFactory() {
        log.info("Registering SiteDataProviderFactory in site-server module");
        return siteComponentConfiguration.getSiteDataProviderFactory();
    }

}
