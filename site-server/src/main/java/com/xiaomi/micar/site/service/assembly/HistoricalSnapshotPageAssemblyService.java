package com.xiaomi.micar.site.service.assembly;

import com.xiaomi.micar.site.core.monitoring.CacheMonitoring;
import com.xiaomi.micar.site.core.monitoring.CacheMonitoringEnhancer;
import com.xiaomi.micar.site.cache.service.MemoryCacheManager;
import com.xiaomi.micar.site.cache.util.HistoricalSnapshotCacheKeyGenerator;
import com.xiaomi.micar.site.config.NacosSiteBizConfig;
import com.xiaomi.micar.site.model.RnVersionRequest;
import com.xiaomi.micar.site.service.UserStrategyService;
import com.xiaomi.micar.site.route.RouteContext;
import com.xiaomi.micar.site.route.RouteContextBuilder;
import com.xiaomi.micar.site.utils.CombinationKeyGenerate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 集成历史快照功能的页面组装服务
 * 支持基于客户端版本的历史快照缓存查询，提供向后兼容性
 */
@Slf4j
@Service
public class HistoricalSnapshotPageAssemblyService {

    @Resource
    private SitePageAssemblyService originalAssemblyService;

    @Resource
    private UserStrategyService userStrategyService;

    @Resource
    private MemoryCacheManager memoryCacheManager;

    @Resource
    private NacosSiteBizConfig nacosSiteBizConfig;

    @Resource
    private CacheMonitoringEnhancer monitoringEnhancer;

    @Resource
    private com.xiaomi.micar.site.route.RouteCacheFetcher routeCacheFetcher;

    @Resource
    private RouteContextBuilder routeContextBuilder;


    /**
     * 支持历史快照的页面数据获取接口
     *
     * @param request 页面数据请求
     * @param version API版本 (1=V1, 2=V2)
     * @param userId  用户ID
     * @return JSON格式的页面响应
     */
    @CacheMonitoring(
            value = "historical_snapshot_query",
            type = CacheMonitoring.MonitoringType.BUSINESS,
            recordResponseSize = true,
            tags = {"operation=query", "service=historical_snapshot"}
    )
    public String getPageDataV2Enhanced(RnVersionRequest request, int version, String userId) {
        String pageId = request.getPageId();
        String rnVersion = request.getRnVersion();

        // 记录RN版本分布
        if (StringUtils.isNotBlank(rnVersion)) {
            monitoringEnhancer.recordVersionDistribution(rnVersion, "service=historical_snapshot");
        }

        // 1. 解析用户在各维度的命中值（由 site-up 统一产出，再结合页面开关适配）
        RouteContext routeContext = userStrategyService.determineRouteContextWithPageCheck(pageId, new HashMap<>(request.getQueryParams()), userId);


        // 2. 白名单用户直接走最新的逻辑不走快照
        if (nacosSiteBizConfig.isWhiteUser(userId)) {
            log.info("用户白名单，使用最新逻辑: userId={}", userId);
            // 白名单：直接走最新缓存读取（基于 RouteContext 候选生成）
            String latest = routeCacheFetcher.fetch(pageId, version, routeContext);
            if (StringUtils.isNotBlank(latest)) {
                return latest;
            }
            throw new RuntimeException(String.format("页面缓存未找到: pageId=%s", pageId));
        }
        // 3. 如果没有提供RN版本，使用原有逻辑
        if (StringUtils.isBlank(rnVersion)) {
            String latest = routeCacheFetcher.fetch(pageId, version, routeContext);
            if (StringUtils.isNotBlank(latest)) {
                return latest;
            }
            throw new RuntimeException(String.format("页面缓存未找到: pageId=%s", pageId));
        }
        // 4. 直接从内存缓存获取历史快照响应
        // 主人群仅用于历史快照键（最新逻辑不再依赖 seed routeCode）
        String userGroup = pickPrimaryStrategy(routeContext);
        String historicalResponse = memoryCacheManager.getHistoricalSnapshotResponse(
                HistoricalSnapshotCacheKeyGenerator.generateForQuery(
                        CombinationKeyGenerate.generateCombinationKey(pageId, userGroup),
                        rnVersion, version));
        if (StringUtils.isNotBlank(historicalResponse)) {
            return historicalResponse;
        }
        // 5. 历史缓存未命中，降级使用最新缓存
        String latest = routeCacheFetcher.fetch(pageId, version, routeContext);
        if (StringUtils.isNotBlank(latest)) {
            return latest;
        }
        throw new RuntimeException(String.format("页面缓存未找到: pageId=%s", pageId));
    }

    private String pickPrimaryStrategy(RouteContext ctx) {
        java.util.List<String> list = ctx == null ? java.util.Collections.emptyList() : ctx.valuesOf("UserStrategyEnum");
        if (list.contains("car_owner")) return "car_owner";
        if (list.contains("car_share")) return "car_share";
        if (list.contains("car_locked")) return "car_locked";
        return "all";
    }

}
