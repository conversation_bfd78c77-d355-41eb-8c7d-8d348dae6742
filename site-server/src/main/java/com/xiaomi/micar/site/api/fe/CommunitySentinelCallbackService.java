package com.xiaomi.micar.site.api.fe;

import com.xiaomi.micar.site.api.model.FeedsLoadReq;
import com.xiaomi.micar.site.api.model.GetPostDetailReq;
import com.xiaomi.micar.site.api.model.GetPostRelationReq;
import com.xiaomi.micar.site.api.model.LoadFeedsReq;
import com.xiaomi.micar.site.api.model.PostLikeReq;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Slf4j
public class CommunitySentinelCallbackService implements FeCommunityService {
    @Override
    public Result<?> loadActivityFeeds(LoadFeedsReq request) {
        log.warn("CommunitySentinelCallbackService#loadActivityFeeds hit mid:{}", request.getMid());
        return Result.fail(GeneralCodes.ServerIsBuzy, "");
    }

    @Override
    public Result<?> getPostRelation(GetPostRelationReq request) {
        log.warn("CommunitySentinelCallbackService#getPostRelation hit mid:{}", request.getMid());
        return Result.fail(GeneralCodes.ServerIsBuzy, "");
    }

    @Override
    public Result<Void> postLike(PostLikeReq request) {
        log.warn("CommunitySentinelCallbackService#postLike hit mid:{}", request.getMid());
        return Result.fail(GeneralCodes.ServerIsBuzy, "");
    }

    @Override
    public Result<Void> postCancelLike(PostLikeReq request) {
        log.warn("CommunitySentinelCallbackService#postCancelLike hit mid:{}", request.getMid());
        return Result.fail(GeneralCodes.ServerIsBuzy, "");
    }

    @Override
    public String getPostDetail(GetPostDetailReq request) {
        log.warn("CommunitySentinelCallbackService#getPostDetail hit mid:{}", request.getMid());
        // 返回服务繁忙的JSON字符串
        return "{\"code\":500,\"message\":\"服务繁忙，请稍后重试\",\"success\":false,\"data\":[]}";
    }

    @Override
    public String loadFeeds(FeedsLoadReq request) {
        log.warn("CommunitySentinelCallbackService#loadFeeds hit mid:{}", request.getPageId());
        return String.format("{\"code\":0,\"success\":true,\"data\":{\"offset\":%d,\"hasMore\":false,\"records\":[]}}", request.getOffset());
    }
}
