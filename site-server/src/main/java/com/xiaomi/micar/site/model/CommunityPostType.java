package com.xiaomi.micar.site.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 社区帖子类型
 */
@AllArgsConstructor
@Getter
public enum CommunityPostType {

    VIDEO_ARTICLE(1, "视频"), // 带有视频信息
    SHORT_ARTICLE(3, "动态"), // (图片+文字)
    LONG_ARTICLE(4, "文章"), // 带图片的富文本
    ;

    private final Integer code;
    private final String desc;

    public static CommunityPostType getByCode(Integer code) {
        for (CommunityPostType type : CommunityPostType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
