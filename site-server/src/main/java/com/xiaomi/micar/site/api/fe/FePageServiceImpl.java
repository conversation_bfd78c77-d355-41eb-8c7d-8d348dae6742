package com.xiaomi.micar.site.api.fe;

import com.fasterxml.jackson.databind.JsonNode;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.api.model.FeedsLoadReq;
import com.xiaomi.micar.site.model.RnVersionRequest;
import com.xiaomi.micar.site.metrics.aop.SiteMetrics;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.service.FeedsLoadService;
import com.xiaomi.micar.site.service.PagePreviewService;
import com.xiaomi.micar.site.service.assembly.HistoricalSnapshotPageAssemblyService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.xiaomi.micar.site.api.fe.GatewayConstants.HTTP_GET_PARAM;
import static com.xiaomi.micar.site.api.fe.GatewayConstants.PARAM_UID;
import static com.xiaomi.micar.site.api.fe.GatewayConstants.RN_VERSION;

/**
 * 页面服务接口
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Slf4j
@ApiModule(value = "页面服务接口", apiInterface = FePageService.class)
@DubboService(group = "${dubbo.group:}", registry = "carlifeRegistry")
public class FePageServiceImpl implements FePageService {

    @Resource
    private HistoricalSnapshotPageAssemblyService historicalSnapshotPageAssemblyService;
    @Resource
    private FePageServiceImpl self;

    @Resource
    private PagePreviewService pagePreviewService;


    @Override
    public String pageData(Map<String, String> request) {
        return self.pageDataV2Optimized(request, 1);
    }

    @Override
    public String pageDataV2Optimized(Map<String, String> request) {
        return self.pageDataV2Optimized(request, 2);
    }

    @Override
    public String topicPageDataV2Optimized(Map<String, String> request) {
        return self.pageDataV2Optimized(request, 2);
    }

    @SiteMetrics(type = "page", pageId = "#pageId")
    public String pageDataV2Optimized(Map<String, String> request, int version) {
        String uid = RpcContext.getContext().getAttachment(PARAM_UID);

        String strQueryParams = request.get(HTTP_GET_PARAM);
        JsonNode queryParams = StringUtils.isEmpty(strQueryParams) ? null : JsonUtil.readTree(strQueryParams);
        String pageId = Optional.ofNullable(queryParams).map(jsonNode -> jsonNode.get("pageId")).map(JsonNode::asText).orElse("explore");
        String cv = Optional.ofNullable(queryParams)
                .map(jsonNode -> jsonNode.get("cv"))
                .map(JsonNode::asText)
                .orElse(null);
        // 处理特定版本预览请求
        if (StringUtils.isNotEmpty(cv)) {
            Result<PageRespV2> result = pagePreviewService.handleSpecificVersionPreview(uid, cv, pageId);
            return JsonUtil.toJSONString(result);
        }
        log.info("pageId:{}, uid={}", pageId, uid);
        RnVersionRequest enhancedRequest = createPageDataV2Request(pageId, uid, queryParams);
        return historicalSnapshotPageAssemblyService.getPageDataV2Enhanced(enhancedRequest, version, uid);
    }

    /**
     * 创建 PageDataV2Request 对象
     */
    private RnVersionRequest createPageDataV2Request(String pageId, String uid, JsonNode queryParams) {
        RnVersionRequest enhancedRequest = new RnVersionRequest();
        enhancedRequest.setPageId(pageId);
        enhancedRequest.setMid(uid);

        // 提取RN版本信息
        String rnVersion = Optional.ofNullable(queryParams)
                .map(jsonNode -> jsonNode.get("rnVersion"))
                .map(JsonNode::asText)
                .orElse(null);

        if (StringUtils.isBlank(rnVersion)) {
            enhancedRequest.setRnVersion(RpcContext.getContext().getAttachment(RN_VERSION));
        } else {
            enhancedRequest.setRnVersion(rnVersion);
        }

        // 设置查询参数
        Map<String, String> queryParamsMap = new HashMap<>();
        if (queryParams != null) {
            queryParams.fields().forEachRemaining(entry -> {
                queryParamsMap.put(entry.getKey(), entry.getValue().asText());
            });
        }
        enhancedRequest.setQueryParams(queryParamsMap);

        return enhancedRequest;
    }

}
