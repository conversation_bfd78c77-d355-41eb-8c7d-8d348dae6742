package com.xiaomi.micar.site.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 官方动态数据响应模型
 */
@Data
public class OfficeNewsResponse {
    private Integer code;
    private String message;
    private OfficeNewsData data;

    /**
     * 官方动态数据内容
     */
    @Data
    public static class OfficeNewsData {
        private Integer limit;
        private String after;
        private List<OfficeNewsRecord> records;
    }

    /**
     * 官方动态记录
     */
    @Data
    public static class OfficeNewsRecord {
        private String postId;
        private Author author;

        /**
         * 帖子类型
         * {@link com.mi.car.iccc.starter.site.enums.CommunityPostType}
         */
        private Integer type;
        private List<SummaryItem> summary;
        private String displaySummary;
        private String title;
        /**
         * 封面
         */
        private ImageItem feedCover;
        /**
         * 图片列表
         */
        private List<ImageItem> imgList;
        /**
         * 视频列表
         */
        private List<VideoInfo> videoList = new ArrayList<>();
        private Boolean like;
        private Integer likeCnt;
        private Integer commentCnt;
        private Long createTime;
        private String deltaText;
        private String tag;
        private String docType;
        private Integer follow;

        /**
         * 浏览次数
         */
        private Integer viewCount;

        /**
         * 话题列表（新接口字段）
         */
        private List<TopicItem> topicList;
    }

    /**
     * 作者信息
     */
    @Data
    public static class Author {
        @JsonProperty("eUserId")
        private String eUserId;
        private String userName;
        private String icon;
        private Boolean isEmployee;
        private PinBadge pinBadge;

        /**
         * 身份标识列表（新接口字段）
         */
        private List<IdentityItem> identityList;

        /**
         * 头像框（新接口字段）
         */
        private HeaderFrame headerFrame;
    }

    /**
     * 徽章信息
     */
    @Data
    public static class PinBadge {
        private Integer badgeId;
        private String img;
    }

    /**
     * 摘要项
     */
    @Data
    public static class SummaryItem {
        private String type;
        private String txt;
    }

    /**
     * 图片项
     */
    @Data
    public static class ImageItem {
        private String imageUrl;
        private Integer height;
        private Integer width;
    }


    /**
     * 视频信息
     */
    @Data
    @NoArgsConstructor
    public static class VideoInfo {
        /**
         * 封面URL
         */
        private String cover;

        /**
         * 时长
         */
        private Long duration;

        /**
         * 图片高度
         */
        private Integer height;

        /**
         * 图片宽度
         */
        private Integer width;
    }

    /**
     * 身份标识项
     */
    @Data
    public static class IdentityItem {
        private String url;
        private String darkUrl;
        private String carModel;
        private Integer status;
    }

    /**
     * 头像框
     */
    @Data
    public static class HeaderFrame {
        private String frameUrl;
        private String darkFrameUrl;
    }

    /**
     * 话题项
     */
    @Data
    public static class TopicItem {
        private String topicId;
        private String topicName;
    }
}
