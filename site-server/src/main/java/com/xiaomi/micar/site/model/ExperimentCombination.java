package com.xiaomi.micar.site.model;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 实验组合类，用于封装实验组合信息.
 * 在页面缓存上下文中使用，方便页面响应构建器访问实验组合信息.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@NoArgsConstructor
public class ExperimentCombination {

    /**
     * 应用的实验列表，每个实验为一个Map，包含实验ID和分桶ID
     */
    private List<Map<String, String>> applicableExperiments = new ArrayList<>();

    /**
     * 构造函数
     *
     * @param applicableExperiments 适用的实验列表
     */
    public ExperimentCombination(List<Map<String, String>> applicableExperiments) {
        this.applicableExperiments = applicableExperiments != null 
                ? applicableExperiments 
                : new ArrayList<>();
    }

    /**
     * 检查是否包含任何实验
     *
     * @return 如果包含任何实验则返回true
     */
    public boolean isEmpty() {
        return applicableExperiments == null || applicableExperiments.isEmpty();
    }

    /**
     * 获取实验组合的大小（实验数量）
     *
     * @return 实验数量
     */
    public int size() {
        return applicableExperiments == null ? 0 : applicableExperiments.size();
    }

    @Override
    public String toString() {
        if (isEmpty()) {
            return "[]";
        }
        return applicableExperiments.toString();
    }
}
