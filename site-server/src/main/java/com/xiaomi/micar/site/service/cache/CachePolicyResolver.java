package com.xiaomi.micar.site.service.cache;

import com.xiaomi.micar.site.cache.policy.CachePolicy;
import com.xiaomi.micar.site.config.CachePolicyProperties;
import com.xiaomi.micar.site.config.PagePolicyRule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/4
 */
@Component
public class CachePolicyResolver {

    @Resource
    private CachePolicyProperties properties;

    /**
     * 按优先级解析：精确(pageId+strategy) > pageId+all > defaultPolicy
     */
    public CachePolicy resolve(String pageId, String strategy) {
        String normPageId = StringUtils.trimToEmpty(pageId);
        String normStrategy = StringUtils.isBlank(strategy) ? "all" : strategy.trim();

        List<PagePolicyRule> rules = properties.getRules();
        if (rules != null && !rules.isEmpty()) {
            // 1) 精确匹配 pageId+strategy
            for (PagePolicyRule r : rules) {
                if (normPageId.equals(StringUtils.trimToEmpty(r.getPageId()))
                        && normStrategy.equals(StringUtils.trimToEmpty(r.getStrategy()))) {
                    return nonNullOrDefault(r.getPolicy());
                }
            }
            // 2) 匹配 pageId+all
            for (PagePolicyRule r : rules) {
                if (normPageId.equals(StringUtils.trimToEmpty(r.getPageId()))
                        && "all".equalsIgnoreCase(StringUtils.trimToEmpty(r.getStrategy()))) {
                    return nonNullOrDefault(r.getPolicy());
                }
            }
        }

        // 3) 默认策略
        return nonNullOrDefault(properties.getDefaultPolicy());
    }

    private CachePolicy nonNullOrDefault(CachePolicy policy) {
        return policy == null ? CachePolicy.MYSQL_BROADCAST_MEMORY : policy;
    }
}
