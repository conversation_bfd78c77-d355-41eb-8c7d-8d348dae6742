package com.xiaomi.micar.site.model;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 响应生成上下文
 * 封装了响应生成过程中需要的上下文参数，避免方法参数过多
 */
@Data
public class ResponseContext {
    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 用户组ID
     */
    private String groupId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户属性
     */
    private Map<String, Object> queryParams;

    /**
     * 页面配置模型
     */
    private PageConfigModel pageConfig;

    /**
     * 适用的实验组合
     */
    private List<Map<String, String>> applicableExperiments;

    /**
     * 页面响应
     */
    private PageRespV2 pageResp;
    /**
     * 是否为白名单用户
     */
    private boolean isWhiteUser;

    /**
     * 组件处理标记，用于避免同一个页面的不同人群中重复处理同一个组件
     * 在页面级别共享，确保同一个组件在整个页面构建过程中只处理一次
     */
    private Set<String> processedComponents;

    /**
     * 创建上下文对象（包含实验信息）
     *
     * @param builder 响应上下文构建器
     * @return 响应上下文对象
     */
    public static ResponseContext create(Builder builder) {
        ResponseContext context = new ResponseContext();
        context.pageId = builder.pageId;
        context.groupId = builder.groupId;
        context.userId = builder.userId;
        context.queryParams = builder.queryParams;
        context.pageConfig = builder.pageConfig;
        context.isWhiteUser = builder.isWhiteUser;
        context.applicableExperiments = builder.applicableExperiments;

        // 初始化组件处理标记
        context.processedComponents = builder.processedComponents != null
            ? builder.processedComponents
            : new HashSet<>();
        
        // 初始化页面结构
        context.pageResp = new PageRespV2();
        // 使用公共方法正确设置属性值
        if (context.pageId != null) {
            context.pageResp.setPageId(context.pageId);
        }
        if (context.pageResp.getModules() == null) {
            context.pageResp.setModules(Lists.newArrayList());
        }

        return context;
    }
    
    /**
     * 响应上下文构建器
     */
    public static class Builder {
        private String pageId;
        private String groupId;
        private String userId;
        private boolean isWhiteUser;
        private Map<String, Object> queryParams;
        private PageConfigModel pageConfig;
        private List<Map<String, String>> applicableExperiments;
        private Set<String> processedComponents;
        
        /**
         * 设置页面ID
         *
         * @param pageId 页面ID
         * @return 构建器实例
         */
        public Builder pageId(String pageId) {
            this.pageId = pageId;
            return this;
        }
        
        /**
         * 设置用户组ID
         *
         * @param groupId 用户组ID
         * @return 构建器实例
         */
        public Builder groupId(String groupId) {
            this.groupId = groupId;
            return this;
        }
        
        /**
         * 设置用户ID
         *
         * @param userId 用户ID
         * @return 构建器实例
         */
        public Builder userId(String userId) {
            this.userId = userId;
            return this;
        }
        
        /**
         * 设置查询参数
         *
         * @param queryParams 查询参数
         * @return 构建器实例
         */
        public Builder queryParams(Map<String, Object> queryParams) {
            this.queryParams = queryParams;
            return this;
        }
        
        /**
         * 设置页面配置模型
         *
         * @param pageConfig 页面配置模型
         * @return 构建器实例
         */
        public Builder pageConfig(PageConfigModel pageConfig) {
            this.pageConfig = pageConfig;
            return this;
        }
        
        /**
         * 设置适用的实验组合
         *
         * @param applicableExperiments 适用的实验组合
         * @return 构建器实例
         */
        public Builder applicableExperiments(List<Map<String, String>> applicableExperiments) {
            this.applicableExperiments = applicableExperiments;
            return this;
        }
        
        /**
         * 构建响应上下文对象
         *
         * @return 响应上下文对象
         */
        public ResponseContext build() {
            return ResponseContext.create(this);
        }

        /**
         * 设置是否为白名单用户
         *
         * @param isWhiteUser 是否为白名单用户
         * @return 构建器实例
         */
        public Builder isWhiteUser(boolean isWhiteUser) {
            this.isWhiteUser = isWhiteUser;
            return this;
        }

        /**
         * 设置组件处理标记
         *
         * @param processedComponents 组件处理标记
         * @return 构建器实例
         */
        public Builder processedComponents(Set<String> processedComponents) {
            this.processedComponents = processedComponents;
            return this;
        }
    }
}
