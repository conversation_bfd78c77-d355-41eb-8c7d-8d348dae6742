package com.xiaomi.micar.site.event;

import com.xiaomi.micar.site.service.assembly.PageCachePreloadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 缓存刷新事件监听器.
 *
 * <AUTHOR>
 * @date 2025/3/15
 */
@Slf4j
@Component
public final class CacheRefreshEventListener {

    @Resource
    private PageCachePreloadService pageCachePreloadService;

    /**
     * 处理缓存刷新事件
     *
     * @param event 缓存刷新事件
     */
    @EventListener
    public void handleCacheRefreshEvent(final CacheRefreshEvent event) {
        log.info("Received cache refresh event, refreshing cache");
        pageCachePreloadService.preloadAllPageCombinations();
    }
} 
