package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.enums.ComponentType;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.component.Component;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 组件数据提供者抽象基类
 * 提供通用功能实现，包括错误处理、日志记录和重试机制
 */
@Slf4j
public abstract class AbstractComponentDataProvider implements ComponentDataProvider {

    // 请求ID生成时的随机数上限
    private static final int REQUEST_ID_RANDOM_BOUND = 10000;

    // 最大重试次数
    private static final int MAX_RETRY_ATTEMPTS = 3;

    // 重试间隔（毫秒）
    private static final long RETRY_DELAY_MS = 1000;

    // 持久化缓存，用于存储最后一次成功的响应
    // 键为缓存键，值为响应数据
    private final ConcurrentHashMap<String, Object> lastSuccessfulResponses = new ConcurrentHashMap<>();

    @Override
    public void process(ResponseContext context, Component componentInfo) {
        if (componentInfo == null) {
            log.error("组件信息为空");
            return;
        }

        // 添加结构化日志上下文
        try (MDC.MDCCloseable ignored1 = MDC.putCloseable("componentId", ComponentType.fromComponentClass(componentInfo.getClass()).getCode());
             MDC.MDCCloseable ignored3 = MDC.putCloseable("userId", context.getUserId());
             MDC.MDCCloseable ignored4 = MDC.putCloseable("componentClass", componentInfo.getClass().getSimpleName())) {

            log.info("开始处理组件数据,组件类型: {}", componentInfo.getClass().getSimpleName());

            // 验证组件类型
            if (!validateComponentType(componentInfo)) {
                log.error("组件类型不匹配，实际: {}",
                        componentInfo.getClass().getSimpleName());
                return;
            }

            // 执行具体的处理逻辑
            try {
                doProcess(context, componentInfo);
                log.info("组件数据处理成功, 组件类型: {}", componentInfo.getClass().getSimpleName());
            } catch (Exception e) {
                log.error("组件数据处理失败, 组件类型: {}, 错误: {}", componentInfo.getClass().getSimpleName(), e.getMessage(), e);
                // 处理失败时，可以设置默认数据或执行其他恢复操作
                handleProcessingError(componentInfo, e);
            }
        }
    }

    /**
     * 验证组件类型是否匹配
     *
     * @param componentInfo 组件信息
     * @return 是否匹配
     */
    protected abstract boolean validateComponentType(Component componentInfo);

    /**
     * 执行具体的处理逻辑
     *
     * @param context 响应上下文
     * @param componentInfo 组件信息
     */
    protected abstract void doProcess(ResponseContext context, Component componentInfo);

    /**
     * 处理处理过程中的错误
     *
     * @param componentInfo 组件信息
     * @param e 异常
     */
    protected abstract void handleProcessingError(Component componentInfo, Exception e);

    /**
     * 使用重试机制执行操作，启用缓存
     *
     * @param cacheKey 缓存键，用于存储成功结果
     * @param supplier 操作供应商
     * @param <T> 返回类型
     * @return 操作结果
     */
    protected <T> T executeWithRetry(String cacheKey, Supplier<T> supplier) {
        return executeWithRetry(cacheKey, supplier, true);
    }

    /**
     * 使用重试机制执行操作，可以控制是否启用缓存
     *
     * @param cacheKey 缓存键，用于存储成功结果
     * @param supplier 操作供应商
     * @param useCache 是否启用缓存，如果为false，则不会将结果存入缓存，也不会从缓存中获取数据
     * @param <T> 返回类型
     * @return 操作结果
     */
    protected <T> T executeWithRetry(String cacheKey, Supplier<T> supplier, boolean useCache) {
        // 生成请求ID
        String requestId = "RT-" + System.currentTimeMillis() + "-"
                + (int) (Math.random() * REQUEST_ID_RANDOM_BOUND);

        log.debug("[{}] 开始执行操作，缓存键: {}, 启用缓存: {}", requestId, cacheKey, useCache);

        // 重试计数器
        int attempts = 0;
        Exception lastException = null;

        // 重试循环
        while (attempts < MAX_RETRY_ATTEMPTS) {
            attempts++;
            try {
                // 执行操作并处理错误，如果启用缓存则缓存成功的响应
                T result = executeWithErrorHandlingAndCache(cacheKey, supplier, requestId, useCache);
                log.debug("[{}] 操作执行成功，尝试次数: {}", requestId, attempts);
                return result;
            } catch (Exception e) {
                lastException = e;

                log.warn("[{}] 操作执行失败，尝试次数: {}/{}，错误: {}",
                        requestId, attempts, MAX_RETRY_ATTEMPTS, e.getMessage());

                // 如果还有重试机会，等待一段时间后重试
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试操作被中断", ie);
                    }
                }
            }
        }

        // 如果启用缓存，尝试使用缓存数据
        if (useCache) {
            log.error("[{}] 达到最大重试次数 {}，操作最终失败，尝试使用缓存数据，缓存键: {}",
                    requestId, MAX_RETRY_ATTEMPTS, cacheKey);

            // 从持久化缓存获取数据
            @SuppressWarnings("unchecked")
            T cachedData = (T) lastSuccessfulResponses.get(cacheKey);
            if (cachedData != null) {
                log.info("[{}] 所有重试都失败，返回缓存数据，缓存键: {}", requestId, cacheKey);
                return cachedData;
            }
        } else {
            log.error("[{}] 达到最大重试次数 {}，操作最终失败，缓存未启用，缓存键: {}",
                    requestId, MAX_RETRY_ATTEMPTS, cacheKey);
        }

        // 如果没有缓存数据可用或缓存未启用，则抛出异常
        log.error("[{}] 无可用缓存数据或缓存未启用，操作彻底失败，缓存键: {}", requestId, cacheKey);
        if (lastException != null) {
            throw new RuntimeException("操作执行失败，已重试 " + MAX_RETRY_ATTEMPTS + " 次", lastException);
        } else {
            throw new RuntimeException("操作执行失败，已重试 " + MAX_RETRY_ATTEMPTS + " 次");
        }
    }

    /**
     * 执行操作并处理错误，如果启用缓存则缓存成功的响应
     *
     * @param cacheKey 缓存键
     * @param supplier 操作供应商
     * @param requestId 请求ID
     * @param useCache 是否启用缓存
     * @param <T> 返回类型
     * @return 操作结果
     */
    private <T> T executeWithErrorHandlingAndCache(String cacheKey, Supplier<T> supplier, String requestId, boolean useCache) {
        try {
            log.debug("[{}] 开始执行操作，缓存键: {}, 启用缓存: {}", requestId, cacheKey, useCache);
            long startTime = System.currentTimeMillis();

            T result = supplier.get();

            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 操作执行完成，耗时: {}ms, 缓存键: {}", requestId, duration, cacheKey);

            // 如果启用缓存，则缓存成功的响应
            if (useCache && result != null) {
                lastSuccessfulResponses.put(cacheKey, result);
                log.debug("[{}] 缓存成功的响应，缓存键: {}", requestId, cacheKey);
            } else if (result == null) {
                log.warn("[{}] 操作返回结果为空，未缓存，缓存键: {}", requestId, cacheKey);
            } else {
                log.debug("[{}] 缓存未启用，不缓存响应，缓存键: {}", requestId, cacheKey);
            }
            return result;
        } catch (Exception e) {
            log.error("[{}] 执行操作失败: {}, 缓存键: {}", requestId, e.getMessage(), cacheKey, e);
            throw e;
        }
    }

    /**
     * 执行操作并处理错误，同时缓存成功的响应
     *
     * @param cacheKey 缓存键
     * @param supplier 操作供应商
     * @param requestId 请求ID
     * @param <T> 返回类型
     * @return 操作结果
     */
    private <T> T executeWithErrorHandlingAndCache(String cacheKey, Supplier<T> supplier, String requestId) {
        return executeWithErrorHandlingAndCache(cacheKey, supplier, requestId, true);
    }

    /**
     * 生成缓存键
     *
     * @param prefix 前缀
     * @param params 参数
     * @return 缓存键
     */
    protected String generateCacheKey(String prefix, Object... params) {
        StringBuilder keyBuilder = new StringBuilder(prefix);

        for (Object param : params) {
            if (param != null) {
                keyBuilder.append(":").append(param);
            }
        }

        return keyBuilder.toString();
    }

    /**
     * 从查询参数中获取整数参数
     *
     * @param queryParams 查询参数
     * @param paramName 参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    protected Integer getIntParam(Map<String, Object> queryParams, String paramName, Integer defaultValue) {
        if (queryParams != null && queryParams.containsKey(paramName)) {
            Object paramObj = queryParams.get(paramName);
            if (paramObj instanceof Integer) {
                return (Integer) paramObj;
            } else if (paramObj instanceof String) {
                try {
                    return Integer.parseInt((String) paramObj);
                } catch (NumberFormatException e) {
                    log.warn("参数{}解析失败: {}", paramName, paramObj);
                }
            }
        }
        return defaultValue;
    }

}
