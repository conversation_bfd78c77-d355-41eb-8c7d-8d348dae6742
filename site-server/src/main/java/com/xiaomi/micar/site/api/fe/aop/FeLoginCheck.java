package com.xiaomi.micar.site.api.fe.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用户检查注解
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FeLoginCheck {

    /**
     * 字段名
     *
     * @return 字段名
     */
    String field() default "mid";

    /**
     * 是否必须登录
     *
     * @return 是否必须登录
     */
    boolean required() default true;


}
