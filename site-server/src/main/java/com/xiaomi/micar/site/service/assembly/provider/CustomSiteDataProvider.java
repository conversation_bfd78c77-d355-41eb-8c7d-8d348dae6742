package com.xiaomi.micar.site.service.assembly.provider;

import com.xiaomi.micar.site.model.config.SiteExpBuckets;
import com.xiaomi.micar.site.model.config.SiteExpConfig;
import com.xiaomi.micar.site.model.config.SitePageConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 自定义站点数据提供者
 * 提供一个接口让使用方实现自定义数据加载逻辑
 */
@Slf4j
public class CustomSiteDataProvider implements SiteDataProvider {

    /**
     * 自定义数据加载器接口
     * 使用方可以实现此接口来提供自定义的数据加载逻辑
     */
    public interface DataLoader {
        /**
         * 加载页面配置
         * 
         * @return 页面配置列表
         */
        List<SitePageConfig> loadPageConfigs();

        /**
         * 加载实验
         * 
         * @return 实验列表
         */
        List<SiteExpConfig> loadExperiments();

        /**
         * 加载实验桶
         * 
         * @return 实验桶列表
         */
        List<SiteExpBuckets> loadExperimentBuckets();

    }

    private DataLoader dataLoader;

    /**
     * 构造函数
     * @param dataLoader 自定义数据加载器
     */
    public CustomSiteDataProvider(DataLoader dataLoader) {
        this.dataLoader = dataLoader;
        if (dataLoader != null) {
            log.info("Initialized custom site data provider with loader: {}", dataLoader.getClass().getName());
        } else {
            log.info("Initialized custom site data provider without loader");
        }
    }

    @Override
    public List<SitePageConfig> getAllPageConfigs() {
        return Optional.ofNullable(dataLoader)
                .map(DataLoader::loadPageConfigs)
                .orElseGet(() -> {
                    log.warn("No custom data loader provided for page configs");
                    return new ArrayList<>();
                });
    }

    @Override
    public List<SiteExpConfig> getAllExperiments() {
        return Optional.ofNullable(dataLoader)
                .map(DataLoader::loadExperiments)
                .orElseGet(() -> {
                    log.warn("No custom data loader provided for experiments");
                    return new ArrayList<>();
                });
    }

    @Override
    public List<SiteExpBuckets> getAllExperimentBuckets() {
        return Optional.ofNullable(dataLoader)
                .map(DataLoader::loadExperimentBuckets)
                .orElseGet(() -> {
                    log.warn("No custom data loader provided for experiment buckets");
                    return new ArrayList<>();
                });
    }
}
