package com.xiaomi.micar.site.service;

import com.xiaomi.micar.site.api.model.FeedsLoadReq;
import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.model.PostDetailWithAuthorResponse;
import com.xiaomi.micar.site.convert.CommunityDataConverter;
import com.xiaomi.micar.site.service.http.RouterService;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Feed流加载服务
 * 处理专题页动态数据的分页加载
 *
 * <AUTHOR>
 * @since 2025/06/24
 */
@Slf4j
@Service
public class FeedsLoadService {

    @Resource
    private CommunityPostCacheService communityPostCacheService;

    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    @Resource
    private RouterService routerService;

    /**
     * 加载Feed流数据 - 返回JSON字符串
     */
    public String loadFeeds(FeedsLoadReq request) {
        try {
            String paginationCacheKey = generatePaginationCacheKey(request);

            log.info("生成分页缓存键: {}", paginationCacheKey);

            String jsonResponse = getJsonResponseFromCache(paginationCacheKey, request);

            if (StringUtils.isNotBlank(jsonResponse)) {
                log.info("从分页缓存中获取到JSON响应: pageId={}, moduleId={}, tabId={}",
                        request.getPageId(), request.getModuleId(), request.getTabId());
                return jsonResponse;
            }

            log.info("分页缓存中没有数据: pageId={}, moduleId={}, tabId={}",
                    request.getPageId(), request.getModuleId(), request.getTabId());
            return createEmptyJsonResponse(request.getOffset());

        } catch (Exception e) {
            log.error("加载Feed流数据异常", e);
            return createErrorJsonResponse(e.getMessage());
        }
    }

    /**
     * 生成分页缓存key（不包含 dataSnapshot）
     */
    private String generatePaginationCacheKey(FeedsLoadReq request) {
        String pageId = request.getPageId();
        String moduleId = request.getModuleId();
        String tabId = request.getTabId();

        if (StringUtils.isNotBlank(tabId)) {
            return String.format("pagination:%s:%s:%s:news", pageId, moduleId, tabId);
        } else {
            return String.format("pagination:%s:%s:news", pageId, moduleId);
        }
    }

    /**
     * 从分页缓存中获取JSON响应（新缓存策略）
     * 从Redis String中获取帖子ID列表，按需分页获取详情
     */
    private String getJsonResponseFromCache(String cacheKey, FeedsLoadReq request) {
        try {
            // 1. 从Redis String中获取完整的帖子ID列表
            List<String> allPostIds = communityPostCacheService.getCachedPostIdList(cacheKey);

            if (CollectionUtils.isEmpty(allPostIds)) {
                log.info("缓存中没有找到帖子ID列表，缓存key: {}", cacheKey);
                return null;
            }

            // 2. 在内存中进行分页处理
            int offset = request.getOffset() != null ? request.getOffset() : 0;
            int limit = 10; // 默认每页10条

            List<String> pagedPostIds = getPagedPostIds(allPostIds, offset, limit);

            if (CollectionUtils.isEmpty(pagedPostIds)) {
                log.info("分页后没有帖子ID，offset: {}, limit: {}", offset, limit);
                return null;
            }

            // 3. 只对当前页的帖子ID调用API获取详情
            List<ArticleElement> articles = getArticlesFromAPI(pagedPostIds);

            if (CollectionUtils.isEmpty(articles)) {
                log.info("API返回空文章列表，postIds: {}", pagedPostIds);
                return null;
            }

            // 4. 应用filterIds过滤（在最后过滤）
            List<ArticleElement> filteredArticles = applyFilterIdsToArticles(articles, request.getFilterIds());

            if (CollectionUtils.isEmpty(filteredArticles)) {
                log.info("过滤后没有文章，filterIds: {}", request.getFilterIds());
                return null;
            }

            // 5. 判断是否还有更多数据
            boolean hasMore = (offset + limit) < allPostIds.size();

            // 6. 构建JSON响应
            return buildJsonResponseFromArticles(filteredArticles, offset, limit, hasMore);

        } catch (Exception e) {
            log.error("从分页缓存获取JSON响应异常，缓存key: {}", cacheKey, e);
            return null;
        }
    }

    /**
     * 创建空的JSON响应
     */
    private String createEmptyJsonResponse(Integer offset) {
        int actualOffset = offset != null ? offset : 0;
        return String.format("{\"code\":0,\"success\":true,\"data\":{\"offset\":%d,\"hasMore\":false,\"records\":[]}}",
                actualOffset);
    }

    /**
     * 创建错误的JSON响应
     */
    private String createErrorJsonResponse(String message) {
        return String.format("{\"code\":500,\"success\":false,\"message\":\"%s\"}",
                message.replace("\"", "\\\""));
    }

    /**
     * 对帖子ID列表进行分页处理
     */
    private List<String> getPagedPostIds(List<String> allPostIds, int offset, int limit) {
        if (CollectionUtils.isEmpty(allPostIds)) {
            return Collections.emptyList();
        }

        int start = Math.min(offset, allPostIds.size());
        int end = Math.min(offset + limit, allPostIds.size());

        if (start >= end) {
            return Collections.emptyList();
        }

        return allPostIds.subList(start, end);
    }

    /**
     * 从API获取文章详情
     */
    private List<ArticleElement> getArticlesFromAPI(List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }

        try {
            String postIdsCsv = String.join(",", postIds);
            PostDetailWithAuthorResponse response = communityApiFeignClient.getPostDetailWithAuthor(postIdsCsv);

            if (response == null || response.getCode() == null || response.getCode() != 200
                    || CollectionUtils.isEmpty(response.getData())) {
                log.warn("API返回空数据或错误，postIds: {}", postIds);
                return Collections.emptyList();
            }

            List<ArticleElement> convertedArticles = CommunityDataConverter.convertFromOfficeNewsRecords(response.getData(), routerService);

            log.info("成功获取并转换{}个文章详情", convertedArticles.size());
            return convertedArticles;

        } catch (Exception e) {
            log.error("从API获取文章详情异常，postIds: {}", postIds, e);
            return Collections.emptyList();
        }
    }

    // 已移除自定义转换方法，使用 CommunityDataConverter.convertFromOfficeNewsRecords

    /**
     * 对帖子ID列表应用filterIds过滤
     */
    private List<String> applyFilterIdsToPostIds(List<String> postIds, String filterIds) {
        if (CollectionUtils.isEmpty(postIds) || StringUtils.isBlank(filterIds)) {
            return postIds;
        }

        Set<String> filterIdSet = Arrays.stream(filterIds.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        if (filterIdSet.isEmpty()) {
            return postIds;
        }

        return postIds.stream()
                .filter(postId -> !filterIdSet.contains(postId))
                .collect(Collectors.toList());
    }

    /**
     * 应用filterIds过滤文章
     */
    private List<ArticleElement> applyFilterIdsToArticles(List<ArticleElement> articles, String filterIds) {
        if (CollectionUtils.isEmpty(articles) || StringUtils.isBlank(filterIds)) {
            return articles;
        }

        Set<String> filterIdSet = Arrays.stream(filterIds.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        if (filterIdSet.isEmpty()) {
            return articles;
        }

        return articles.stream()
                .filter(article -> !filterIdSet.contains(article.getId()))
                .collect(Collectors.toList());
    }


    /**
     * 从文章列表构建JSON响应
     */
    private String buildJsonResponseFromArticles(List<ArticleElement> articles, int offset, int limit, boolean hasMore) {

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{\"code\":0,\"success\":true,\"data\":{");
        jsonBuilder.append("\"offset\":").append(offset).append(",");
        jsonBuilder.append("\"hasMore\":").append(hasMore).append(",");
        jsonBuilder.append("\"records\":[");

        for (int i = 0; i < articles.size(); i++) {
            if (i > 0) {
                jsonBuilder.append(",");
            }
            jsonBuilder.append(JsonUtil.toJSONString(articles.get(i)));
        }

        jsonBuilder.append("]}}");

        return jsonBuilder.toString();
    }
}