package com.xiaomi.micar.site.route;

import com.xiaomi.micar.site.model.PageConfigModel;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public final class RouteCodeUtil {
    private RouteCodeUtil() {}

    /**
     * 生成退化链：car_owner|su7 -> [car_owner|su7, car_owner, all]
     */
    public static List<String> degradeChain(String routeCode) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isBlank(routeCode)) {
            list.add("all");
            return list;
        }

        String rc = routeCode;
        // 从最细到最粗，依次去掉末尾一个维度
        while (StringUtils.isNotBlank(rc)) {
            if (!list.contains(rc)) {
                list.add(rc);
            }
            int p = rc.lastIndexOf('|');
            if (p < 0) {
                break;
            }
            rc = rc.substring(0, p);
        }
        if (!list.contains("all")) {
            list.add("all");
        }
        return list;
    }

    /**
     * 在页面 groupConfig 中按退化链寻找最近可用分组
     */
    public static Optional<PageConfigModel.GroupConfigModel> matchGroup(PageConfigModel pageConfig, String routeCode) {
        if (pageConfig == null || pageConfig.getGroupConfig() == null) {
            return Optional.empty();
        }
        List<String> cands = degradeChain(routeCode);
        for (String cand : cands) {
            Optional<PageConfigModel.GroupConfigModel> hit = pageConfig.getGroupConfig().stream()
                    .filter(g -> StringUtils.equals(cand, g.getStrategy()))
                    .findFirst();
            if (hit.isPresent()) {
                return hit;
            }
        }
        return Optional.empty();
    }
}
