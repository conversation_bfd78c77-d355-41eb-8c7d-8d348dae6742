package com.xiaomi.micar.site.service.assembly;

import com.google.common.collect.Lists;
import com.xiaomi.micar.site.component.ComponentConf;
import com.xiaomi.micar.site.constants.SiteComponentConstants;
import com.xiaomi.micar.site.model.ExperimentResult;
import com.xiaomi.micar.site.model.PageConfigModel;
import com.xiaomi.micar.site.model.PageRespV2;
import com.xiaomi.micar.site.model.ResponseContext;
import com.xiaomi.micar.site.service.experiment.ExperimentHelper;
import lombok.Getter;
import com.xiaomi.micar.site.route.RouteCodeUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 页面响应构建器 - 简化版
 * 职责：构建页面响应对象
 */
@Slf4j
@Component
public class PageResponseBuilder {

    @Getter
    private final ExperimentHelper experimentHelper;

    @Resource
    @Setter
    private ComponentProcessor componentProcessor;
    

    public PageResponseBuilder() {
        this.experimentHelper = new ExperimentHelper();
    }

    /**
     * 构建完整的页面响应（兼容性方法）
     * 为实时生成页面响应提供兼容性，创建新的去重标记
     */
    public PageRespV2 buildPageResponse(
            PageConfigModel pageConfig,
            String pageId,
            String strategy,
            String userId,
            Map<String, Object> queryParams,
            ExperimentResult experimentResult,
            boolean isWhiteUser) {

        // 为实时生成创建新的去重标记（每次调用都是独立的）
        Set<String> processedComponents = new HashSet<>();

        return buildPageResponse(
                pageConfig,
                pageId,
                strategy,
                userId,
                queryParams,
                experimentResult,
                isWhiteUser,
                processedComponents
        );
    }

    /**
     * 构建完整的页面响应（带组件去重标记）
     */
    public PageRespV2 buildPageResponse(
            PageConfigModel pageConfig,
            String pageId,
            String strategy,
            String userId,
            Map<String, Object> queryParams,
            ExperimentResult experimentResult,
            boolean isWhiteUser,
            Set<String> processedComponents) {

        PageRespV2 pageResp = new PageRespV2();

        // 1. 构建基本信息
        buildBasicInfo(pageResp, pageConfig, pageId, strategy);

        // 2. 应用用户组配置
        filterAndApplyGroupComponents(pageConfig, strategy, pageResp);

        // 3. 应用实验配置
        experimentHelper.applyExperimentCombination(pageConfig, experimentResult, pageResp);

        // 4. 处理组件数据
        ResponseContext context = new ResponseContext.Builder()
                .pageId(pageId)
                .groupId(strategy)
                .userId(userId)
                .queryParams(queryParams)
                .pageConfig(pageConfig)
                .isWhiteUser(isWhiteUser)
                .applicableExperiments(experimentResult.getApplicableExperiments())
                .processedComponents(processedComponents)  // 传递页面级别的去重标记
                .build();

        componentProcessor.processModules(context, pageResp);

        // 5. 设置实验分桶结果
        buildExperimentBuckets(pageResp, experimentResult);

        return pageResp;
    }

    /**
     * 构建页面基本信息
     */
    private void buildBasicInfo(PageRespV2 pageResp, PageConfigModel pageConfig, String pageId, String strategy) {
        PageRespV2.PageInfo pageInfo = new PageRespV2.PageInfo();

        // 优先使用 pageName 作为 title，如果没有则使用 title
        if (pageConfig.getPageName() != null) {
            pageInfo.setTitle(pageConfig.getPageName());
        } else if (pageConfig.getTitle() != null) {
            pageInfo.setTitle(pageConfig.getTitle());
        }

        pageInfo.setGroupKey(strategy);
        pageInfo.setCacheMaxAge(604800);
        pageResp.setPage(pageInfo);
        pageResp.setPageId(pageId);
    }

    /**
     * 构建实验分桶结果
     */
    private void buildExperimentBuckets(PageRespV2 pageResp, ExperimentResult experimentResult) {
        if (experimentResult != null) {
            List<String> expIds = Optional.ofNullable(experimentResult.getApplicableExperiments())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .sorted(Comparator.comparing(
                            (Map<String, String> m) -> m.get(SiteComponentConstants.Experiment.EXPERIMENT_ID)))
                    .map(m -> String.join(
                            SiteComponentConstants.CombinationKey.BUCKET_DELIMITER,
                            m.get(SiteComponentConstants.Experiment.EXPERIMENT_ID),
                            m.get(SiteComponentConstants.Bucket.BUCKET_ID)
                    )).collect(Collectors.toList());
            pageResp.setExpId(expIds);
        }
    }

    /**
     * 根据用户组筛选组件并应用到页面响应
     * 兼容无分人群策略的页面配置
     */
    private void filterAndApplyGroupComponents(PageConfigModel pageConfig, String strategy, PageRespV2 pageResp) {
        if (StringUtils.isBlank(strategy)) {
            return;
        }

        // 如果没有 groupConfig，说明是无分人群策略的页面，直接使用所有组件
        if (pageConfig.getGroupConfig() == null || pageConfig.getGroupConfig().isEmpty()) {
            log.debug("页面无分人群策略配置，使用所有组件: pageId={}", pageConfig.getPageId());
            addAllComponentsToResponse(pageConfig, pageResp);
            return;
        }

        // 有 groupConfig 的情况，先尝试精确命中（预加载场景只需要精确命中即可）
        Optional<PageConfigModel.GroupConfigModel> exact = pageConfig.getGroupConfig().stream()
                .filter(g -> strategy.equals(g.getStrategy()))
                .findFirst();
        if (exact.isPresent() && exact.get().getComponents() != null) {
            addComponentsByIds(pageConfig, pageResp, exact.get().getComponents());
            return;
        }

        // 精确未命中：直接使用全量组件兜底（退化逻辑仅在路由读取过程中执行）
        log.debug("未找到精确分组，使用全量组件兜底: pageId={}, routeCode={}", pageConfig.getPageId(), strategy);
        addAllComponentsToResponse(pageConfig, pageResp);
    }

    /**
     * 添加所有组件到响应中（无分人群策略时使用）
     */
    private void addAllComponentsToResponse(PageConfigModel pageConfig, PageRespV2 pageResp) {
        if (pageConfig.getComponents() == null || pageConfig.getComponents().isEmpty()) {
            return;
        }

        List<ComponentConf> modules = new ArrayList<>();
        for (PageConfigModel.ComponentModel component : pageConfig.getComponents()) {
            ComponentConf componentConf = new ComponentConf();
            componentConf.setId(component.getId());
            componentConf.setName(component.getName());
            componentConf.setConfig(component.getComponentInfo());
            componentConf.setTemplate(component.getTemplate());
            modules.add(componentConf);
        }
        pageResp.setModules(modules);
        log.debug("添加了 {} 个组件到页面响应", modules.size());
    }

    /**
     * 根据组件ID列表添加组件到响应中（有分人群策略时使用）
     */
    private void addComponentsByIds(PageConfigModel pageConfig, PageRespV2 pageResp, List<String> componentIds) {
        Map<String, PageConfigModel.ComponentModel> componentModelMap = pageConfig.getComponents()
                .stream()
                .collect(Collectors.toMap(
                        PageConfigModel.ComponentModel::getId,
                        s -> s));

        List<ComponentConf> modules = new ArrayList<>();
        for (String componentId : componentIds) {
            if (componentModelMap.containsKey(componentId)) {
                PageConfigModel.ComponentModel component = componentModelMap.get(componentId);
                ComponentConf componentConf = new ComponentConf();
                componentConf.setId(component.getId());
                componentConf.setName(component.getName());
                componentConf.setConfig(component.getComponentInfo());
                componentConf.setTemplate(component.getTemplate());
                componentConf.setTitleTag(component.getTitleTag());
                modules.add(componentConf);
            }
        }
        pageResp.setModules(modules);
        log.debug("根据用户组策略添加了 {} 个组件到页面响应", modules.size());
    }

    /**
     * 转换图片信息到响应模型
     */
    private PageRespV2.ImageInfo convertToRespImageInfo(PageConfigModel.ImageInfo imageInfo) {
        if (imageInfo == null) {
            return null;
        }

        PageRespV2.ImageInfo result = new PageRespV2.ImageInfo();
        result.setSrc(imageInfo.getSrc());
        result.setWidth(imageInfo.getWidth());
        result.setHeight(imageInfo.getHeight());
        return result;
    }

}
