package com.xiaomi.micar.site.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * 帖子操作响应
 * 用于点赞、取消点赞等操作的响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PostActionResponse {
    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * HTTP状态码 - 成功
     */
    private static final int HTTP_STATUS_OK = HttpStatus.OK.value();

    /**
     * 创建成功响应
     *
     * @return 成功响应
     */
    public static PostActionResponse success() {
        return PostActionResponse.builder()
                .code(HTTP_STATUS_OK)
                .message("操作成功")
                .success(true)
                .build();
    }

    /**
     * 创建失败响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @return 失败响应
     */
    public static PostActionResponse fail(Integer code, String message) {
        return PostActionResponse.builder()
                .code(code)
                .message(message)
                .success(false)
                .build();
    }
}
