package com.xiaomi.micar.site.model;

import lombok.Builder;
import lombok.Data;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * 组件数据请求类
 * 用于封装获取组件数据所需的参数
 */
@Data
@Builder
public class ComponentDataRequest {
    
    /**
     * 组件类型
     */
    private String componentType;
    
    /**
     * 标签ID (如TabFeeds组件中的tabId)
     */
    private String tabId;
    
    /**
     * 分页开始位置
     */
    private Integer offset;
    
    /**
     * 每页记录数量
     */
    private Integer limit;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 附加参数
     */
    private Map<String, Object> extraParams;

    /**
     * 上一刷所有帖子id，去重使用
     */
    private Set<String> filterIds;
    
    /**
     * 获取附加参数，确保不为空
     * 
     * @return 非空的附加参数Map，如果原始参数为null则返回空Map
     */
    public Map<String, Object> getExtraParams() {
        return extraParams != null ? extraParams : Collections.emptyMap();
    }
}
