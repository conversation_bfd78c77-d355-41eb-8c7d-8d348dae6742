<?xml version="1.0" encoding="UTF-8"?>
<configuration status="WARN" monitorInterval="30">
    <appenders>
        <RollingRandomAccessFile name="IcccSiteEventLog"
                                 fileName="${loggFilePath}/logs/${serviceName}/micar-site-event.log"
                                 filePattern="${loggFilePath}/logs/${serviceName}/micar-site-event.log.%d{yyyy-MM-dd}.%i"
                                 immediateFlush="true">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="${loggerRolloverMax}">
                <Delete basePath="${loggFilePath}/logs/${serviceName}">
                    <IfFileName glob="micar-site-event.log.*"/>
                    <IfLastModified age="${loggerRolloverAge}"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </appenders>

    <loggers>
        <Logger name="IcccSiteEventLogger" additivity="false" level="INFO">
            <AppenderRef ref="IcccSiteEventLog"/>
        </Logger>
    </loggers>

</configuration>
