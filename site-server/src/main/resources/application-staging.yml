spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password@kc-sid: iccc-config-decrypt
    password: GDAGSxp0NKAMNA9opEmXiN57osW_Cw6jtMLosXpGqNyDLdpR68joozqxqs8NlgpZ7osYEkxdJiNUjU75uMlCof9n1HBS_xgQyO3UYghWRaeVndcdvktuOBgUvwRcbw15twjbJcN3sItGTW2S1-0A
    url: ******************************************************************************************************************************
    username: micar_site_sv22_wn
  # 座舱通用redis
  redis:
    host: wcc.cache01.test.b2c.srv
    port: 22122
    password@kc-sid: iccc-config-decrypt
    password: GDA0KVOkNnRpWWWXU/SwvICafkaNk64/UVPDp5NuSeJBLONZ1TIe4Z+9JGjndmiiRdcYEpzjCFcVy0s4l/JAR+LwcDGk/xgQMgoMEIT0QCqrgxEt0cv6gxgUlG8oZNye3l/l6QN4koXJiu7T4wUA
# dubbo 注册统一配置在starter
dubbo:
  consumer:
    activity-site: staging
    profCert: staging
nacos:
  config:
    addr: mione-staging-nacos.api.xiaomi.net:80
    namespace: staging
  registry:
    addr: mione-staging-nacos.api.xiaomi.net:80
  biz:
    group: DEFAULT_GROUP
    site_config: site_config.json

iccc:
  site:
    api:
      base-url: internal-staging.car.miui.srv
    router:
      community:
        domain: https://web-staging.community.car.miui.com
logging:
  include:
    location: true
  level:
    root: INFO
    # DEBUG可以看到请求/响应头和元数据，TRACE可以看到完整请求/响应体
    com.mi.car.iccc.starter.site.service.http.CommunityApiFeignClient: DEBUG
feign:
  client:
    config:
      # 'default' 应用于所有 Feign 客户端, 或使用客户端名称 'CommunityApiService'
      CommunityApiService: # 或者 default:
        loggerLevel: full # 可选值: none, basic, headers, full

site:
  cache:
    nacos:
      batch-notification-data-id: site_cache_batch_notifications_staging
      historical-snapshot-data-id: rn_version_config_staging
    policies:
      defaultPolicy: MYSQL_BROADCAST_MEMORY
      rules:
        - pageId: explore
          strategy: car_owner|su7
          policy: REDIS_ONLY
        - pageId: explore
          strategy: car_owner
          policy: MYSQL_BROADCAST_MEMORY
        - pageId: explore
          strategy: all
          policy: MYSQL_BROADCAST_MEMORY
  up:
    strategy: none
    local:
      jdbc:
        url: *******************************************************************************************************************
        username: workspace_12987_user
        password: GCBHpwqD/3RVN20EINjCXYUxbGwG8op1BU0NH+prkKGhwhgSXu1ZIJAUS5qMZBV07bhKYgb/GBAdBApQH65IYYw1GI0vInddGBSteCM1onK7GyOszDO9FAaMPSCf7AA=
        password@kc-sid: iccc-config-decrypt
        driver-class-name: com.mysql.cj.jdbc.Driver
      enable-bitmap: true
      nacos-data-id: "site_up_local.json"
      query-data: "select mid, owner, sharer, locked from dws_site_up_di where date=? and mid > ? order by mid asc limit ? "
      query-version: "select max(date) as dt from dws_site_up_di"

  route:
    degrade:
      # 维度顺序：主维度在前（用于嵌套退化顺序）
      dimension_order: [UserStrategyEnum, CarModelEnum]
      # 同层优先序（key 必须与 dimension_order 中的维度名一致）
      order:
        UserStrategyEnum:
          order: [car_owner, car_share, all]
        CarModelEnum:
          # 仅用于排序；最终只会保留 RouteContext 中存在的取值
          order: [yu7, su7Ultra, su7]
      # 跨维关系：声明某个主维度具体取值下，允许参与组合的下层维度
      relations:
        UserStrategyEnum:
          car_owner:
            enable_dims: [CarModelEnum]
          car_share:
            enable_dims: [CarModelEnum]
          all:
            enable_dims: []   # all 不携带车型
      fallback:
        beam: 200
        max_candidates: 10
