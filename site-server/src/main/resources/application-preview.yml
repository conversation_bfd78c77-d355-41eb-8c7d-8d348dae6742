spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password@kc-sid: iccc-config-decrypt
    password: GDD/P4BNEksumNR2Ux1khDthlOv5Um+FcbUOZsJTfbQWx0lJqhD69eHRW9y1cjbQfXYYEoMGTt63s02AiA0AdgPPqin+ARgQ3mh7jY4tTWqUSphP8Mp8xBgUUjmOFBH6nKKaZk+IR0sbygXpGmEA
    url: ******************************************************************************************************************************
    username: micar_site_sv22_wn
  redis:
    host: ares.car.cache.b2c.srv
    port: 5105
    password@kc-sid: iccc-config-decrypt
    password: GDChON/3JNFs+mH+MLQW/rRoFwUhbZW4rGEcfQ4jKtG8gmMCBCmjbpDsWV82QOWDgjAYEtD/BSNb5Eegn82KkUSXkn7OARgQBxjPmtUjSwygR6quH3Bz7xgUOp5QQLiFW4kEV0dBCQ6Ss4ijJY4A
# dubbo 注册统一配置在starter
dubbo:
  consumer:
    activity-site: preview
    profCert: preview
#nacos
nacos:
  config:
    addr: mione-config-nacos.api.xiaomi.net:80
    namespace: preview
  registry:
    addr: mione-nacos.api.xiaomi.net:80
  biz:
    group: DEFAULT_GROUP
    site_config: site_config_preview.json

#mybatis
mybatis-plus:
  global-config:
    db-config:
      table-prefix: preview_

iccc:
  site:
    component:
    router:
      community:
        domain: https://web-preview.community.car.miui.com
    api:
      base-url: internal-preview.car.miui.srv
feign:
  client:
    config:
      # 'default' 应用于所有 Feign 客户端, 或使用客户端名称 'CommunityApiService'
      CommunityApiService: # 或者 default:
        loggerLevel: full # 可选值: none, basic, headers, full

site:
  cache:
    nacos:
      batch-notification-data-id: site_cache_batch_notifications_preview
      historical-snapshot-data-id: rn_version_config_preview
  up:
    strategy: composite
    local:
      jdbc:
        url: ******************************************************************************************************************
        username: workspace_12112_user
        password: GCC6NISB/gx9Bz78zUxYfzTR/a9KzHqrBzo1i/X37j9fexgS4XvW4ZrNR1+EaOGwJ5WFQDABGBDwLn3vbGpK35Wx7TWZzdGOGBRpq2+NBo7KqaRtqjjrkS0o5G0ABAA=
        password@kc-sid: iccc-config-decrypt
        driver-class-name: com.mysql.cj.jdbc.Driver
      enable-bitmap: true
      nacos-data-id: "site_up_local_preview.json"
      query-data: "select mid, owner, sharer, locked from dwd_micar_site_up_wide_df where date=? and mid > ? order by mid asc limit ? "
      query-version: "select max(date) as dt from dwd_micar_site_up_wide_df"
