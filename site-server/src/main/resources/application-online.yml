spring:  
  application:
    name: micar-site
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password@kc-sid: iccc-config-decrypt-prod
    password: GDCsPi0OrFqRg0uDta6GEWswsF1qTREbEnLdNEZZQ3gsIqaxMkMVWSERukX9zM-bh3EYEn67EkhlOUFhi6IEi4nPGa7AARgQGkZzkmqTQm-L87MTxlsFPhgUTM7lyQlwO5X0JR4dzsQGkDv6_fYA
    url: ***********************************************************************************************************************************************
    username: micar_site_ov79_wn
  redis:
    host: ares.cn-car-micar-vip-micar-site-online.cache.srv
    port: 5105
    password@kc-sid: iccc-config-decrypt-prod
    password: GDD9nyqVBNqKpiEcmu4s4Fh9ljzUni9O0wBIfWAjFuxQwoBPE+c2ELVYhM1Ql7XM0FQYEoxFC7tRsEgLgZMSyA+qgSXPARgQ0MOrqtDCSF6wON5ogEmychgU1N8ljPQDkyy2jd0haD1I2r8Q+GsA
  cloud:
    sentinel:
      log:
        dir: /home/<USER>/logs/micar-site/sentinel-client
      transport:
        dashboard: https://sentinel.be.mi.com
      eager: false
      cluster_enable: false
      nacos-server-addr: ${nacos.config.addr}
      datasource:
        ds1:
          nacos:
            server-addr: ${nacos.config.addr}
            dataId: micar-site-flow-rules
            groupId: DEFAULT_GROUP
            rule-type: FLOW
        ds2:
          nacos:
            server-addr: ${nacos.config.addr}
            dataId: micar-site-degrade-rules
            groupId: DEFAULT_GROUP
            rule-type: DEGRADE

# dubbo 注册统一配置在starter
dubbo:
  consumer:
    activity-site: prod
    profCert: online
#nacos
nacos:
  config:
    addr: mione-config-nacos.api.xiaomi.net:80
    namespace: c3
  registry:
    addr: mione-nacos.api.xiaomi.net:80
  biz:
    group: DEFAULT_GROUP
    site_config: site_config_online.json
iccc:
  site:
    router:
      community:
        domain: https://web.community.car.miui.com
    api:
      base-url: internal.car.miui.srv

site:
  cache:
    nacos:
      batch-notification-data-id: site_cache_batch_notifications_online
      historical-snapshot-data-id: rn_version_config_online
  up:
    strategy: composite
    local:
      jdbc:
        url: ******************************************************************************************************************
        username: workspace_12112_user
        password: GCBXWrc+MtU6G6bNvkUu5aC9ruViZIkwUKPpd5icDIrbLxgSoEL/flbqRTC2mwm+EsBkn/YBGBA/+H3ovblK74X6EfUI25wEGBRYxgYL0DkSc9D7jStAfROvXsJ04AA=
        password@kc-sid: iccc-config-decrypt-prod
        driver-class-name: com.mysql.cj.jdbc.Driver
      enable-bitmap: true
      nacos-data-id: "site_up_local_online.json"
      query-data: "select mid, owner, sharer, locked from dwd_micar_site_up_wide_df where date=? and mid > ? order by mid asc limit ? "
      query-version: "select max(date) as dt from dwd_micar_site_up_wide_df"
