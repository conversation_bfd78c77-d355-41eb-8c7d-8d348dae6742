package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.component.NewsComponent;
import com.xiaomi.micar.site.model.ResponseContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;

/**
 * NewsProvider 简单测试类
 * 用于调试 processNews 方法
 */
@Slf4j
@SpringBootTest
public class SimpleNewsProviderTest {

    @Resource
    private NewsProvider newsProvider;

    @Test
    public void testProcessNews() {
        // 创建测试组件
        NewsComponent newsComponent = new NewsComponent();
        newsComponent.setTitle("测试新闻组件");
        
        // 创建响应上下文
        ResponseContext responseContext = new ResponseContext();
        responseContext.setUserId("test_user_123");
        
        // 设置测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("type", "postId");
        params.put("ids", Arrays.asList("post1", "post2"));
        params.put("limit", 10);
        responseContext.setQueryParams(params);
        
        // 调用方法进行测试 - 可以在这里打断点调试
        log.info("开始测试 NewsProvider#processNews");
        newsProvider.doProcess(responseContext, newsComponent);
        log.info("测试完成，返回数据条数: {}", 
                newsComponent.getData() != null ? newsComponent.getData().size() : 0);
    }
}
