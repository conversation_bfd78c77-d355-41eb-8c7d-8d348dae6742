package com.xiaomi.micar.site.service.provider;

import com.xiaomi.micar.site.model.PostDetailWithAuthorResponse;
import com.xiaomi.micar.site.model.OfficeNewsResponse;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * NewsProvider 新接口测试
 * 测试使用 detailWithAuthor 接口获取帖子详情
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("staging")
public class NewsProviderNewApiTest {

    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    @Test
    public void testNewApiDirectCall() {
        // 测试直接调用新接口
        String postIds = "330381417689141922,330380826652985639";
        
        try {
            PostDetailWithAuthorResponse response = communityApiFeignClient.getPostDetailWithAuthor(postIds);
            log.info("新接口响应: code={}, message={}, dataSize={}", 
                    response.getCode(), response.getMessage(), 
                    response.getData() != null ? response.getData().size() : 0);
            
            if (response.getData() != null) {
                for (OfficeNewsResponse.OfficeNewsRecord record : response.getData()) {
                    log.info("帖子详情: postId={}, title={}, author={}, viewCount={}", 
                            record.getPostId(), record.getTitle(), 
                            record.getAuthor() != null ? record.getAuthor().getUserName() : "null",
                            record.getViewCount());
                    
                    // 检查新字段
                    if (record.getAuthor() != null) {
                        log.info("作者身份标识数量: {}", 
                                record.getAuthor().getIdentityList() != null ? 
                                record.getAuthor().getIdentityList().size() : 0);
                        log.info("头像框: {}", 
                                record.getAuthor().getHeaderFrame() != null ? 
                                record.getAuthor().getHeaderFrame().getFrameUrl() : "null");
                    }
                    
                    log.info("话题数量: {}", 
                            record.getTopicList() != null ? record.getTopicList().size() : 0);
                }
            }
        } catch (Exception e) {
            log.error("调用新接口失败", e);
        }
    }
}
