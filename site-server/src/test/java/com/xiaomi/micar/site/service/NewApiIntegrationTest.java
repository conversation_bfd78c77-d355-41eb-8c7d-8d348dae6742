package com.xiaomi.micar.site.service;

import com.xiaomi.micar.site.component.element.ArticleElement;
import com.xiaomi.micar.site.model.PostDetailWithAuthorResponse;
import com.xiaomi.micar.site.model.OfficeNewsResponse;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import com.xiaomi.micar.site.convert.CommunityDataConverter;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 新接口集成测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("staging")
public class NewApiIntegrationTest {

    @Resource
    private CommunityApiFeignClient communityApiFeignClient;

    @Test
    public void testDetailWithAuthorApi() {
        // 测试新接口 detailWithAuthor
        String postIds = "330381417689141922,330380826652985639";
        
        try {
            log.info("开始测试新接口 detailWithAuthor，postIds: {}", postIds);
            
            PostDetailWithAuthorResponse response = communityApiFeignClient.getPostDetailWithAuthor(postIds);
            
            log.info("新接口响应: code={}, message={}, dataSize={}", 
                    response.getCode(), response.getMessage(), 
                    response.getData() != null ? response.getData().size() : 0);
            
            if (response.getData() != null && !response.getData().isEmpty()) {
                for (OfficeNewsResponse.OfficeNewsRecord record : response.getData()) {
                    log.info("=== 帖子详情 ===");
                    log.info("postId: {}", record.getPostId());
                    log.info("title: {}", record.getTitle());
                    log.info("displaySummary: {}", record.getDisplaySummary());
                    log.info("viewCount: {}", record.getViewCount());
                    
                    // 检查作者信息
                    if (record.getAuthor() != null) {
                        log.info("作者: {}", record.getAuthor().getUserName());
                        log.info("作者头像: {}", record.getAuthor().getIcon());
                        log.info("是否员工: {}", record.getAuthor().getIsEmployee());
                        
                        // 检查新字段：身份标识
                        if (record.getAuthor().getIdentityList() != null) {
                            log.info("身份标识数量: {}", record.getAuthor().getIdentityList().size());
                            for (OfficeNewsResponse.IdentityItem identity : record.getAuthor().getIdentityList()) {
                                log.info("  - 车型: {}, 状态: {}", identity.getCarModel(), identity.getStatus());
                            }
                        }
                        
                        // 检查新字段：头像框
                        if (record.getAuthor().getHeaderFrame() != null) {
                            log.info("头像框: {}", record.getAuthor().getHeaderFrame().getFrameUrl());
                        }
                    }
                    
                    // 检查新字段：话题列表
                    if (record.getTopicList() != null) {
                        log.info("话题数量: {}", record.getTopicList().size());
                        for (OfficeNewsResponse.TopicItem topic : record.getTopicList()) {
                            log.info("  - 话题: {} ({})", topic.getTopicName(), topic.getTopicId());
                        }
                    }
                    
                    log.info("==================");
                }
                
                log.info("✅ 新接口测试成功！获取到{}个帖子详情，包含丰富的作者和话题信息", response.getData().size());
            } else {
                log.warn("❌ 新接口返回数据为空");
            }
        } catch (Exception e) {
            log.error("❌ 新接口测试失败", e);
        }
    }

    @Test
    public void testDataConversion() {
        // 测试数据转换功能
        String postIds = "330381417689141922,330380826652985639";

        try {
            log.info("开始测试数据转换功能");

            PostDetailWithAuthorResponse response = communityApiFeignClient.getPostDetailWithAuthor(postIds);

            if (response != null && response.getData() != null && !response.getData().isEmpty()) {
                // 转换为 ArticleElement
                List<ArticleElement> articles = CommunityDataConverter.convertFromOfficeNewsRecords(response.getData());

                log.info("✅ 成功转换{}个 OfficeNewsRecord 为 ArticleElement", articles.size());

                for (int i = 0; i < articles.size(); i++) {
                    ArticleElement article = articles.get(i);
                    OfficeNewsResponse.OfficeNewsRecord record = response.getData().get(i);

                    log.info("=== 转换结果对比 {} ===", i + 1);
                    log.info("原始数据:");
                    log.info("  - postId: {}", record.getPostId());
                    log.info("  - displaySummary: {}", record.getDisplaySummary());
                    log.info("  - title: {}", record.getTitle());
                    log.info("  - viewCount: {}", record.getViewCount());
                    log.info("  - likeCnt: {}", record.getLikeCnt());
                    log.info("  - commentCnt: {}", record.getCommentCnt());

                    log.info("转换后数据:");
                    log.info("  - id: {}", article.getId());
                    log.info("  - title: {}", article.getTitle());
                    log.info("  - summary: {}", article.getSummary());
                    log.info("  - viewCount: {}", article.getViewCount());
                    log.info("  - author: {}", article.getPublishInfo() != null ? article.getPublishInfo().getAuthor() : "null");
                    log.info("  - publishTime: {}", article.getPublishInfo() != null ? article.getPublishInfo().getTime() : "null");

                    // 验证关键字段
                    assert article.getId().equals(record.getPostId()) : "postId 转换错误";
                    assert article.getTitle().equals(record.getDisplaySummary()) : "title 应该使用 displaySummary";
                    assert article.getSummary().equals(record.getDisplaySummary()) : "summary 应该使用 displaySummary";

                    log.info("✅ 数据转换验证通过");
                }

                log.info("🎉 所有数据转换测试通过！新接口的 displaySummary、viewCount 等字段都正确处理了");
            } else {
                log.warn("❌ 没有获取到测试数据");
            }
        } catch (Exception e) {
            log.error("❌ 数据转换测试失败", e);
        }
    }
}
