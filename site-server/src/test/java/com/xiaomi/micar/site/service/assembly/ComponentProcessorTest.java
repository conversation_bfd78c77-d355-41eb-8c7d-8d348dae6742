package com.xiaomi.micar.site.service.assembly;

import com.mi.car.iccc.iccccommonutil.util.JsonUtil;
import com.xiaomi.micar.site.component.BannerComponent;
import com.xiaomi.micar.site.component.Component;
import com.xiaomi.micar.site.model.PostDetailResponse;
import com.xiaomi.micar.site.service.remote.CommunityApiFeignClient;
import com.xiaomi.micar.site.service.http.RouterService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.BeanFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class ComponentProcessorTest {

    private ComponentProcessor processor;

    @BeforeEach
    public void setup() {
        BeanFactory beanFactory = mock(BeanFactory.class);
        given(beanFactory.getBean(RouterService.class)).willReturn(new RouterService());

        CommunityApiFeignClient apiFeignClient = mock(CommunityApiFeignClient.class);

        doAnswer(invocation -> {
            String postIds = invocation.getArgument(0);
            String resp = getRequest("http://internal-staging.car.miui.srv/api/internal/site/post/v1/detail?postIds=" + postIds);
            return JsonUtil.parseObject(resp, PostDetailResponse.class);
        }).when(apiFeignClient).getPostDetailList(anyString());

        given(beanFactory.getBean(CommunityApiFeignClient.class)).willReturn(apiFeignClient);

        processor = new ComponentProcessor(beanFactory);
    }


    @Test
    public void testProcessFieldsRecursively() throws Exception {

        String str = "{\"data\": [{\"id\": \"81b427d06648437d87c3ea4f2b61ceca\", \"name\": \"banner5\", \"type\": \"image\", \"image\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/9acb4f005bb0788cbaaa78c4eb1374e4-file1751622167229.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224627653&Signature=ynEmAav4gh0bBY0i+Ng47vECNhQ=\"}, \"refId\": \"https://web-staging.community.car.miui.com/detail?postId=318785106686779091\", \"title\": \"banner5\", \"video\": {}, \"_track\": {\"itemName\": \"5\"}, \"action\": {\"needLogin\": 0}, \"refType\": \"h5\", \"subTitle\": \"banner5\", \"imageFold\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/6a1b9ef668b66d365e000c5f8e7a61fc-file1751622170624.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224630952&Signature=5mDcML9szv0MowMlf9UDrZ3eLX4=\"}, \"videoFold\": {}}, {\"id\": \"9f1fd906b2144662ad196f4974d57141\", \"name\": \"banner4\", \"type\": \"image\", \"image\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/9acb4f005bb0788cbaaa78c4eb1374e4-file1751622116805.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224577209&Signature=Jht4235E6BpNHRe/mMpSvQRXzq0=\"}, \"refId\": \"https://www.xiaomiev.com/shop/home/<USER>", \"title\": \"banner4-rn\", \"video\": {}, \"_track\": {\"itemName\": \"4\"}, \"action\": {\"needLogin\": 0}, \"refType\": \"router\", \"subTitle\": \"rn\", \"imageFold\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/c2eb4c7f88ffdd1d57a2055c7d046871-file1751622121870.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224582243&Signature=eNCOvjllawUn+NyueVHEZwJ4VrY=\"}, \"videoFold\": {}}, {\"id\": \"514fdb479e644489aeae9d7c3b6013ae\", \"name\": \"banner3\", \"type\": \"image\", \"image\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/9acb4f005bb0788cbaaa78c4eb1374e4-file1751622065856.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224526268&Signature=QxX8JBQO+iEctmq3X6H1YeYdetw=\"}, \"refId\": \"https://web-staging.community.car.miui.com/detail?postId=318785106686779091\", \"title\": \"banner3-h5\", \"video\": {}, \"_track\": {\"itemName\": \"3\"}, \"action\": {\"needLogin\": 0}, \"refType\": \"h5\", \"subTitle\": \"图文\", \"imageFold\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/c2eb4c7f88ffdd1d57a2055c7d046871-file1751622069994.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224530393&Signature=HBldc5RtZFs9UuZTJchfVnT2H2o=\"}, \"videoFold\": {}}, {\"id\": \"26e902ef09094226a05c736511b007e1\", \"name\": \"banner2\", \"type\": \"image\", \"image\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/9acb4f005bb0788cbaaa78c4eb1374e4-file1751622018398.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224478848&Signature=uERw8KJD1W/iBUpZGbEYAYZ/U6U=\"}, \"refId\": \"https://web-staging.community.car.miui.com/videoDetail?postId=313345594959649193&darkMode=true\", \"title\": \"banner2\", \"video\": {}, \"_track\": {\"itemName\": \"2\"}, \"action\": {\"needLogin\": 0}, \"refType\": \"h5\", \"subTitle\": \"视频\", \"imageFold\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/c2eb4c7f88ffdd1d57a2055c7d046871-file1751622022638.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224482996&Signature=XgF2Qmju9c+8tpuhvBUqILchslQ=\"}, \"videoFold\": {}}, {\"id\": \"1ae0c24055484a6495d7a3b54d621545\", \"name\": \"banner1\", \"type\": \"image\", \"image\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/9acb4f005bb0788cbaaa78c4eb1374e4-file1751621936523.jpg?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224396969&Signature=bGe4LG7HkWXIoIdoz11XXXxdZV8=\"}, \"refId\": \"https://web-staging.community.car.miui.com/detail?postId=306372988079445913\", \"title\": \"banner1-h5\", \"video\": {}, \"_track\": {\"itemName\": \"banner\"}, \"action\": {\"needLogin\": 0}, \"refType\": \"h5\", \"subTitle\": \"文章\", \"imageFold\": {\"src\": \"https://staging-cnbj2-fds.api.xiaomi.net/remote-config/theme/20250704/d8bcae91c720874c881d2847c95f9411-file1751621942636.jfif?GalaxyAccessKeyId=AKS7T6LK4S47PWXHUX&Expires=1974224402912&Signature=1lqU2DMX+pS7869sXyyO2e4314E=\"}, \"videoFold\": {}}]}";
        Component component = JsonUtil.parseObject(str, BannerComponent.class);
        processor.processComponentLinks(component);
        System.out.println(JsonUtil.toJSONString(component));
    }


    private String getRequest(String url) throws Exception {
        System.out.println(url);

        // 打开连接
        HttpURLConnection connection = (HttpURLConnection) (new URL(url)).openConnection();

        // 设置请求方法为GET
        connection.setRequestMethod("GET");

        // 设置请求头（可选）
        connection.setRequestProperty("User-Agent", "Mozilla/5.0");

        // 获取响应码
        int responseCode = connection.getResponseCode();
        System.out.println("Response Code: " + responseCode);

        // 读取响应内容
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream()));
        String line;
        StringBuilder response = new StringBuilder();

        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();

        return response.toString();

    }

}