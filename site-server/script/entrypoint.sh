#!/bin/bash
echo "start server"

# 尝试从注入配置文件中获取ZONE
ZONE_NEW=$(if [ -f /matrix_env/config ]; then grep az_code /matrix_env/config | cut -d ' ' -f 2|cut -d '-' -f 3; fi);
# 优先从注入变量取，如果没有注册则直接取环境变量
ZONE=${ZONE_NEW:-${ZONE}};

# 检查ZONE是否设置
if [ -z "$ZONE" ]; then
    echo "Error: ZONE is not set or is empty."
    exit 1
fi

# 将ak映射为c4
if [ "$ZONE" = "ak" ]; then
    ZONE="c4"
fi

export ZONE="$ZONE"

if
  [ "${ACTIVE}" = "pre-prod" ]
then
  SPRING_PROFILE="${ACTIVE}-${ZONE}"
  export CUSTOM_SERVER_ENV="preprod"
else
  SPRING_PROFILE=${ACTIVE:-${ZONE}}
fi

if [ -z "$JAVA_OPTIONS" ]; then
  JAVA_OPTIONS="-server"
  JAVA_OPTIONS="$JAVA_OPTIONS $JVM_MEM_ARGS"
  JAVA_OPTIONS="$JAVA_OPTIONS -XX:+UseG1GC -XX:CICompilerCount=8 -XX:SurvivorRatio=4 -XX:G1HeapRegionSize=2m -XX:MaxGCPauseMillis=200"
  JAVA_OPTIONS="$JAVA_OPTIONS -verbose:gc -Xloggc:/home/<USER>/logs/app_gc_%p.log -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -XX:+PrintTenuringDistribution -XX:+PrintHeapAtGC -XX:+PrintReferenceGC"
  JAVA_OPTIONS="$JAVA_OPTIONS -XX:HeapDumpPath=/home/<USER>/logs/app_heap.hprof"
  JAVA_OPTIONS="$JAVA_OPTIONS -javaagent:/home/<USER>/app/mitelemetry/agent/opentelemetry-javaagent-all.jar"
  JAVA_OPTIONS="$JAVA_OPTIONS -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector"
  JAVA_OPTIONS="$JAVA_OPTIONS -Dtls.enable=true -Dtls.test.mode.enable=false"
  JAVA_OPTIONS="$JAVA_OPTIONS -Drocketmq.client.logRoot=/home/<USER>/logs"
  JAVA_OPTIONS="$JAVA_OPTIONS -Dspring.profiles.active=${SPRING_PROFILE}"
  JAVA_OPTIONS="$JAVA_OPTIONS -Xbootclasspath/a:/home/<USER>/app"
  JAVA_OPTIONS="$JAVA_OPTIONS -Dproject.name=iccc-push-admin"
fi

if
  [ "${SPRING_PROFILE}" != "c3" ] && [ "${SPRING_PROFILE}" != "c4" ]
then
  JAVA_OPTIONS="$JAVA_OPTIONS -XX:+HeapDumpBeforeFullGC -XX:+HeapDumpAfterFullGC"
fi

echo java $JAVA_OPTIONS -jar /home/<USER>/app/app.jar
exec java $JAVA_OPTIONS -jar /home/<USER>/app/app.jar
