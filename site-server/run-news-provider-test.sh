#!/bin/bash

# NewsProvider processNews 方法测试运行脚本

echo "🚀 开始运行 NewsProvider#processNews 测试..."
echo "================================================"

# 设置测试环境
export SPRING_PROFILES_ACTIVE=test

# 运行特定的测试类
mvn test -Dtest=NewsProviderProcessNewsTest

echo "================================================"
echo "✅ 测试运行完成！"
echo ""
echo "📊 测试报告位置："
echo "   - target/surefire-reports/TEST-com.xiaomi.micar.site.service.provider.NewsProviderProcessNewsTest.xml"
echo "   - target/surefire-reports/com.xiaomi.micar.site.service.provider.NewsProviderProcessNewsTest.txt"
echo ""
echo "🔍 如需查看详细日志，请检查控制台输出或日志文件"
